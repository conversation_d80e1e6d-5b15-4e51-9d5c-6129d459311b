/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/23
 */
package com.xmcares.platform.admin.common.datasource.mq.rabbitmq;

import com.rabbitmq.client.*;
import com.xmcares.platform.admin.common.datasource.mq.MessageHeaders;
import com.xmcares.platform.admin.common.datasource.mq.MqDataSource;
import com.xmcares.platform.admin.common.datasource.mq.TopicInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * RabbitMq数据源 客户端版本5.14.x
 * <AUTHOR>
 * @since 1.0.0
 */
public class RabbitMqDataSource implements MqDataSource {
    private static final Logger logger = LoggerFactory.getLogger(RabbitMqDataSource.class);

    private final String name;
    private final RabbitMqProperties props;
    private Connection connection;
    private Channel channel;

    // 用于监听线程
    private final Map<String, Thread> listenerThreads = new ConcurrentHashMap<>();
    private final Map<String, AtomicBoolean> listenerRunning = new ConcurrentHashMap<>();

    public RabbitMqDataSource(String name, RabbitMqProperties props) {
        this.name = name;
        this.props = props;
        initialize();
    }

    private void initialize() {
        try {
            ConnectionFactory factory = new ConnectionFactory();
            factory.setHost(props.getHost());
            factory.setPort(props.getPort());
            factory.setVirtualHost(props.getVirtualHost());
            factory.setUsername(props.getUsername());
            factory.setPassword(props.getPassword());

            this.connection = factory.newConnection();
            this.channel = connection.createChannel();
        } catch (Exception e) {
            logger.error("Failed to initialize RabbitMQ connection", e);
            throw new RuntimeException("RabbitMQ initialization error", e);
        }
    }

    @Override
    public void close() {
        try {
            for (Thread t : listenerThreads.values()) {
                t.interrupt();
            }
            if (channel != null && channel.isOpen()) {
                channel.close();
            }
            if (connection != null && connection.isOpen()) {
                connection.close();
            }
        } catch (Exception e) {
            logger.warn("Error closing RabbitMQ connection", e);
        }
    }

    @Override
    public AvailableStatus testAvailable() {
        try {
            if (connection != null && connection.isOpen() && channel != null && channel.isOpen()) {
                return new AvailableStatus(true, null);
            }
        } catch (Exception e) {
            logger.warn("RabbitMQ testAvailable failed", e);
            return new AvailableStatus(false, e.getMessage());
        }
        return new AvailableStatus(false, null);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void sendMessage(String topic, MessageHeaders headers, byte[] body) {
        try {
            AMQP.BasicProperties.Builder builder = new AMQP.BasicProperties.Builder();
            Map<String, Object> headerMap = new HashMap<>(headers);
            builder.headers(headerMap);

            String routingKey = StringUtils.isNotEmpty(props.getRoutingKey()) ? props.getRoutingKey() : topic;
            channel.basicPublish(props.getExchange(), routingKey, builder.build(), body);
        } catch (Exception e) {
            logger.error("Failed to send RabbitMQ message", e);
            throw new RuntimeException("RabbitMQ send error", e);
        }
    }

    @Override
    public void pullMessage(String topic, String group, MessageHeaders headers, Callback callback) {
        try {
            GetResponse response = channel.basicGet(topic, false);
            if (response != null) {
                AMQP.BasicProperties properties = response.getProps();
                Map<String, Object> headerMap = properties.getHeaders() != null
                        ? new HashMap<>(properties.getHeaders()) : new HashMap<>();
                headers.putAll(headerMap);
                callback.invoke(Collections.singletonList(response.getBody()));
                channel.basicAck(response.getEnvelope().getDeliveryTag(), false);
            }
        } catch (Exception e) {
            logger.error("Failed to pull RabbitMQ message", e);
            throw new RuntimeException("RabbitMQ pull error", e);
        }
    }

    @Override
    public void addMessageListener(String topic, String group, MqMessageListener listener) {
        String key = topic + "::" + group;
        if (listenerRunning.containsKey(key)) {
            logger.warn("Listener already exists for key: {}", key);
            return;
        }

        listenerRunning.put(key, new AtomicBoolean(true));

        Thread thread = new Thread(() -> {
            try {
                Channel consumeChannel = connection.createChannel();
                consumeChannel.queueDeclare(topic, true, false, false, null);
                consumeChannel.basicConsume(topic, false, (consumerTag, message) -> {
                    try {
                        MessageHeaders headers = new MessageHeaders(message.getProperties().getHeaders());
                        headers.put("routingKey", message.getEnvelope().getRoutingKey());
                        headers.put("exchange", message.getEnvelope().getExchange());
                        headers.put("deliveryTag", message.getEnvelope().getDeliveryTag());

                        listener.onMessage(headers, message.getBody());

                        consumeChannel.basicAck(message.getEnvelope().getDeliveryTag(), false);
                    } catch (Exception e) {
                        logger.error("Error processing message", e);
                        consumeChannel.basicNack(message.getEnvelope().getDeliveryTag(), false, true);
                    }
                }, consumerTag -> {});

                listenerThreads.put(key, Thread.currentThread());

                while (listenerRunning.get(key).get()) {
                    Thread.sleep(500);
                }

                consumeChannel.close();
            } catch (Exception e) {
                logger.error("Listener thread error for {}", key, e);
            }
        });
        thread.setName("rabbitmq-listener-" + key);
        thread.start();
    }

    @Override
    public void removeMessageListener(String topic, String group, MqMessageListener listener) {
        String key = topic + "::" + group;
        AtomicBoolean flag = listenerRunning.remove(key);
        if (flag != null) {
            flag.set(false);
        }

        Thread t = listenerThreads.remove(key);
        if (t != null) {
            t.interrupt();
        }
    }

    @Override
    public List<TopicInfo> getTopicInfos() {

        return Collections.emptyList();
    }

}