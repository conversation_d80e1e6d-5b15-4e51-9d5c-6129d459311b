package com.xmcares.platform.admin.developer.dataflow.service;

import com.alibaba.fastjson.JSON;
import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.framework.commons.util.CollectionUtils;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.common.vo.ReturnT;
import com.xmcares.platform.admin.developer.dataflow.model.*;
import com.xmcares.platform.admin.developer.dataflow.vo.*;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import com.xmcares.platform.admin.metadata.database.service.DatasourceService;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.errors.SystemException;
import com.xmcares.platform.admin.developer.dataflow.core.node.ITaskNodeManager;
import com.xmcares.platform.admin.developer.dataflow.core.node.TaskNodeFactory;
import com.xmcares.platform.admin.developer.dataflow.dto.BuildFulleNodeResultDto;
import com.xmcares.platform.admin.developer.dataflow.repository.DevDataflowDefinitionRepository;
import com.xmcares.platform.admin.developer.dataflow.repository.DevDataflowInstanceRepository;
import com.xmcares.platform.admin.developer.dataflow.repository.DevDataflowProcessResourceRepository;
import com.xmcares.platform.admin.developer.dataflow.repository.DevDataflowResourceRepository;
import com.xmcares.platform.admin.developer.dataflow.repository.file.DataDevFileRepository;
//import com.xmcares.platform.admin.developer.dataflow.repository.metadata.DatasourceClient;
import com.xmcares.platform.admin.developer.dataflow.repository.scheduler.XbdpProcessClient;
import com.xmcares.platform.admin.developer.dataflow.repository.xxljob.ProcessTaskClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


@Service
public class DevDataflowDefinitionService {

    private static final Logger LOG = LoggerFactory.getLogger(DevDataflowDefinitionService.class);

    @Autowired
    private DevDataflowDefinitionRepository definitionRepository;
    @Autowired
    private DevDataflowInstanceRepository instanceRepository;
    @Autowired
    private DevDataflowProcessResourceRepository processResourceRepository;
    @Autowired
    private DataDevFileRepository fileRepository;
    @Autowired
    private DevDataflowResourceRepository resourceRepository;
//    @Autowired
//    private DatasourceClient datasourceClient;
    @Autowired
    DatasourceService datasourceService;
    @Autowired
    private XbdpProcessClient processClient;
    @Autowired
    private ProcessTaskClient processTaskClient;
    //分页查询
    public Page<DevDataflow> pageDevDataflow(DevDataflow devDataflow, Pagination pagination) {
        devDataflow.setDeleted(YNEnum.NO.getIntCharCode()); //查询未删除分页信息
        List<DevDataflow> datasources =  definitionRepository.queryDevDataflowPage(devDataflow, new Page<>(pagination.getPageNo()-1, pagination.getPageSize()));
        int total = definitionRepository.countDevDataflow(devDataflow);
        Page<DevDataflow> page = new Page<>();
        page.setData(datasources);
        page.setTotal(total);
        page.setPageNo(pagination.getPageNo());
        page.setPageSize(pagination.getPageSize());
        return page;
    }

    /**
     * 获取所有需要删除的定义
     * @return
     */
    public List<DevDataflow> queryRemoves() {
        return this.definitionRepository.queryByDeleted(YNEnum.YES.getIntCharCode());
    }

    /**
     * 上架
     * @param id 主键
     * @return 是否成功
     */
    public boolean shelve(String id){
        DevDataflow devDataflow=this.definitionRepository.getById(id);
        //验证是否完整性是否正确性
        TaskNodeFactory.create(devDataflow.getGraphOptions()).check();
        return this.definitionRepository.updateDevDataflowShelvedById(id, YNEnum.YES.getIntCharCode());
    }

    /**
     * 下架数据
     * @param id 主键
     * @return
     */
    public Boolean unShelve(String id){
        return this.definitionRepository.updateDevDataflowShelvedById(id, YNEnum.NO.getIntCharCode());
    }

    /**
     * 获取详情
     * @param id 主键
     * @return
     */
    public DevDataflow detail( String id){
        return this.definitionRepository.getById(id);
    }

    /**
     * 初始化定义
     * @param devDataflow
     * @return
     */
    public DevDataflow init(DevDataflow devDataflow){
        boolean flag = validate(devDataflow.getId(), devDataflow.getName());
        if(!flag){
            throw  new BusinessException("流程定义名字不能重复");
        }
        devDataflow.setId(SnowflakeGenerator.getNextId().toString());
        Date current=new Date();
        devDataflow.setCreateTime(current);
        devDataflow.setUpdateTime(current);
        devDataflow.setDeleted(YNEnum.NO.getIntCharCode());
        devDataflow.setShelved(YNEnum.NO.getIntCharCode());
        devDataflow.setCreateUser(UserContextHolder.getUserContext().getUsername());
        definitionRepository.add(devDataflow);
        return devDataflow;
    }

    /**
     * 保存定义信息
     * @param devDataflow
     * @return
     */
    public Boolean save(DevDataflow devDataflow){
        DevDataflow devDataflow1 = this.definitionRepository.getById(devDataflow.getId());
//        if(YNEnum.YES.getIntCharCode().equals(devDataflow1.getShelved())){
//            throw  new BusinessException("上架的定义是不允许编辑保存的");
//        }
        boolean flag = validate(devDataflow.getId(), devDataflow.getName());
        if(!flag){
            throw  new BusinessException("流程定义名字不能重复");
        }
        devDataflow1.setName(devDataflow.getName());
        devDataflow1.setRemark(devDataflow.getRemark());
        devDataflow1.setGraphOptions(devDataflow.getGraphOptions());
        devDataflow1.setUpdateTime(new Date());
        definitionRepository.update(devDataflow1);
        return true;
    }

    /**
     * 伪删除
     * @param id
     * @return
     */
    public Boolean delete(String id){
        int instanceCount = instanceRepository.countByDataflowIdAndStatus(id, ProcessStatusEnum.RUNNING.getValue());
        if (instanceCount > 0) {
            throw new BusinessException("请先停止运行中的实例后，再继续删除！");
        }
        return this.definitionRepository.updateDevDataflowDeletedById(id, YNEnum.YES.getIntCharCode());
    }

    /**
     * 真删除
     * @param id
     * @return
     */
    public Boolean realDelete(String id) {
        return this.definitionRepository.delete(id);
    }

    private boolean validate(String id,String name){
        List<DevDataflow> devDataflows=this.definitionRepository.getByName(name);
        if( devDataflows == null || devDataflows.size() == 0){
            return true;
        }
        if(StringUtils.isEmpty(id)){
            return false;
        }
        for(DevDataflow devDataflow: devDataflows){
            if(devDataflow.getId().equals(id)){
                return true;
            }
        }
        return false;
    }
    /**
     * @return
     */
    public boolean deleteUnUseData(){
        // 1. 获取需要删除的数据
        List<DevDataflow> devDataflowList = this.definitionRepository.queryByDeleted(YNEnum.YES.getCharCode());
        if (CollectionUtils.isEmpty(devDataflowList)) { return true; }
        // 2. 遍历需要删除的数据
        for (DevDataflow devDataflow : devDataflowList) {
            try {
                // 2.0. 休眠20毫秒
                Thread.sleep(20);

                // 2.1. 删除定义所属的资源
                this.resourceRepository.deleteByDataflowId(devDataflow.getId());
            } catch (Exception e) {
                LOG.error("删除定义【{}】的资源，异常", devDataflow.getName(), e);
                continue;
            }
            // 2.2. 删除实例
            boolean processRemoveSuccess = true;
            List<DevDataflowProcess> dataflowProcessList = this.instanceRepository.queryByDataflowIdAndStatus(devDataflow.getId(), null);
            if (!CollectionUtils.isEmpty(devDataflowList)) {
                // 2.2.1. 遍历需要删除的实例
                for (DevDataflowProcess devDataflowProcess : dataflowProcessList) {
                    try {
                        // 2.0. 休眠10毫秒
                        Thread.sleep(10);

                        // 2.2.1.1. 删除实例的资源
                        this.processResourceRepository.deleteByProcessId(devDataflowProcess.getId());
                        // 2.2.1.2. 删除实例下的流程
                        ReturnT<Boolean> returnT = processTaskClient.remove(devDataflowProcess.getId());
                        if(ReturnT.SUCCESS_CODE != returnT.getCode()){
                            LOG.error("删除实例流程【{}】，异常：{}", devDataflowProcess.getName(), returnT.getMsg());
                            processRemoveSuccess = false;
                            continue;
                        }
                        // 2.2.1.3. 删除具体实例
                        this.instanceRepository.delete(devDataflowProcess.getId());
                    } catch (Exception e) {
                        LOG.error("删除实例【{}】，异常", devDataflowProcess.getName(), e);
                        processRemoveSuccess = false;
                    }
                }
            }
            // 2.3. 删除定义
            if (processRemoveSuccess) {
                this.definitionRepository.delete(devDataflow.getId());
            }
        }
        return true;
    }
    /**
     * 发布
     * @param process
     * @return
     */
    @Transactional(rollbackFor = SystemException.class)
    public Boolean publish(DevDataflowProcess process) {
        //1. 校验
        DevDataflow result = definitionRepository.getById(process.getDataflowId());
        if (result == null) { throw new BusinessException("记录不存在"); }
        try {
            //验证是否完整性是否正确性
            TaskNodeFactory.create(result.getGraphOptions()).check();
        }catch (Exception e){
            LOG.error("发布失败",e);
            throw new BusinessException(e.getMessage() + " 请重新编辑！");
        }
//        if (YNEnum.such(result.getShelved()).equals(YNEnum.NO)) {
//            throw new BusinessException("请先上架");
//        }
        //2. 初始化实例信息
        DevDataflowProcess instance = DevDataflowProcess.createBy(result, process);
        //3. 解析节点信息, 并根据资源ID或者数据源ID构建完整的参数
        ITaskNodeManager<DevDataflowNodeVo> manager = TaskNodeFactory.create(result.getGraphOptions());
        BuildFulleNodeResultDto buildResult = buildFullNodes(instance.getName(), manager.nodeInfos());
        //4. 构建并保存文件信息
        DevDataflowFileVo fileEntity = DevDataflowFileVo.createBy(instance, buildResult.getNodes());
        instance.setPath(fileRepository.save(instance.getId() + ".json", JSON.toJSONString(fileEntity)));
        //5. 构建需要保存的信息
        //5.1. 构建资源与实例关系信息
        List<DevDataflowProcessResource> batchSaveProcessResource = DevDataflowProcessResource.createAll(instance.getId(), buildResult.getUseResources());
        //5.2. 构建流程控制模块信息
        List<AddProcessTaskVo> addProcessTasks = AddProcessTaskVo.createFrom(instance.getId(),buildResult.getNodes(),instance.getPath());
        try {
            //6. 新增实例信息
            instanceRepository.add(instance);
            //7. 新增资源信息
            if (!CollectionUtils.isEmpty(batchSaveProcessResource)) {
                processResourceRepository.batchSave(batchSaveProcessResource);
            }
        } catch (Exception e) {
            LOG.error("保存实例数据异常", e);
            throw new SystemException("新增实例数据异常");
        }
        //8. 保存流程控制模块信息
        ReturnT<Boolean> addResult = processClient.add(addProcessTasks);
        if (addResult.getCode() != ReturnT.SUCCESS_CODE) {
            throw new SystemException("创建任务失败！" + addResult.getMsg());
        }
        return true;
    }

    private BuildFulleNodeResultDto buildFullNodes(String name, List<DevDataflowNodeVo> nodes) {
        try {
            //1. 转 Map 方便快速处理数据
            Map<String, DevDataflowNodeVo> handler = nodes.stream().collect(Collectors.toMap(DevDataflowNodeVo::getId, t->t));
            //2. 根据内部数据源ID获取实际信息
            Map<String, List<String>> datasourceIds = handlerInnerParams(nodes, "datasourceId");
            // 原始feign调用
            // List<DatasourceVo> dataSources = datasourceIds.isEmpty() ? new ArrayList<>() :
            // Optional.ofNullable(datasourceClient.listDatasource(new ArrayList<>(datasourceIds.keySet()))).orElse(new ArrayList<>());
            List<Datasource> dataSources = datasourceIds.isEmpty() ? new ArrayList<>() :
                    Optional.ofNullable(datasourceService.listDatasource(new ArrayList<>(datasourceIds.keySet()))).orElse(new ArrayList<>());
            if (!dataSources.isEmpty() && dataSources.size() != datasourceIds.size()) {
                throw new BusinessException("数据源信息丢失！");
            }
            //2.1. 将数据源信息设置到Handler中
            dataSources.forEach(datasource-> {
                List<String> nodeIds = datasourceIds.get(datasource.getId());
                Map<String, Object> datasourceParams = JSON.parseObject(datasource.getOptions());
                String type = datasource.getType();
                nodeIds.forEach(nodeId-> {
                    List<DevDataflowNodeVo.DevDataflowNodeParam> params = handler.get(nodeId).getParams();
                    params.add(DevDataflowNodeVo.createParam("datasource.type", type));
                    datasourceParams.forEach((k,v)-> params.add(DevDataflowNodeVo.createParam("datasource." + k, v)));
                });
            });
            //3. 根据内部资源ID获取实际信息
            Map<String, List<String>> resourceIds = handlerInnerParams(nodes, "resourceId");
            List<DevDataflowResource> resources = resourceIds.isEmpty() ? new ArrayList<>() :
                    Optional.ofNullable(resourceRepository.findByIds(new ArrayList<>(resourceIds.keySet()))).orElse(new ArrayList<>());
            if (!dataSources.isEmpty() && resourceIds.size() != resources.size()) {
                throw new BusinessException("资源信息丢失！");
            }
            //3.1. 将资源信息设置到Handler中
            resources.forEach(res-> {
                List<String> nodeIds = resourceIds.get(res.getId());
                nodeIds.forEach(nodeId-> {
                    List<DevDataflowNodeVo.DevDataflowNodeParam> params = handler.get(nodeId).getParams();
                    params.add(DevDataflowNodeVo.createParam("resource.path", res.getPath()));
                });
            });
            return new BuildFulleNodeResultDto(new ArrayList<>(handler.values()), resources);

        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw e;
            }
            LOG.error("构建【{}】的完整数据期间遇到异常", name, e);
            throw new SystemException("内部服务异常， 数据构建未成功！");
        }
    }

    private Map<String, List<String>> handlerInnerParams(List<DevDataflowNodeVo> nodes,String idKey) {
        Map<String, List<String>> result = new HashMap<>();
        for (DevDataflowNodeVo node : nodes) {
            Map<String, Object> params = Optional.ofNullable(node.getParams()).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(DevDataflowNodeVo.DevDataflowNodeParam::getKey,
                    DevDataflowNodeVo.DevDataflowNodeParam::getValue));
            if (!params.containsKey(idKey)) { continue; }
            String triggerId = params.get(idKey).toString();
            if (StringUtils.isEmpty(triggerId)) { continue; }
            if (result.containsKey(triggerId)) {
                result.get(triggerId).add(node.getId());
            } else {
                List<String> nodeIds = new ArrayList<>();
                nodeIds.add(node.getId());
                result.put(triggerId, nodeIds);
            }
        }
        return result;
    }


}
