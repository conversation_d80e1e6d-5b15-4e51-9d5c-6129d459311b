package com.xmcares.platform.admin.asset.repository;

import com.alibaba.fastjson.JSON;
import com.xmcares.framework.commons.error.BusinessException;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.platform.admin.asset.model.AsstStatsMetrics;
import com.xmcares.platform.admin.asset.model.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.ColumnMapRowMapper;
import org.springframework.stereotype.Repository;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.temporal.TemporalField;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/30 10:16
 */
@Repository
public class AssetSummaryRepository {

    private static final Logger LOG = LoggerFactory.getLogger(AssetSummaryRepository.class);

    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    public static final String TABLE_BDP_ASST_STATS_METRICS = "bdp_asst_stats_metrics";
    public static final String TABLE_BDP_META_DATAWARE = "bdp_meta_dataware";

    DecimalFormat df = new DecimalFormat("0.##");

    public List<DatawareListQueryVO> datawareListQuery(String[] datawareIds, String[] metricses) {
        try {
            String metricsesStr = Arrays.stream(metricses).collect(Collectors.joining("','", "('", "')"));
            String datawareIdsStr = Arrays.stream(datawareIds).collect(Collectors.joining("','", "('", "')"));

            List<Map<String, Object>> step1 = xcfJdbcTemplate.<HashMap<String, Object>>queryForList(
                    " select " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".stats_targets stats_targets, " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".metric_name metric_name, " +
                            " " + TABLE_BDP_META_DATAWARE + ".code code, " +
                            " " + TABLE_BDP_META_DATAWARE + ".name name, " +
                            " " + TABLE_BDP_META_DATAWARE + ".remark remark, " +
                            " " + TABLE_BDP_META_DATAWARE + ".`type` `type`, " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".metric_value metric_value, " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".create_time updateTime " +
                            " from " + TABLE_BDP_ASST_STATS_METRICS +
                            " left join " + TABLE_BDP_META_DATAWARE + " on json_unquote(JSON_EXTRACT(" + TABLE_BDP_ASST_STATS_METRICS + ".metric_labels, '$.dataware_id'))=" + TABLE_BDP_META_DATAWARE + ".id" +
                            " where  " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".stats_status ='1' and " +
                            " " + TABLE_BDP_META_DATAWARE + ".id is not  null and " +
                            ((metricses == null || metricses.length == 0) ? (" true ") : (TABLE_BDP_ASST_STATS_METRICS + ".metric_name in " + metricsesStr) + " and ") +
                            ((datawareIds == null || datawareIds.length == 0) ? (" true ") : (" json_unquote(JSON_EXTRACT(" + TABLE_BDP_ASST_STATS_METRICS + ".metric_labels, '$.dataware_id')) in " + datawareIdsStr))
            );

            //group and sort filter
            HashMap<String, List<Map<String, Object>>> step2 = new HashMap<>();
            for (Map<String, Object> step1Map : step1) {
                String key = String.valueOf(step1Map.getOrDefault("stats_targets", "")) + String.valueOf(step1Map.getOrDefault("metric_name", ""));
                step2.putIfAbsent(key, new ArrayList<Map<String, Object>>());
                step2.get(key).add(step1Map);
            }
            List<Map<String, Object>> step2Max = step2.values().stream().map(g -> g.stream().max((l1, l2) -> ((LocalDateTime) l1.get("updateTime")).compareTo((LocalDateTime) l2.get("updateTime")))).map(v -> v.get()).collect(Collectors.toList());

            //merge by code
            HashMap<String, List<Map<String, Object>>> step3 = new HashMap<>();
            for (Map<String, Object> step2MaxMap : step2Max) {
                String key = String.valueOf(step2MaxMap.getOrDefault("code", ""));
                step3.putIfAbsent(key, new ArrayList<Map<String, Object>>());
                step3.get(key).add(step2MaxMap);
            }
            List<DatawareListQueryVO> newlestMetrics = step3.values().stream().map(g -> {
                HashMap<String, Object> mergeLine = new HashMap<>();

                if (g.size() > 0) {
                    Map<String, Object> l = g.get(0);
                    mergeLine.put("code", l.get("code"));
                    mergeLine.put("name", l.get("name"));
                    mergeLine.put("remark", l.get("remark"));
                    mergeLine.put("type", l.get("type"));
                }
                LocalDateTime tableCountTime = null;
                LocalDateTime databaseSizeTime = null;

                for (Map<String, Object> l : g) {
                    if (metricses[0].equals(l.get("metric_name"))) {
                        if (tableCountTime == null) {
                            tableCountTime = (LocalDateTime) l.get("updateTime");
                        }
                        if (tableCountTime.isBefore((LocalDateTime) l.get("updateTime")) || tableCountTime.isEqual((LocalDateTime) l.get("updateTime"))) {
                            mergeLine.put("tableCount", l.getOrDefault("metric_value", "-1"));
                            tableCountTime = (LocalDateTime) l.get("updateTime");
                        }

                    } else if (metricses[1].equals(l.get("metric_name"))) {
                        if (databaseSizeTime == null) {
                            databaseSizeTime = (LocalDateTime) l.get("updateTime");
                        }
                        if (databaseSizeTime.isBefore((LocalDateTime) l.get("updateTime")) || databaseSizeTime.isEqual((LocalDateTime) l.get("updateTime"))) {
                            mergeLine.put("size", l.getOrDefault("metric_value", "-1"));
                            databaseSizeTime = (LocalDateTime) l.get("updateTime");
                        }

                    }
                }
                if (tableCountTime != null && databaseSizeTime != null) {
                    if (tableCountTime.isBefore(databaseSizeTime)) {
                        mergeLine.put("updateTime", databaseSizeTime);
                    } else {
                        mergeLine.put("updateTime", tableCountTime);
                    }
                }
                return mergeLine;
            }).map(m -> JSON.parseObject(JSON.toJSONString(m), DatawareListQueryVO.class)).filter(b->b != null).map(b -> {
                try{
                    b.setSize(df.format(Double.valueOf(b.getSize())));
                }catch (Exception e){
                    LOG.error("datawareListQuery", e);
                    b.setSize("0.0");
                }
                return b;
            }).collect(Collectors.toList());

            return newlestMetrics;
        } catch (Exception e) {
            LOG.warn("datawareListQuery", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }


    public List<DatawareSizeQueryVO> datawareSizeQuery(List<String> datawareIds, String beginDate, String endDate, String metricse, String order) {

        try {
            String datawareIdsStr = datawareIds.stream().collect(Collectors.joining("','", "('", "')"));


            List<Map<String, Object>> step1 = xcfJdbcTemplate.<HashMap<String, Object>>queryForList(
                    " select " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".stats_targets stats_targets, " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".metric_name metric_name, " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".id id, " +
                            " " + TABLE_BDP_META_DATAWARE + ".code datawareKey, " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".metric_date datetime, " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".metric_value value, " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".create_time updateTime " +
                            " from " + TABLE_BDP_ASST_STATS_METRICS +
                            " left join " + TABLE_BDP_META_DATAWARE + " on json_unquote(JSON_EXTRACT(" + TABLE_BDP_ASST_STATS_METRICS + ".metric_labels, '$.dataware_id'))=" + TABLE_BDP_META_DATAWARE + ".id" +
                            " where  " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".stats_status ='1' and " +
                            " " + TABLE_BDP_META_DATAWARE + ".id is not  null and " +
                            " metric_name = '" + metricse + "'" + " and " +
                            (" metric_date >= '" + beginDate + "'" + " and ") +
                            (" metric_date <= '" + endDate + "'" + " and ") +
                            ((datawareIds.size() == 0) ? (" true ") : (" json_unquote(JSON_EXTRACT(" + TABLE_BDP_ASST_STATS_METRICS + ".metric_labels, '$.dataware_id')) in " + datawareIdsStr))
            );

            //group and sort filter
            HashMap<String, List<Map<String, Object>>> step2 = new HashMap<>();
            for (Map<String, Object> step1Map : step1) {
                String key = String.valueOf(step1Map.getOrDefault("stats_targets", "")) + String.valueOf(step1Map.getOrDefault("metric_name", "")) + String.valueOf(step1Map.getOrDefault("datetime", ""));
                step2.putIfAbsent(key, new ArrayList<Map<String, Object>>());
                step2.get(key).add(step1Map);
            }
            List<Map<String, Object>> step2Max = step2.values().stream().map(g -> g.stream().max((l1, l2) -> ((LocalDateTime) l1.get("updateTime")).compareTo((LocalDateTime) l2.get("updateTime")))).map(v -> v.get()).collect(Collectors.toList());

            List<DatawareSizeQueryVO> newlestMetrics = step2Max.stream().map(m -> JSON.parseObject(JSON.toJSONString(m), DatawareSizeQueryVO.class)).map(b -> {
                b.setValue(df.format(Double.valueOf(b.getValue())));
                return b;
            }).collect(Collectors.toList());

            return newlestMetrics;
        } catch (Exception e) {
            LOG.warn("datawareSizeQuery", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }


    public List<AssetSummaryStatusQueryVO> statusQuery(String metricse) {
        try {


            List<Map<String, Object>> step1 = xcfJdbcTemplate.<HashMap<String, Object>>queryForList(
                    " SELECT " +
                            TABLE_BDP_ASST_STATS_METRICS + ".*  , " +
                            " json_unquote(JSON_EXTRACT( metric_labels, '$.status_name' ))  status_name " +
                            " from " + TABLE_BDP_ASST_STATS_METRICS +
                            " where  " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".stats_status ='1' and " +
                            " metric_name = '" + metricse + "' "
            );

            //group and sort filter
            HashMap<String, List<Map<String, Object>>> step2 = new HashMap<>();
            for (Map<String, Object> step1Map : step1) {
                String key = /*String.valueOf(step1Map.getOrDefault("metric_date", "")) + */String.valueOf(step1Map.getOrDefault("stats_targets", "")) + String.valueOf(step1Map.getOrDefault("status_name", ""));
                step2.putIfAbsent(key, new ArrayList<Map<String, Object>>());
                step2.get(key).add(step1Map);
            }
            List<Map<String, Object>> step2Max = step2.values().stream().map(g -> g.stream().max((l1, l2) -> ((LocalDateTime) l1.get("update_time")).compareTo((LocalDateTime) l2.get("update_time")))).map(v -> v.get()).collect(Collectors.toList());

            List<AsstStatsMetrics> asstStatsMetrics = step2Max.stream().map(m -> JSON.parseObject(JSON.toJSONString(m), AsstStatsMetrics.class)).map(b -> {
                b.setMetricValue(df.format(Double.valueOf(b.getMetricValue())));
                return b;
            }).collect(Collectors.toList());


            SimpleDateFormat pares = new SimpleDateFormat("yyyy-MM-dd");
            HashMap<String, AssetSummaryStatusQueryVO> assetSummaryStatusQueryVOHashMap = new HashMap<>();
            for (AsstStatsMetrics asstStatsMetric : asstStatsMetrics) {

                HashMap<String, String> lablesMap = JSON.parseObject(asstStatsMetric.getMetricLabels(), HashMap.class);
                AssetSummaryStatusQueryVO value = new AssetSummaryStatusQueryVO();
                String statusTaskName = lablesMap.get("status_task_name");
                if (statusTaskName == null) {
                    statusTaskName = "None";
                } else {
                    if (statusTaskName.length() > 30) {
                        statusTaskName = statusTaskName.substring(0, 30);
                    }
                }
                value.setName(statusTaskName);
                value.setMetricDate(pares.format(asstStatsMetric.getMetricDate()));

                String k = asstStatsMetric.getMetricDate() + asstStatsMetric.getStatsTargets();
                assetSummaryStatusQueryVOHashMap.putIfAbsent(k, value);

                AssetSummaryStatusQueryVO assetSummaryStatusQueryVO = assetSummaryStatusQueryVOHashMap.get(k);
                List<AssetSummaryStatusQueryVO.StatusCountInfo> data = assetSummaryStatusQueryVO.getData();
                if (data == null) {
                    data = new ArrayList<>();
                    assetSummaryStatusQueryVO.setData(data);
                }

                AssetSummaryStatusQueryVO.StatusCountInfo statusCountInfo = new AssetSummaryStatusQueryVO.StatusCountInfo();
                statusCountInfo.setKey(lablesMap.get("status_name"));
                statusCountInfo.setValue(String.valueOf(asstStatsMetric.getMetricValue()));
                data.add(statusCountInfo);
            }

            ArrayList<AssetSummaryStatusQueryVO> assetSummaryStatusQueryVOS = new ArrayList<>();
            assetSummaryStatusQueryVOS.addAll(assetSummaryStatusQueryVOHashMap.values());
            return assetSummaryStatusQueryVOS;
        } catch (Exception e) {
            LOG.warn("statusQuery", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }

    public List<DatatableSizeTopsQueryVO> datatableSizeTopsQuery(String[] datawareIds, String metricse, String order, Integer limit) {
        try {
            String datawareIdsStr = Arrays.stream(datawareIds).collect(Collectors.joining("','", "('", "')"));


            List<Map<String, Object>> step1 = xcfJdbcTemplate.<HashMap<String, Object>>queryForList(
                    " select " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".stats_targets stats_targets, " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".create_time updateTime, " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".metric_date datetime, " +
                            " REPLACE(CONCAT(json_unquote(JSON_EXTRACT(metric_labels, '$.dataware_name')), '.', json_unquote(JSON_EXTRACT(metric_labels, '$.table_name'))), '\"', '') name, " +
                            " CAST(" + TABLE_BDP_ASST_STATS_METRICS + ".metric_value AS DECIMAL(65,2)) value " +
                            " from " + TABLE_BDP_ASST_STATS_METRICS +
                            " left join " + TABLE_BDP_META_DATAWARE + " on json_unquote(JSON_EXTRACT(" + TABLE_BDP_ASST_STATS_METRICS + ".metric_labels, '$.dataware_id'))=" + TABLE_BDP_META_DATAWARE + ".id" +
                            " where  " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".stats_status ='1' and " +
                            " metric_name = '" + metricse + "'" + " and " +
                            " " + TABLE_BDP_META_DATAWARE + ".id is not  null and " +
                            ((datawareIds.length == 0) ? (" true ") : (" json_unquote(JSON_EXTRACT(" + TABLE_BDP_ASST_STATS_METRICS + ".metric_labels, '$.dataware_id')) in " + datawareIdsStr))
            );

            //group and sort filter
            HashMap<String, List<Map<String, Object>>> step2 = new HashMap<>();
            for (Map<String, Object> step1Map : step1) {
                String key = String.valueOf(step1Map.getOrDefault("stats_targets", ""));
                step2.putIfAbsent(key, new ArrayList<Map<String, Object>>());
                step2.get(key).add(step1Map);
            }
            List<Map<String, Object>> step2Max = step2.values().stream().map(g -> g.stream().max((l1, l2) -> ((LocalDateTime) l1.get("updateTime")).compareTo((LocalDateTime) l2.get("updateTime")))).map(v -> v.get()).collect(Collectors.toList());

            List<DatatableSizeTopsQueryVO> datatableSizeTopsQueryVOS = step2Max.stream().map(m -> JSON.parseObject(JSON.toJSONString(m), DatatableSizeTopsQueryVO.class)).map(b -> {
                b.setValue(df.format(Double.valueOf(b.getValue())));
                return b;
            }).collect(Collectors.toList());

            Collections.<DatatableSizeTopsQueryVO>sort(datatableSizeTopsQueryVOS, new Comparator<DatatableSizeTopsQueryVO>() {
                @Override
                public int compare(DatatableSizeTopsQueryVO o1, DatatableSizeTopsQueryVO o2) {

                    return order.toLowerCase().equals("desc") ? Double.valueOf(o2.getValue()).compareTo(Double.valueOf(o1.getValue())) : Double.valueOf(o1.getValue()).compareTo(Double.valueOf(o2.getValue()));
                }
            });

            return datatableSizeTopsQueryVOS.stream().limit(limit).collect(Collectors.toList());
        } catch (Exception e) {
            LOG.warn("datatableSizeTopsQuery", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }

    public List<ServiceHotTopsQueryVO> serviceHotTopsQuery(String[] datawareIds, String metricse, String order) {
        try {
            String datawareIdsStr = Arrays.stream(datawareIds).collect(Collectors.joining(",'", "('", "')"));


            List<Map<String, Object>> step1 = xcfJdbcTemplate.<HashMap<String, Object>>queryForList(
                    " select " +
                            " stats_targets NAME , " +
                            " CAST(" + TABLE_BDP_ASST_STATS_METRICS + ".metric_value AS DECIMAL(65,2)) value" +
                            " from " + TABLE_BDP_ASST_STATS_METRICS +
                            " where  " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".stats_status ='1' and " +
                            " metric_name = '" + metricse + "'" + " and " +
                            ((datawareIds.length == 0) ? (" true ") : (" json_unquote(JSON_EXTRACT(" + TABLE_BDP_ASST_STATS_METRICS + ".metric_labels, '$.dataware_id')) in " + datawareIdsStr))
            );

            //group and sort filter
            HashMap<String, List<Map<String, Object>>> step2 = new HashMap<>();
            for (Map<String, Object> step1Map : step1) {
                String key = String.valueOf(step1Map.getOrDefault("NAME", ""));
                step2.putIfAbsent(key, new ArrayList<Map<String, Object>>());
                step2.get(key).add(step1Map);
            }
            List<Map<String, Object>> step2Max = step2.values().stream().map(g -> g.stream().max((l1, l2) -> (Double.valueOf(String.valueOf(l1.get("value")))).compareTo(Double.valueOf(String.valueOf(l2.get("value")))))).map(v -> v.get()).collect(Collectors.toList());

            List<ServiceHotTopsQueryVO> serviceHotTopsQueryVOS = step2Max.stream().map(m -> JSON.parseObject(JSON.toJSONString(m), ServiceHotTopsQueryVO.class)).map(b -> {
                b.setValue(df.format(Double.valueOf(b.getValue())));
                return b;
            }).sorted((b1, b2) -> order.toLowerCase().equals("desc") ? Double.valueOf(b2.getValue()).compareTo(Double.valueOf(b1.getValue())) : Double.valueOf(b1.getValue()).compareTo(Double.valueOf(b2.getValue()))).limit(20).collect(Collectors.toList());


            return serviceHotTopsQueryVOS;
        } catch (Exception e) {
            LOG.warn("serviceHotTopsQuery", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }
}
