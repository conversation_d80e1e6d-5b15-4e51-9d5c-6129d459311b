/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2023/6/3
 */
package com.xmcares.platform.admin.common.datasource.mq.rocketmq.core;

import org.apache.rocketmq.client.consumer.AllocateMessageQueueStrategy;
import org.apache.rocketmq.client.consumer.store.ReadOffsetType;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.impl.consumer.MQConsumerInner;
import org.apache.rocketmq.client.impl.consumer.ProcessQueue;
import org.apache.rocketmq.client.impl.consumer.PullRequest;
import org.apache.rocketmq.client.impl.consumer.RebalanceImpl;
import org.apache.rocketmq.client.impl.factory.MQClientInstance;
import org.apache.rocketmq.common.MixAll;
import org.apache.rocketmq.common.message.MessageQueue;
import org.apache.rocketmq.common.protocol.heartbeat.ConsumeType;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.common.protocol.heartbeat.SubscriptionData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 此类由于自定义实现{@link MQConsumerInner}，需要相配套实现{@link RebalanceImpl}
 * 注：rocketmq 客户端的代码设计的循环引用。
 * <AUTHOR>
 * @since 1.4.1
 */
public class CustomRebalanceImpl extends RebalanceImpl {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    private CustomMQConsumer customMQConsumer;

    public CustomRebalanceImpl(CustomMQConsumer customMQConsumer) {
        this(customMQConsumer.groupName(), customMQConsumer.messageModel(), null, customMQConsumer.getMqClientFactory());
        this.customMQConsumer = customMQConsumer;
    }

    public CustomRebalanceImpl(String consumerGroup, MessageModel messageModel, AllocateMessageQueueStrategy allocateMessageQueueStrategy, MQClientInstance mQClientFactory) {
        super(consumerGroup, messageModel, allocateMessageQueueStrategy, mQClientFactory);
    }

    /**
     * 负载均衡分配的队列发生变化时
     *
     * @param topic
     * @param mqAll
     * @param mqDivided
     */
    @Override
    public void messageQueueChanged(String topic, Set<MessageQueue> mqAll, Set<MessageQueue> mqDivided) {
        this.customMQConsumer.messageQueueChanged(topic, mqAll, mqDivided);
    }

    @Override
    public boolean removeUnnecessaryMessageQueue(MessageQueue mq, ProcessQueue pq) {
        this.customMQConsumer.getOffsetStore().persist(mq);
        this.customMQConsumer.getOffsetStore().removeOffset(mq);
        return true;
    }

    @Override
    public ConsumeType consumeType() {
        return ConsumeType.CONSUME_ACTIVELY;
    }

    @Override
    public void removeDirtyOffset(MessageQueue mq) {
        this.customMQConsumer.getOffsetStore().removeOffset(mq);
    }

    @Override
    public long computePullFromWhere(MessageQueue mq) {
        long result;
        long lastOffset = this.customMQConsumer.getOffsetStore().readOffset(mq, ReadOffsetType.MEMORY_FIRST_THEN_STORE);
        if (lastOffset <= 0) {//-2，-1：获取异常；0：首次消费
            result = this.fetchMaxOffset(mq);
            if (logger.isDebugEnabled()) {
                logger.debug("MQ[{}]ComputePullFromWhere: [offset:{}]",  mq, result);
            }
        } else {
            long hoursBefore = this.customMQConsumer.consumeOffsetBeforeHours();
            result = this.fetchTimestampOffset(mq, hoursBefore);
            if (logger.isDebugEnabled()) {
                logger.info("获取队列拉取偏移量: {}, Offset [Max(hoursBefore={}, last={}))]",  mq, result, lastOffset);
            }
            result = Math.max(result, lastOffset);
        }
        return result;
    }

    /**
     * 覆盖并改写rocketmq的{@link RebalanceImpl#doRebalance(boolean)}
     *
     * @param isOrder
     */
    public void doRebalance(final boolean isOrder) {
        Map<String, SubscriptionData> subTable = this.getSubscriptionInner();
        if (subTable != null) {
            for (final Map.Entry<String, SubscriptionData> entry : subTable.entrySet()) {
                final String topic = entry.getKey();
                try {
                    this.rebalanceByTopic(topic, isOrder);
                } catch (Throwable e) {
                    if (!topic.startsWith(MixAll.RETRY_GROUP_TOPIC_PREFIX)) {
                        log.warn("rebalanceByTopic Exception", e);
                    }
                }
            }
        }

    }

    /**
     * 在重新负载均衡的适合，调用{@link #messageQueueChanged(String, Set, Set)}修改可拉主题取消息的队列
     * @param topic 重新负载均衡的主题
     * @param isOrder
     */
    private void rebalanceByTopic(final String topic, final boolean isOrder) {
        Set<MessageQueue> mqSet = this.topicSubscribeInfoTable.get(topic);
        List<String> cidAll = this.mQClientFactory.findConsumerIdList(topic, consumerGroup);
        if (null == mqSet) {
            if (!topic.startsWith(MixAll.RETRY_GROUP_TOPIC_PREFIX)) {
                log.warn("doRebalance, {}, but the topic[{}] not exist.", consumerGroup, topic);
            }
        }
        if (null == cidAll) {
            log.warn("doRebalance, {} {}, get consumer id list failed", consumerGroup, topic);
        }
        if (mqSet != null && cidAll != null) {
            List<MessageQueue> mqAll = new ArrayList<MessageQueue>();
            mqAll.addAll(mqSet);

            Collections.sort(mqAll);
            Collections.sort(cidAll);

            AllocateMessageQueueStrategy strategy = this.allocateMessageQueueStrategy;
            List<MessageQueue> allocateResult = null;
            try {
                allocateResult = strategy.allocate(this.consumerGroup, this.mQClientFactory.getClientId(),
                        mqAll, cidAll);
            } catch (Throwable e) {
                log.error("AllocateMessageQueueStrategy.allocate Exception. allocateMessageQueueStrategyName={}", strategy.getName(),
                        e);
                return;
            }

            Set<MessageQueue> allocateResultSet = new HashSet<MessageQueue>();
            if (allocateResult != null) {
                allocateResultSet.addAll(allocateResult);
            }
            this.messageQueueChanged(topic, mqSet, allocateResultSet);
        }
    }

    private long fetchTimestampOffset(MessageQueue mq, long hoursBefore) {
        long result;
        long timestamp = Instant.now().minus(hoursBefore, ChronoUnit.HOURS).toEpochMilli();
        try {
            result = this.mQClientFactory.getMQAdminImpl().searchOffset(mq, timestamp);
        } catch (MQClientException e) {
            logger.warn("获取不到MQ[{}]的[timestamp = {}]的Offset，返回-1", mq, timestamp);
            result = -1;
        }
        return result;
    }

    private long fetchMaxOffset(MessageQueue mq) {
        long result;
        try {
            result = this.mQClientFactory.getMQAdminImpl().maxOffset(mq);
        } catch (MQClientException e) {
            logger.warn("获取不到MQ[{}]的maxOffset，返回-1", mq);
            result = 0;
        }
        return result;
    }


    @Override
    public void dispatchPullRequest(List<PullRequest> pullRequestList) {
        System.out.println("balance >>>dispatchPullRequest: pullRequestList=" + Arrays.toString(pullRequestList.toArray()));
    }
}
