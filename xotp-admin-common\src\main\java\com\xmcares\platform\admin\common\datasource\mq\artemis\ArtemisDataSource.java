/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/10
 */
package com.xmcares.platform.admin.common.datasource.mq.artemis;

import com.xmcares.platform.admin.common.datasource.mq.MessageHeaders;
import com.xmcares.platform.admin.common.datasource.mq.MqDataSource;
import org.apache.activemq.artemis.api.core.*;
import org.apache.activemq.artemis.api.core.client.*;
import org.apache.activemq.artemis.core.remoting.impl.netty.NettyConnectorFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public class ArtemisDataSource implements MqDataSource {

    private static final Logger logger = LoggerFactory.getLogger(ArtemisDataSource.class);

    private final String name;
    private final ArtemisProperties properties;
    private ServerLocator serverLocator;
    private ClientSessionFactory sessionFactory;

    // 存储每个主题和消费者组对应的监听器列表
    // Key: topicName + "_" + consumerGroupName, Value: List<MqMessageListenerWrapper>
    private final Map<String, MqMessageListenerWrapper> listenerMap = new ConcurrentHashMap<>();

    // 专门用于处理消息监听(订阅)的线程池
    private final ExecutorService listenerThreadPool;

    private final AtomicBoolean initialized = new AtomicBoolean(false); // 避免重复初始化
    private final AtomicBoolean closed = new AtomicBoolean(false); // 标记是否已关闭

    public ArtemisDataSource(String name, ArtemisProperties properties) {
        this.name = name;
        this.properties = properties;
        // 为每个ArtemisDataSource实例创建一个独立的监听器线程池，便于管理其生命周期
        this.listenerThreadPool = new ThreadPoolExecutor(
                0, // 核心线程数0，按需创建
                Integer.MAX_VALUE, // 最大线程数
                60L, TimeUnit.SECONDS, // 线程空闲时间
                new SynchronousQueue<>(), // 任务队列，不存储任务，直接创建新线程
                r -> new Thread(r, "artemis-listener-" + name + "-" + r.hashCode()), // 线程工厂
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：调用者运行
        );
    }

    private synchronized void initialize() {
        if (initialized.get()) {
            logger.warn("Artemis data source [{}] already initialized, skipping.", name);
            return;
        }

        try {
            URI brokerUri = new URI(properties.getBrokerUrl());
            Map<String, Object> params = new HashMap<>();
            params.put("host", brokerUri.getHost());
            params.put("port", brokerUri.getPort());

            TransportConfiguration config =
                    new TransportConfiguration(NettyConnectorFactory.class.getName(), params);

            // 使用createServerLocatorWithHA
            // 考虑重连和故障转移，可以在ServerLocator上设置更多参数
            serverLocator = ActiveMQClient.createServerLocatorWithHA(config);
            serverLocator.setReconnectAttempts(-1); // 永远尝试重连
            serverLocator.setInitialConnectAttempts(-1); // 初始连接也永远尝试
            serverLocator.setCallFailoverTimeout(60000L); // 故障转移超时时间
            serverLocator.setConfirmationWindowSize(1024); // 确认窗口大小，影响吞吐量

            sessionFactory = serverLocator.createSessionFactory();
            logger.info("Artemis 数据源 [{}] 已初始化，服务端：{}", name, properties.getBrokerUrl());
            initialized.set(true);
        } catch (URISyntaxException e) {
            throw new RuntimeException("Artemis 源 [“ + this.getName（） + ”] 的服务端 URL 无效", e);
        } catch (Exception e) {
            throw new RuntimeException("初始化 Artemis 源 [" + this.getName() + "]失败", e);
        }
    }

    @Override
    public AvailableStatus testAvailable() {
        this.initialize();

        if (!initialized.get()) {
            return new AvailableStatus(false, "Artemis data source not initialized.");
        }
        ClientSession session = null;
        try {
            // 使用try-with-resources确保session关闭
            session = sessionFactory.createSession(properties.getUsername(), properties.getPassword(),
                    false, true, true, false, 0);
            session.start(); // 启动session
            return new AvailableStatus(true, null);
        } catch (Exception e) {
            logger.warn("测试 Artemis [{}] 连接失败： {}", name, e.getMessage());
            return new AvailableStatus(false, e.getMessage());
        } finally {
            if (session != null) {
                try {
                    session.close();
                } catch (ActiveMQException e) {
                    logger.warn("关闭 Artemis 源 [{}] 的测试会话时出错： {}", name, e.getMessage());
                }
            }
        }
    }

    @Override
    public String getName() {
        return this.name;
    }


    public ArtemisProperties getProperties() {
        return this.properties;
    }

    /**
     * 发送消息到主题 (Multicast Address)。
     * 注意：这里不再需要创建队列。
     *
     * @param topic   主题名称 (即 Artemis 中的 Address)
     * @param headers 消息头
     * @param body    消息体
     */
    @Override
    public void sendMessage(String topic, MessageHeaders headers, byte[] body) {
        this.initialize();

        if (!initialized.get() || closed.get()) {
            throw new IllegalStateException("Artemis 数据源 [“ + name + ”] 未初始化或已关闭。");
        }

        ClientSession session = null;
        ClientProducer producer = null;
        try {
            // 创建会话（事务 false，自动确认 true，autoCommitSends true）
            // 生产者的Session可以是非事务的
            session = sessionFactory.createSession(properties.getUsername(), properties.getPassword(),
                    false, true, true, false,  0);
            session.start(); // 启动session

            // 直接创建producer到地址 (topic)，Artemis会自动识别为Multicast
            producer = session.createProducer(topic);

            ClientMessage message = session.createMessage(true); // 持久化消息
            if (headers != null) {
                headers.forEach((k, v) -> message.putStringProperty(k, String.valueOf(v)));
            }
            message.getBodyBuffer().writeBytes(body);
            producer.send(message);
            logger.info("已发送消息到 source[{}] topic[{}]，大小：{}", name, topic, body.length);
        } catch (Exception e) {
            logger.error("无法向源 [{}] 主题 [{}] 发送消息", name, topic, e);
            throw new RuntimeException(
                    String.format("发送 Artemis 源 [%s] 主题 [%s] 消息失败", name, topic), e);
        } finally {
            if (producer != null) {
                try {
                    producer.close();
                } catch (ActiveMQException ignored) { /* ignore */ }
            }
            if (session != null) {
                try {
                    session.close();
                } catch (ActiveMQException ignored) { /* ignore */ }
            }
        }
    }

    /**
     * 拉取模式。消息确认现在由 PullCallback 负责。
     *
     * @param topic    主题名称
     * @param group    消费组
     * @param headers  消息头 (目前此参数在拉取模式下无实际用途)
     * @param callback 回调函数，处理拉取到的消息列表
     */
    @Override
    public void pullMessage(String topic, String group, MessageHeaders headers, Callback callback) {
        this.initialize();

        if (!initialized.get() || closed.get()) {
            logger.warn("Artemis 数据源 [{}] 未初始化或已关闭，无法拉取消息。", name);
            callback.invoke(Collections.emptyList());
            return;
        }
        ClientSession session = null;
        ClientConsumer consumer = null;
        List<ClientMessage> receivedMsgs = new ArrayList<>();
        List<byte[]> bodies = new ArrayList<>();
        try {
            session = sessionFactory.createSession(
                    properties.getUsername(),
                    properties.getPassword(),
                    false, // xa = false
                    true,  // autoCommitSends = true
                    false, // **autoCommitAcks = false (强制客户端手动确认)**
                    false, // preAcknowledge = false
                    0      // **ackBatchSize = 0 (每次 acknowledge() 调用都立即发送)**
            );
            session.start();
            String consumerQueueName = getSharedConsumerQueueName(topic, group);
            ensureSharedQueueExists(session, topic, consumerQueueName);

            consumer = session.createConsumer(consumerQueueName);

            final int maxBatchSize = 20;
            final long singleReceiveTimeout = 3000;

            for (int i = 0; i < maxBatchSize; i++) {
                ClientMessage message = consumer.receive(singleReceiveTimeout);
                if (message == null) {
                    break;
                }
                // 创建一个与消息体长度相同的 byte 数组
                byte[] body = new byte[message.getBodySize()];
                //将消息体内容读取到 byte 数组中
                message.getBodyBuffer().readBytes(body);
                receivedMsgs.add(message); // 存储原始消息对象
                bodies.add(body);
            }

            if (!receivedMsgs.isEmpty()) {
                // 重要：只有当回调成功处理消息后才确认。
                // 如果回调抛出异常，这些消息将不被确认。
                callback.invoke(bodies);
                // 如果回调完成时没有抛出异常，则确认所有接收到的消息。
                // 回调本身负责在其逻辑内部进行确认。
                // 在批量处理成功后统一确认。
                for (ClientMessage msg : receivedMsgs) {
                    try {
                        msg.acknowledge();
                    } catch (ActiveMQException ackEx) {
                        logger.error("主题 [{}] 回调成功后，消息确认失败：{}", topic, ackEx.getMessage(), ackEx);
                    }
                }
                logger.debug("在源 [{}] 上主题 [{}] 回调后成功确认 {} 条消息。", receivedMsgs.size(), topic, name);
            } else {
                callback.invoke(Collections.emptyList()); // 如果没有拉取到消息，回调空列表
            }

        } catch (Exception e) {
            logger.error("从源 [{}] 上的主题 [{}] 提取消息时出错。此方法不会确认消息。", topic, name, e);
            // 发生异常时，不执行确认。消息将在连接关闭或超时后重新投递。
        } finally {
            if (consumer != null) {
                try {
                    consumer.close();
                } catch (ActiveMQException ignored) { /* ignore */ }
            }
            if (session != null) {
                try {
                    session.close();
                } catch (ActiveMQException ignored) { /* ignore */ }
            }
        }
    }

    @Override
    public void addMessageListener(String topic, String group, MqMessageListener listener) {
        this.initialize();

        if (!initialized.get() || closed.get()) {
            logger.warn("Artemis 数据源 [{}] 未初始化或已关闭，无法添加消息侦听器。", name);
            return;
        }

        String listenerKey = topic + "_" +group;
        MqMessageListenerWrapper wrapper = listenerMap.computeIfAbsent(listenerKey, k -> {
            MqMessageListenerWrapper newWrapper = new MqMessageListenerWrapper(
                    topic, group, this, listenerThreadPool,
                    properties.getUsername(),
                    properties.getPassword()
            );
            listenerThreadPool.submit(newWrapper);
            return newWrapper;
        });

        wrapper.addListener(listener);
        logger.info("为 source[{}] topic[{}]、group[{}] 注册的侦听器。此组的侦听器总数： {}",
                name, topic, group, wrapper.getListenerCount());
    }


    public void removeMessageListener(String topic, String group, MqMessageListener listener) {
        String listenerKey = topic + "_" + group;
        MqMessageListenerWrapper wrapper = listenerMap.get(listenerKey);
        if (wrapper != null) {
            wrapper.removeListener(listener);
            if (wrapper.getListenerCount() == 0) {
                wrapper.stopConsuming();
                listenerMap.remove(listenerKey);
                logger.info("删除了 source[{}] topic[{}]、consumerGroup[{}] 的所有侦听器，停止使用者。",
                        name, topic, group);
            } else {
                logger.info("删除了 source[{}] topic[{}]、consumerGroup[{}] 的侦听器。剩余侦听器数:{}",
                        name, topic, group, wrapper.getListenerCount());
            }
        }
    }

    @Override
    public void close() throws Exception {
        if (closed.compareAndSet(false, true)) { // 确保只关闭一次
            logger.info("关闭 Artemis 数据源 [{}]", name);

            // 停止所有消息监听器
            listenerMap.values().forEach(MqMessageListenerWrapper::stopConsuming);
            listenerMap.clear();

            // 优雅关闭线程池
            listenerThreadPool.shutdown();
            try {
                if (!listenerThreadPool.awaitTermination(30, TimeUnit.SECONDS)) {
                    listenerThreadPool.shutdownNow();
                    if (!listenerThreadPool.awaitTermination(30, TimeUnit.SECONDS)) {
                        logger.warn("Listener thread pool for [{}] did not terminate.", name);
                    }
                }
            } catch (InterruptedException ie) {
                listenerThreadPool.shutdownNow();
                Thread.currentThread().interrupt();
            }

            if (sessionFactory != null) {
                try {
                    sessionFactory.close();
                } catch (Exception e) {
                    logger.warn("关闭 Artemis 源 [{}] 的 sessionFactory 时出错：{}", name, e.getMessage());
                }
            }
            if (serverLocator != null) {
                try {
                    serverLocator.close();
                } catch (Exception e) {
                    logger.warn("关闭 Artemis 源 [{}] 的 serverLocator 时出错：{}", name, e.getMessage());
                }
            }
            logger.info("Artemis 数据源 [{}] 已关闭。", name);
            initialized.set(false);
        }
    }

    /**
     * 内部方法：确保共享队列存在并绑定到 Multicast 地址。
     * 这个方法会在第一次有消费者连接到某个主题的共享队列时被调用。
     *
     * @param session           客户端会话
     * @param addressName       主题地址名称 (即 topic)
     * @param queueName         共享队列名称
     * @throws ActiveMQException 如果创建队列失败
     */
    private void ensureSharedQueueExists(ClientSession session, String addressName, String queueName) throws ActiveMQException {
        // 检查队列是否存在，避免重复创建（Artemis 内部会处理重复创建同名队列的逻辑，但显式检查更清晰）
        // 注意：Artemis 2.x API 检查队列是否存在需要通过管理 API 或服务器端配置。
        // 在客户端，最简单的方式是尝试创建，如果已存在，它会返回已有的队列。
        // 对于共享队列，重要的是 address 和 queue name 的组合。
        // 如果 queueName 已经存在且是 ANYCAST 类型，再创建 MULTICAST 会失败。
        // 所以我们确保用 MULTICAST RoutingType 创建。

        // QueueConfiguration 用于创建非持久共享队列
        // Durable = false: 非持久队列，消费者全部断开后消息会丢失
        // AutoCreated = true: 如果队列不存在则自动创建
        // PurgeOnNoConsumers = true: 没有消费者时自动清空队列（对于共享订阅通常设置为 false，让消息保留）
        // 注意：对于你“同一主题消息不能重复消费”的需求，本质上是在一个消费者组内竞争消费。
        // Durable = false 意味着当所有消费者都断开连接后，队列会自动删除。
        // 要将 durable 设置为 true，并考虑消息过期策略。
        // Multicast: 确保是主题模式
        QueueConfiguration queueConfig = new QueueConfiguration(queueName)
                .setAddress(addressName)
                .setRoutingType(RoutingType.MULTICAST) // 绑定到多播地址 (主题)
                .setDurable(true) // 持久队列
                .setPurgeOnNoConsumers(false) // 即使没有消费者，也不清空队列中的消息 (如果希望消息在有消费者重新连接时继续被消费)
                .setAutoCreated(true); // 自动创建队列

        try {
            session.createQueue(queueConfig);
            logger.info("确保主题“{}”的共享队列“{}”存在。", queueName, addressName);
        } catch (ActiveMQException e) {
            // 如果队列已存在且配置不匹配，这里可能会抛出异常
            // 正常情况下，如果队列配置正确，且是共享队列，不会重复创建
            if (e.getType() == ActiveMQExceptionType.QUEUE_EXISTS) {
                logger.info("Shared queue '{}' for topic '{}' already exists.", queueName, addressName);
            } else {
                logger.error("Failed to ensure shared queue '{}' for topic '{}': {}", queueName, addressName, e.getMessage());
                throw e;
            }
        }
    }


    private class MqMessageListenerWrapper implements Runnable, MessageHandler {
        private final String topic;
        private final String consumerGroupName;
        private final ClientSessionFactory sessionFactory;
        private ClientSession session;
        private ClientConsumer consumer;
        private final List<MqMessageListener> listeners = new CopyOnWriteArrayList<>();
        private final AtomicBoolean running = new AtomicBoolean(true);
        private final ExecutorService consumerExecutor;
        private final String username;
        private final String password;

        public MqMessageListenerWrapper(String topic, String consumerGroupName, ArtemisDataSource dataSource,
                                        ExecutorService consumerExecutor, String username, String password) {
            this.topic = topic;
            this.consumerGroupName = consumerGroupName;
            this.sessionFactory = dataSource.sessionFactory;
            this.consumerExecutor = consumerExecutor;
            this.username = username;
            this.password = password;
        }

        public void addListener(MqMessageListener listener) {
            if (!listeners.contains(listener)) {
                listeners.add(listener);
            }
        }

        public void removeListener(MqMessageListener listener) {
            listeners.remove(listener);
        }

        public int getListenerCount() {
            return listeners.size();
        }

        public void stopConsuming() {
            if (running.compareAndSet(true, false)) {
                logger.info("Stopping consumer for topic[{}], consumerGroup[{}]...", topic, consumerGroupName);
                closeConsumerResources();
            }
        }

        private void closeConsumerResources() {
            if (consumer != null) {
                try {
                    consumer.close();
                    consumer = null;
                } catch (ActiveMQException ignored) { /* ignore */ }
            }
            if (session != null) {
                try {
                    session.close();
                    session = null;
                } catch (ActiveMQException ignored) { /* ignore */ }
            }
            logger.info("Consumer resources for topic[{}], consumerGroup[{}] closed.", topic, consumerGroupName);
        }

        @Override
        public void run() {
            String consumerQueueName = getSharedConsumerQueueName(topic, consumerGroupName);
            logger.info("Starting consumer thread for topic[{}], consumerGroup[{}] on shared queue[{}]", topic, consumerGroupName, consumerQueueName);

            while (running.get() && !closed.get()) {
                try {
                    session = sessionFactory.createSession(
                            username,
                            password,
                            false, // xa = false
                            true,  // autoCommitSends = true
                            false, // **autoCommitAcks = false (强制客户端手动确认)**
                            false, // preAcknowledge = false
                            0      // **ackBatchSize = 0 (每次 acknowledge() 调用都立即发送)**
                    );
                    session.start();

                    ensureSharedQueueExists(session, topic, consumerQueueName);

                    consumer = session.createConsumer(consumerQueueName);
                    consumer.setMessageHandler(this); // 将此实例设置为消息处理器

                    // 在消费者活动时保持线程活跃
                    while (running.get() && !session.isClosed() && !closed.get()) {
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }

                } catch (ActiveMQException e) {
                    if (running.get() && !closed.get()) {
                        logger.error("Artemis topic[{}]、consumerGroup[{}]的消费者错误：{}。在 5 秒后重试...",
                                topic, consumerGroupName, e.getMessage(), e);
                        closeConsumerResources();
                        try {
                            Thread.sleep(5000); // 等待后重试
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    } else {
                        logger.info("Artemis topic[{}]、consumerGroup[{}]的消费者正常停止或数据源关闭。", topic, consumerGroupName);
                        break;
                    }
                } catch (Exception e) {
                    logger.error("Artemis topic[{}]、consumerGroup[{}]的使用者线程出现意外错误：{}", topic, consumerGroupName, e.getMessage(), e);
                    if (running.get() && !closed.get()) {
                        closeConsumerResources();
                        try {
                            Thread.sleep(5000); // 等待后重试
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    } else {
                        break;
                    }
                } finally {
                    if (!running.get() || closed.get()) {
                        closeConsumerResources(); // 如果线程停止或数据源关闭，确保资源关闭
                    }
                }
            }
            logger.info("Artemis source[{}] topic[{}]， consumerGroup[{}] 的使用者线程已停止。", name, topic, consumerGroupName);
        }

        @Override
        public void onMessage(ClientMessage message) {
            // 在单独的线程池中处理消息，避免阻塞 Artemis 内部的 IO 线程
            consumerExecutor.submit(() -> {
                try {
                    byte[] body = new byte[message.getBodyBuffer().readableBytes()];
                    message.getBodyBuffer().readBytes(body);

                    // 关键变更：手动构建 MessageHeaders，因为 ClientMessage 没有 getProperties()
                    Map<String, Object> propsMap = new HashMap<>();
                    // 遍历所有属性名，并获取属性值
                    Set<SimpleString> propertyNames = message.getPropertyNames(); // 获取所有属性名
                    if (propertyNames != null) {
                        for (SimpleString propName : propertyNames) {
                            propsMap.put(propName.toString(), message.getObjectProperty(propName));
                        }
                    }
                    MessageHeaders headers = new MessageHeaders(propsMap);
                    // 通知所有注册的侦听器，并传递原始 ClientMessage
                    boolean success = true; // 标志位，用于跟踪所有侦听器是否都成功处理
                    for (MqMessageListener l : listeners) {
                        try {
                            l.onMessage(headers, body); // 传递 ClientMessage
                        } catch (Exception ex) {
                            logger.warn("Listener error for topic [{}], consumerGroup [{}]: {}", topic, consumerGroupName, ex.getMessage(), ex);
                            success = false; // 如果任何侦听器抛出异常，则标记为失败
                        }
                    }
                    // 仅当所有侦听器都成功处理后才确认消息。
                    if (success) {
                        try {
                            message.acknowledge();
                            logger.debug("侦听器成功确认 topic[{}]， consumerGroup[{}] 的消息。", topic, consumerGroupName);
                        } catch (ActiveMQException ackEx) {
                            logger.error("成功处理 topic[{}]、consumerGroup[{}]： {} 的侦听器后，无法确认消息", topic, consumerGroupName, ackEx.getMessage(), ackEx);
                        }
                    } else {
                        // 任何侦听器失败，记录日志，并且消息将不会被确认，从而导致重新投递。
                        logger.warn("由于侦听器处理失败，未确认 topic[{}]、consumerGroup[{}] 的消息，将重新交付。", topic, consumerGroupName);
                        // TODO：如果你希望在失败时明确将其移至死信队列 (DLQ) 或丢弃，使用 negativeAcknowledge。
                        // try { message.negativeAcknowledge(); } catch (ActiveMQException nackEx) { logger.error("Failed to negative acknowledge: ", nackEx); }
                    }

                } catch (Exception ex) {
                    // 此错误表示在侦听器处理之前就出现了问题。消息不应被确认。
                    logger.error("Error preparing message for topic[{}], consumerGroup[{}]: {}", topic, consumerGroupName, ex.getMessage(), ex);
                }
            });
        }
    }

    /**
     * 根据主题和消费者组名生成唯一的共享队列名称。
     * 这确保了不同进程使用相同的共享队列名称来竞争消费。
     */
    private String getSharedConsumerQueueName(String topic, String consumerGroupName) {
        // 加上数据源名称，避免不同数据源配置到同一个 Artemis 实例时冲突
        return String.format("%s.shared_consumer.%s.%s", name, topic, consumerGroupName);
    }
}
