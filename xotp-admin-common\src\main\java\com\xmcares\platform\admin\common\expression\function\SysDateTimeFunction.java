/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：4/24/23
 */
package com.xmcares.platform.admin.common.expression.function;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Map;

/**
 * 系统日期时间获取的函数
 * 格式：$SYS_DT{yyyy-MM-dd hh:mm:ss, 1d}
 * 实例
 * <AUTHOR>
 * @since 1.4.1
 */
public class SysDateTimeFunction extends FmtDateTimeFunction {

    public final static String DEFAULT_FUNC_NAME = "SYS_DT";
    private String name = DEFAULT_FUNC_NAME;



    @Override
    public String execute(Map<String, Object> environment, String ... args) {
        LocalDateTime now = LocalDateTime.now();
        String format = args.length > 0 ? args[0] : DEFAULT_PARAM_FORMAT;
        String diff = args.length > 1 ? args[1] : DEFAULT_PARAM_AMOUNT;
        return this.executeInternal(now, format, diff);
    }


    public void setName(String name) {
        this.name = name;
    }
    @Override
    public String getName() {
        return this.name;
    }


    public static void main(String[] args) {
        SysDateTimeFunction function = new SysDateTimeFunction();
        Map<String, Object> environment = Collections.emptyMap();
        System.out.println(function.execute(environment,"yyyy-MM-dd hh:mm:ss"));
        System.out.println(function.execute(environment,"yyyy-MM-dd hh:mm:ss", "1d"));
        System.out.println(function.execute(environment,"yyyy-MM-dd hh:mm:ss", "1h"));
        System.out.println(function.execute(environment,"yyyy-MM-dd 00:00:00", "-1d"));
        System.out.println(function.execute(environment,"yyyy-MM-dd", "1"));
        System.out.println(function.execute(environment,"yyyy-MM-dd", "-10d"));

    }
}
