package com.xmcares.platform.admin.dataservice.dataset.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/9/21 13:53
 */
public class MetaDatasetVO implements Serializable {
  private String id;

  private Date createTime;

  private Date updateTime;

  private String deleted;

  private String datasetId;

  private String datasourceId;

  private String datasourceName;

  private String datasourceType;

  private String datasetName;

  private String datasetCode;

  private String datasetSql;

  private Integer datasetPublished;

  private String serviceApi;
  private String serviceId;
  private String serviceName;
  private String serviceUri;

  private String datasetParameter;
  private Integer datasetMode;

  private List<MetaDatasetVO> publishedDatasets;

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public String getDeleted() {
    return deleted;
  }

  public void setDeleted(String deleted) {
    this.deleted = deleted;
  }

  public String getDatasetId() {
    return datasetId;
  }

  public void setDatasetId(String datasetId) {
    this.datasetId = datasetId;
  }

  public String getDatasourceId() {
    return datasourceId;
  }

  public void setDatasourceId(String datasourceId) {
    this.datasourceId = datasourceId;
  }

  public String getDatasourceName() {
    return datasourceName;
  }

  public void setDatasourceName(String datasourceName) {
    this.datasourceName = datasourceName;
  }

  public String getDatasourceType() {
    return datasourceType;
  }

  public void setDatasourceType(String datasourceType) {
    this.datasourceType = datasourceType;
  }

  public String getDatasetName() {
    return datasetName;
  }

  public void setDatasetName(String datasetName) {
    this.datasetName = datasetName;
  }

  public String getDatasetCode() {
    return datasetCode;
  }

  public void setDatasetCode(String datasetCode) {
    this.datasetCode = datasetCode;
  }

  public String getDatasetSql() {
    return datasetSql;
  }

  public void setDatasetSql(String datasetSql) {
    this.datasetSql = datasetSql;
  }

  public Integer getDatasetPublished() {
    return datasetPublished;
  }

  public void setDatasetPublished(Integer datasetPublished) {
    this.datasetPublished = datasetPublished;
  }

  public String getServiceId() {
    return serviceId;
  }

  public void setServiceId(String serviceId) {
    this.serviceId = serviceId;
  }

  public String getServiceApi() {
    return serviceApi;
  }

  public void setServiceApi(String serviceApi) {
    this.serviceApi = serviceApi;
  }

  public String getServiceName() {
    return serviceName;
  }

  public void setServiceName(String serviceName) {
    this.serviceName = serviceName;
  }

  public String getServiceUri() {
    return serviceUri;
  }

  public void setServiceUri(String serviceUri) {
    this.serviceUri = serviceUri;
  }

  public String getDatasetParameter() {
    return datasetParameter;
  }

  public void setDatasetParameter(String datasetParameter) {
    this.datasetParameter = datasetParameter;
  }

  public Integer getDatasetMode() {
    return datasetMode;
  }

  public void setDatasetMode(Integer datasetMode) {
    this.datasetMode = datasetMode;
  }

  public List<MetaDatasetVO> getPublishedDatasets() {
    return publishedDatasets;
  }

  public void setPublishedDatasets(List<MetaDatasetVO> publishedDatasets) {
    this.publishedDatasets = publishedDatasets;
  }
}
