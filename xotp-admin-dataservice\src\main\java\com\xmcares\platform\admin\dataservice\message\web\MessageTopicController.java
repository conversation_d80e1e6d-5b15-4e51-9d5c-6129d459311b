package com.xmcares.platform.admin.dataservice.message.web;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.framework.sharing.reference.resource.AppResource;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.dataservice.message.service.MessageTopicService; // 确保导入正确的服务类
import com.xmcares.platform.admin.dataservice.message.vo.MessageTopicAuthAppVO;
import com.xmcares.platform.admin.dataservice.message.vo.MessageTopicVO; // 对应的VO类
import com.xmcares.platform.admin.metadata.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 消息主题控制器
 * <AUTHOR>
 * @Date 2025/5/26
 */
@Api(value = "消息主题控制器")
@Validated
@RestController
@RequestMapping(value = "${xbdp.api.dataservice:/dataservice}/messageTopic", produces = "application/json")
public class MessageTopicController {
    private Logger logger = LoggerFactory.getLogger(MessageTopicController.class);

    @Autowired
    private MessageTopicService messageTopicService; // 注入消息主题服务


    @ApiOperation("消息主题分页展示")
    @GetMapping("/page-query")
    @ResponseBody
    public Page<MessageTopicVO> page(@RequestParam(name = "page", defaultValue = "1") Integer page,
                                     @RequestParam(name = "rows", defaultValue = "10") Integer row,
                                     @RequestParam(name = "topicName", required = false) String topicName,
                                     @RequestParam(name = "remark", required = false) String remark
    ) {
        Pagination pagination = new Pagination();
        pagination.setPageNo(page);
        pagination.setPageSize(row);
        MessageTopicVO messageTopicVO = new MessageTopicVO();
        messageTopicVO.setTopicName(topicName);
        messageTopicVO.setRemark(remark);
        return messageTopicService.messageTopicPageQuery(messageTopicVO, pagination);
    }


    @ApiOperation("保存消息主题")
    @PostMapping("/save")
    @ResponseBody
    public Boolean save(@RequestBody MessageTopicVO messageTopicVO) {
        try {
            boolean flag = messageTopicService.save(messageTopicVO);
            return flag;
        } catch (Exception e) {
            logger.error("保存消息主题失败", e);
            throw new BusinessException(e.getMessage());
        }
    }

    @ApiOperation("消息主题删除")
    @PostMapping("/delete")
    @ResponseBody
    public Boolean deleteMessageTopics(
            @RequestParam(name = "topicId") String topicId
    ) {
        return messageTopicService.deleteMessageTopics(topicId);
    }


    @ApiOperation("应用授权")
    @GetMapping("/auth")
    @ResponseBody
    public Boolean auth(
            @RequestParam(name = "appIdList") List<String> appIdList,
            @RequestParam(name = "topicIdList") List<String> topicIdList) {

        boolean success = false;
        try {
            if (appIdList != null && !appIdList.isEmpty() && topicIdList != null && !topicIdList.isEmpty()) {
                // 调用服务层进行插入
                success = messageTopicService.appAuthService(appIdList, topicIdList);
            } else {
                throw new IllegalArgumentException("appIdList 或 topicIdList 不能为空");
            }
        } catch (Exception e) {
            success = false;
            logger.error("应用授权失败", e);
        }
        return success;
    }

    @ApiOperation("主题授权")
    @GetMapping("/topicAuth")
    @ResponseBody
    public Boolean topicAuth(
            @RequestParam(name = "appIdList") List<String> appIdList,
            @RequestParam(name = "topicIdList") List<String> topicIdList) {

        boolean success = false;
        try {
            if (appIdList != null && !appIdList.isEmpty() && topicIdList != null && !topicIdList.isEmpty()) {
                // 调用服务层进行插入
                success = messageTopicService.appAuthTopic(appIdList, topicIdList);
            } else {
                throw new IllegalArgumentException("appIdList 或 topicIdList 不能为空");
            }
        } catch (Exception e) {
            success = false;
            logger.error("主题授权失败", e);
        }
        return success;
    }

    @ApiOperation("主题已授权应用列表")
    @GetMapping("/apps")
    @ResponseBody
    public Result<List<MessageTopicAuthAppVO>> getAppList(@RequestParam(name = "topicId") String topicId) {
        boolean success = false;
        String message = "";
        List<MessageTopicAuthAppVO> appList = null;
        try {
            appList = messageTopicService.getAppResourceByRelService(topicId);
            success = true;

        } catch (Exception e) {
            success = false;
            message = e.getMessage();
            logger.error("主题已授权应用列表获取失败", e);
        }
        return new Result<>(success, message, appList);
    }


    @ApiOperation("应用已授权主题列表")
    @GetMapping("/topics")
    @ResponseBody
    public Result<List<MessageTopicAuthAppVO>> getTopicList(@RequestParam(name = "appId") String appId) {
        boolean success = false;
        String message = "";
        List<MessageTopicAuthAppVO> appList = null;
        try {
            appList = messageTopicService.getTopicResourceByRelService(appId);
            success = true;

        } catch (Exception e) {
            success = false;
            message = e.getMessage();
            logger.error("应用已授权主题列表获取失败", e);
        }
        return new Result<>(success, message, appList);
    }


    @ApiOperation("通过主题ID获取消息主题对象")
    @GetMapping("/id-get")
    @ResponseBody
    public Result<MessageTopicVO> getMessageTopicById(
            @RequestParam(name = "topicId") String topicId
    ) {
        return messageTopicService.getMessageTopicById(topicId);
    }

    @ApiOperation("模型主题名唯一性验证")
    @GetMapping("/unique-check")
    @ResponseBody
    public Result<Boolean> nameUniqueCheck(
            @RequestParam(name = "topicName") String topicName
    ) {
        return messageTopicService.nameUniqueCheck(topicName);
    }
}
