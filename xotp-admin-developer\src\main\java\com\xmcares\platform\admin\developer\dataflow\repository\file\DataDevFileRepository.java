package com.xmcares.platform.admin.developer.dataflow.repository.file;

import com.xmcares.framework.fsclient.FSTemplate;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.platform.admin.common.errors.SystemException;
import com.xmcares.platform.admin.developer.common.config.DeveloperProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;

@Repository
public class DataDevFileRepository {

    @Autowired
    private FSTemplate fsTemplate;
    @Autowired
    private DeveloperProperties properties;

    public String save(String filename, String context) {
        String filePath = properties.getFileServerDir() + "/dataflow/" + filename;
        try (ByteArrayInputStream input = new ByteArrayInputStream(context.getBytes(StandardCharsets.UTF_8))) {
            fsTemplate.saveFile(new FileDesc.FileDescImpl(null,filePath), input);
            return filePath;
        } catch (Exception e) {
            throw new SystemException(
                    String.format("保存数据开发任务[%s]文件异常", filename), e);
        }
    }

}
