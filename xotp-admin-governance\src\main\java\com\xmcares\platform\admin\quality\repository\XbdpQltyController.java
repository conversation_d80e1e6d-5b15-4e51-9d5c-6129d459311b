package com.xmcares.platform.admin.quality.repository;

import com.xmcares.platform.admin.common.vo.ReturnT;
import com.xmcares.platform.admin.quality.model.XxlJobGroup;
import com.xmcares.platform.admin.quality.model.XxlJobInfo;
import com.xmcares.platform.admin.quality.model.XxlJobLog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/16 09:19
 */
@FeignClient(name = "${xbdp.feign.scheduler-service.name:scheduler-service}",
        url = "${xbdp.feign.scheduler-service.url:}"
)
public interface XbdpQltyController {

    @RequestMapping("/xbdp/qlty/findJobLogByJobId")
    @ResponseBody
    public List<XxlJobLog> findJobLogByJobId(
            @RequestParam(name = "jobId") String jobId,
            @RequestParam(name = "startTriggerTime") String startTriggerTime,
            @RequestParam(name = "endTriggerTime") String endTriggerTime,
            @RequestParam(name = "pageNo") Integer pageNo,
            @RequestParam(name = "pageSize") Integer pageSize
    );


    @RequestMapping("/xbdp/qlty/findJobByExecuteHandler")
    @ResponseBody
    public List<Integer> findJobByExecuteHandler(
            @RequestParam(name = "executeHandler") String executeHandler,
            @RequestParam(name = "pageNo") Integer pageNo,
            @RequestParam(name = "pageSize") Integer pageSize
    );

    @RequestMapping("/xbdp/qlty/countJobLogSizeByJobId")
    @ResponseBody
    public int countJobLogSizeByJobId(
            @RequestParam(name = "jobId") String jobId,
            @RequestParam(name = "startTriggerTime") String startTriggerTime,
            @RequestParam(name = "endTriggerTime") String endTriggerTime
    );


    @RequestMapping("/xbdp/qlty/logDetailCat")
    @ResponseBody
    public String logDetailPage(@RequestParam(name = "executorAddress") String executorAddress, @RequestParam(name = "triggerTime") long triggerTime, @RequestParam(name = "logId") long logId, @RequestParam(name = "fromLineNum") int fromLineNum);


    @RequestMapping("/xbdp/qlty/saveJobInfo")
    @ResponseBody
    public int saveJobInfo(@RequestBody XxlJobInfo info);


    @RequestMapping("/xbdp/qlty/loadJobInfoById")
    @ResponseBody
    public XxlJobInfo loadById(@RequestParam(name = "id") int id);


    @RequestMapping("/xbdp/qlty/stop")
    @ResponseBody
    public ReturnT<String> pause(@RequestParam(name = "id") int id) ;

    @RequestMapping("/xbdp/qlty/start")
    @ResponseBody
    public ReturnT<String> start(@RequestParam(name = "id") int id) ;

    @RequestMapping("/xbdp/qlty/trigger")
    @ResponseBody
    public ReturnT<String> triggerJob(@RequestParam(name = "id") int id, @RequestParam(name = "executorParam") String executorParam, @RequestParam(name = "addressList") String addressList) ;

    @RequestMapping("/xbdp/qlty/deleteJobInfo")
    @ResponseBody
    public int delete(@RequestParam(name = "id") long id);

    @RequestMapping("/xbdp/qlty/findJobGroupByAppname")
    @ResponseBody
    XxlJobGroup findByAppname(@RequestParam(name = "name") String name) ;

    @RequestMapping("/xbdp/qlty/updateJobInfo")
    @ResponseBody
    public int update(@RequestBody XxlJobInfo xxlJobInfo);

    @GetMapping("/xbdp/qlty/findJobLogByArchiveResultIds")
    @ResponseBody
    List<XxlJobLog> findJobLogByArchiveResultIds(@RequestParam(name = "dispatchIds") List<String> dispatchIds, @RequestParam(name = "archiveResultIds") List<String> archiveResultIds);
}
