package com.xmcares.platform.admin.developer.common.enums;

import com.xmcares.platform.admin.common.util.SysCommonConstant;
import com.xmcares.platform.admin.integrator.common.util.XxlJobTaskType;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 数据开发任务类型定义
 * 批处理任务必须以 BATCH开头
 * 流处理任务必须以 STREAM开头
 */
public enum DeveloperTaskType {


    /** SQL批处理任务 */
    BATCH_SQL(XxlJobTaskType.DEV_BATCH, "BatchJdbcSqlJobHandler", "SQL批处理任务"),
    STREAM_FLINK(XxlJobTaskType.DEV_STREAM,"StreamFlinkJobHandler", "Flink流处理任务"),
    SYS_GLOBAL_PARAM(XxlJobTaskType.DEV_BATCH,"DevSystemHandler", "全局参数"),
    SYS_BEGIN(XxlJobTaskType.DEV_BATCH, "DevSystemHandler","开始节点"),
    SYS_END(XxlJobTaskType.DEV_BATCH, "DevSystemHandler","结束节点")
    ;

    public static final String BATCH = SysCommonConstant.BATCH;
    public static final String STREAM = SysCommonConstant.STREAM;
    public static final String SYSTEM = SysCommonConstant.SYSTEM;

    /** 任务中文标识 */
    private String title;
    /** 任务的Handler名称 */
    private String taskHandler;
    /** 任务执行方式 */
    private XxlJobTaskType executeType;

    DeveloperTaskType(XxlJobTaskType executeType,String taskHandler, String title) {
        this.executeType = executeType;
        this.title = title;
        this.taskHandler = taskHandler;
    }


    public String getTitle() {
        return title;
    }

    public XxlJobTaskType getExecuteType() {
        return executeType;
    }

    public String getTaskHandler() {
        return taskHandler;
    }

    public static DeveloperTaskType match(String type) {
        if (StringUtils.isEmpty(type)) { return null; }
        try {
            return DeveloperTaskType.valueOf(type.toUpperCase());
        } catch (Exception e) {
            return null;
        }
    }

    public static List<String> batchTasks() {
        return Stream.of(DeveloperTaskType.values()).filter(type-> type.executeType.equals(XxlJobTaskType.DEV_BATCH) && type != DeveloperTaskType.SYS_GLOBAL_PARAM)
                .map(Enum::name)
                .collect(Collectors.toList());
    }

}
