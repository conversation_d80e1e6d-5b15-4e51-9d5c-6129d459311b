package com.xmcares.platform.admin.quality.model.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/12 10:17
 */
public class QualitySummaryDatawareScoreQueryVO implements Serializable {

    private List<Map<String, Object>> datawareDimSocre;
    private List<Map<String, Object>> datawareAllSocre;

    public List<Map<String, Object>> getDatawareDimSocre() {
        return datawareDimSocre;
    }

    public void setDatawareDimSocre(List<Map<String, Object>> datawareDimSocre) {
        this.datawareDimSocre = datawareDimSocre;
    }

    public List<Map<String, Object>> getDatawareAllSocre() {
        return datawareAllSocre;
    }

    public void setDatawareAllSocre(List<Map<String, Object>> datawareAllSocre) {
        this.datawareAllSocre = datawareAllSocre;
    }
}
