package com.xmcares.platform.admin.integrator.common.util;

/**
 * 集成方式
 *
 * <AUTHOR> chenYG
 * @date : 2022/3/29 15:24
 */
public enum IntegratorMod {

    /**
     * 插件
     */
    PLUGIN("插件", "1"),
    /**
     * 系统
     */
    SYS("内置", "0"),
    /**
     * 无
     */
    NONE("无", "2");
    private final String title;
    private final String value;

    IntegratorMod(String title, String value) {
        this.title = title;
        this.value = value;
    }

    public String getTitle() {
        return title;
    }

    public String getValue() {
        return value;
    }

    public static IntegratorMod match(String value, IntegratorMod def) {
        IntegratorMod result = def;
        try {
            result = IntegratorMod.valueOf(value);
        } catch (Exception e) {
            for (IntegratorMod mod : IntegratorMod.values()) {
                if (mod.value.equals(value)) {
                    result = mod;
                    return result;
                }
            }
        }
        return result;
    }

}
