
package com.xmcares.platform.admin.developer.dataflow.repository;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.platform.admin.developer.dataflow.model.DevDataflowProcess;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/06/15 13:48:18
 * @version 1.0.0
 * @Description zhanlh 增加一个根据流程定义id查询运行实例
 */
@Repository
public class DevDataflowInstanceRepository {

    public static final String TABLE_BDP_DEV_DATAFLOW_PROCESS = "bdp_dev_dataflow_process";

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    public Boolean add(DevDataflowProcess devDataflowProcess) {
        Map map = DBUtils.insertSqlAndObjects(devDataflowProcess, DevDataflowProcess.class, TABLE_BDP_DEV_DATAFLOW_PROCESS);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.update(sql, args) > 0;
    }

    public Boolean update(DevDataflowProcess devDataflowProcess) {
        Map map = DBUtils.updateSqlAndObjects("id", devDataflowProcess, DevDataflowProcess.class, TABLE_BDP_DEV_DATAFLOW_PROCESS);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.update(sql, args) > 0;
    }

    public boolean delete(String id) {
        String sql = DBUtils.deleteSql(TABLE_BDP_DEV_DATAFLOW_PROCESS, "id");
        xcfJdbcTemplate.update(sql, id);
        return true;
    }

    /**
     * 根据流程定义编号删除
     * @param dataflowId
     * @return
     */
    public boolean deleteByDataFlowId(String dataflowId) {
        String sql = "DELETE from " + TABLE_BDP_DEV_DATAFLOW_PROCESS + " where dataflow_id= ? ";
        return  xcfJdbcTemplate.update(sql, dataflowId)>0;
    }
    /**
     * 查询根据流程定义查询
     * @param dataflowId 流程定义id
     * @param status 查询状态
     * @return
     */
    public List<DevDataflowProcess> queryByDataflowIdAndStatus(String dataflowId,String status) {
        HashMap<String, Object> conditions = new HashMap<>();
        conditions.put("dataflow_id", dataflowId);
        if (!StringUtils.isEmpty(status)) {
            conditions.put("status", status);
        }
        Map<String, Object> map = DBUtils.queryList(TABLE_BDP_DEV_DATAFLOW_PROCESS, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntities(sql, args, DevDataflowProcess.class);
    }

    /**
     * 根据父表ID与状态获取获取数量
     * @param dataflowId 流程定义id
     * @param status 查询状态
     * @return
     */
    public int countByDataflowIdAndStatus(String dataflowId, String status) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("dataflow_id", dataflowId);
        conditions.put("status", status);
        Map<String, Object> map = DBUtils.queryCount(DevDataflowProcess.TABLE, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForObject(sql, Integer.class, args);
    }

    public DevDataflowProcess get(String id) {
        HashMap<String, Object> conditions = new HashMap<>();
        conditions.put("id", String.valueOf(id));
        Map<String, Object> map = DBUtils.queryList(TABLE_BDP_DEV_DATAFLOW_PROCESS, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntity(sql, args, DevDataflowProcess.class);
    }

    /**
     *  分页查询 流程实例
     * @param devDataflowProcess
     * @param page
     * @return
     */
    public List<DevDataflowProcess> queryDevDataflowProcessPage(DevDataflowProcess devDataflowProcess, Page<DevDataflowProcess> page) {
        Map<String, Object> conditions = buildCondition(devDataflowProcess);
        Map<String, Object> map = DBUtils.queryList(DevDataflowProcess.TABLE, conditions);
        String sql = DBUtils.getSql(map) + " ORDER BY update_time Desc ";
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntities(sql, args, page, DevDataflowProcess.class);
    }

    /**
     * 统计
     * @param devDataflowProcess
     * @return
     */
    public int countDevDataflowProcess(DevDataflowProcess devDataflowProcess) {
        Map<String, Object> conditions = buildCondition(devDataflowProcess);
        Map<String, Object> map = DBUtils.queryCount(DevDataflowProcess.TABLE, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForObject(sql, Integer.class, args);
    }
    private Map<String, Object> buildCondition(DevDataflowProcess devDataflowProcess) {
        Map<String, Object> conditions = new HashMap<>();
        if (devDataflowProcess != null) {
            if (devDataflowProcess.getName() != null && !devDataflowProcess.getName().equals("")) {
                conditions.put("name", "%" + devDataflowProcess.getName() + "%");
            }
            if (devDataflowProcess.getRemark() != null && !devDataflowProcess.getRemark().equals("")) {
                conditions.put("remark", "%" + devDataflowProcess.getRemark() + "%");
            }
            if (devDataflowProcess.getDataflowId() != null && !devDataflowProcess.getDataflowId().equals("")) {
                conditions.put("dataflow_id", devDataflowProcess.getDataflowId());
            }
            if (devDataflowProcess.getStatus() != null && !devDataflowProcess.getStatus().equals("")) {
                conditions.put("status", devDataflowProcess.getStatus());
            }
        }
        return conditions;
    }
}
