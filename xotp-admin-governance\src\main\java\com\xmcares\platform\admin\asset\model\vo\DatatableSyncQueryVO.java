package com.xmcares.platform.admin.asset.model.vo;


import com.xmcares.platform.admin.asset.model.MetaDatatable;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/4 10:05
 */
public class DatatableSyncQueryVO {

    String datawareId;
    String catalogId;

    List<UnSyncDatatable> unSyncList;

    List<MetaDatatable> syncList;

    public static class UnSyncDatatable {
        private String name;
        private String remark;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }
    }


    public List<UnSyncDatatable> getUnSyncList() {
        return unSyncList;
    }

    public void setUnSyncList(List<UnSyncDatatable> unSyncList) {
        this.unSyncList = unSyncList;
    }

    public List<MetaDatatable> getSyncList() {
        return syncList;
    }

    public void setSyncList(List<MetaDatatable> syncList) {
        this.syncList = syncList;
    }

    public String getDatawareId() {
        return datawareId;
    }

    public void setDatawareId(String datawareId) {
        this.datawareId = datawareId;
    }

    public String getCatalogId() {
        return catalogId;
    }

    public void setCatalogId(String catalogId) {
        this.catalogId = catalogId;
    }
}
