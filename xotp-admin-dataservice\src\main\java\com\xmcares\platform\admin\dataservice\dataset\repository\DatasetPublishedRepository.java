package com.xmcares.platform.admin.dataservice.dataset.repository;

import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.dataservice.dataset.vo.MetaDatasetVO;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * @version 1.0
 * @since: 2025-05-19
 */
@Repository
public class DatasetPublishedRepository {
    public static final String TABLE_BDP_META_DATASET_PUBLISHED = "bdp_api_dataset_service";

    private static final Logger LOG = LoggerFactory.getLogger(ServiceModelRepository.class);

    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    /**
     * 根据数据集ID获取已发布的数据集列表
     * 
     * @param datasetId 数据集ID
     * @return 返回已发布的数据集列表，如果未找到则返回null
     */
    public List<MetaDatasetVO> getByDatasetId(String datasetId) {
        List<MetaDatasetVO> dsList = null;
        try {
            String sql = String.format(
                    "SELECT " +
                            "%1$s.id, " +
                            "%1$s.create_time, " +
                            "%1$s.update_time, " +
                            "%1$s.deleted, " +
                            "%1$s.dataset_id, " +
                            "%1$s.datasource_id, " +
                            "%1$s.dataset_name, " +
                            "%1$s.dataset_code, " +
                            "%1$s.dataset_sql, " +
                            "%1$s.dataset_parameter, " +
                            "%1$s.dataset_published, " +
                            "%1$s.service_id, " +
                            "%1$s.dataset_mode, " +
                            "%2$s.name AS datasource_name, " +
                            "%2$s.type AS datasource_type, " +
                            "%3$s.service_name, " +
                            "%3$s.uri AS service_uri " +
                            "FROM %1$s " +
                            "JOIN %2$s ON %1$s.datasource_id = %2$s.id " +
                            "JOIN %3$s ON %1$s.service_id = %3$s.id " +
                            "WHERE deleted = 0 AND %1$s.dataset_id = ?",
                    TABLE_BDP_META_DATASET_PUBLISHED,
                    ServiceModelRepository.TABLE_BDP_META_DATASOURCE, "sys_service");
            dsList = xcfJdbcTemplate.<MetaDatasetVO>queryForEntities(sql,
                    new Object[] { datasetId },
                    MetaDatasetVO.class);
        } catch (Exception e) {
            LOG.warn(e.toString());
        }
        return dsList;
    }

    /**
     * 根据已发布数据集ID获取数据集信息
     * 
     * @param id 已发布数据集ID
     * @return 返回MetaDatasetVO对象，如果未找到则返回null
     */
    public MetaDatasetVO getDatasetById(String id) {
        MetaDatasetVO metaDatasetVO = null;
        try {
            String sql = String.format(
                    "SELECT " +
                            "%1$s.id, " +
                            "%1$s.create_time, " +
                            "%1$s.update_time, " +
                            "%1$s.deleted, " +
                            "%1$s.datasource_id, " +
                            "%1$s.dataset_name, " +
                            "%1$s.dataset_code, " +
                            "%1$s.dataset_sql, " +
                            "%1$s.dataset_published, " +
                            "%1$s.dataset_parameter, " +
                            "%1$s.dataset_mode, " +
                            "%1$s.service_id, " +
                            "%2$s.name AS datasource_name, " +
                            "%2$s.type AS datasource_type " +
                            "FROM %1$s " +
                            "LEFT JOIN %2$s ON %1$s.datasource_id = %2$s.id " +
                            "WHERE deleted = 0 AND %1$s.id = ?",
                    TABLE_BDP_META_DATASET_PUBLISHED,
                    ServiceModelRepository.TABLE_BDP_META_DATASOURCE);
            metaDatasetVO = xcfJdbcTemplate.<MetaDatasetVO>queryForEntity(sql,
                    new Object[] { id },
                    MetaDatasetVO.class);
        } catch (Exception e) {
            LOG.warn(e.toString());
        }
        return metaDatasetVO;
    }

    /**
     * 创建已发布的数据集
     * 
     * @param datasetVO   数据集信息
     * @param serviceId   服务ID
     * @param datasetCode 数据集代码
     * @return 成功返回1，失败返回-1
     * @throws Exception
     */
    public int createDatasetPublished(MetaDatasetVO datasetVO, String serviceId, String datasetCode) throws Exception {
        if (checkDatasetCode(datasetCode) > 0) {
            throw new Exception("datasetCode 不唯一");
        } else {
            String datasetId = SnowflakeGenerator.getNextId().toString();
            Date nowDate = new Date();
            return xcfJdbcTemplate.update(
                    " INSERT INTO " + TABLE_BDP_META_DATASET_PUBLISHED +
                            "(id, create_time, update_time, deleted, " +
                            "dataset_id, datasource_id, dataset_name, dataset_code, " +
                            "dataset_published,service_id, dataset_sql, dataset_parameter, dataset_mode) " +
                            " VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?) ",
                    datasetId,
                    nowDate,
                    nowDate,
                    "0",
                    datasetVO.getDatasetId(),
                    datasetVO.getDatasourceId(),
                    datasetVO.getDatasetName(),
                    datasetCode,
                    "1",
                    serviceId,
                    datasetVO.getDatasetSql(),
                    datasetVO.getDatasetParameter(),
                    datasetVO.getDatasetMode());
        }
    }

    /**
     * 检查数据集代码是否已存在
     * 
     * @param datasetCode 数据集代码
     * @return 存在返回记录数，不存在返回0，发生错误返回-1
     */
    public Integer checkDatasetCode(String datasetCode) {
        try {
            return xcfJdbcTemplate.<Integer>queryForObject(
                    "SELECT COUNT(*) FROM " + TABLE_BDP_META_DATASET_PUBLISHED
                            + " where deleted = 0 AND dataset_code = ?",
                    Integer.class,
                    datasetCode);
        } catch (Exception e) {
            LOG.warn(e.toString());
            return -1;
        }
    }

    public Integer checkServiceId(String service_id) {
        try {
            return xcfJdbcTemplate.<Integer>queryForObject(
                    "SELECT COUNT(*) FROM " + TABLE_BDP_META_DATASET_PUBLISHED
                            + " where deleted = 0 AND service_id = ?",
                    Integer.class,
                    service_id);
        } catch (Exception e) {
            LOG.warn(e.toString());
            return -1;
        }
    }

    /**
     * 上下线服务
     * 
     * @param id
     * @param published 1 上线 0 下线
     * @return 操作成功返回true，失败返回false
     */
    public boolean updatePublishedByDatasetCode(String datasetCode, int published) {
        if (published != 1) {
            published = 0;
        }
        return xcfJdbcTemplate.update(
                "UPDATE " + TABLE_BDP_META_DATASET_PUBLISHED
                        + " SET dataset_published = ? WHERE deleted = 0 AND dataset_code = ? ",
                published, datasetCode) == 1;
    }

    /**
     * 删除已发布的数据集（逻辑删除）
     * 删除时同时将服务下线（dataset_published设为0）
     * 
     * @param id 已发布数据集ID
     * @return 操作成功返回true，失败返回false
     */
    public boolean deleteById(String id) {
        try {
            return xcfJdbcTemplate.update(
                    "UPDATE " + TABLE_BDP_META_DATASET_PUBLISHED
                            + " SET deleted = ?, dataset_published = ? WHERE id = ? ",
                    1, 0, id) == 1;
        } catch (Exception e) {
            LOG.warn(e.toString());
            return false;
        }
    }
}
