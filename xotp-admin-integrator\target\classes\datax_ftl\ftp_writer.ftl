{
                    "name": "ftpwriter",
                    "parameter": {
                        "protocol": ${destDatasource.protocol},
                        "host": ${destDatasource.host},
                        "port": ${destDatasource.port},
                        "username": ${destDatasource.username},
                        "password": ${destDatasource.password},
                        "timeout": ${destDatasource.timeout},
                        "connectPattern": ${destDatasource.connectPattern},
                        "path": ${destDatasource.path},
                        "fileName": ${destDatasource.fileName},
                        "writeMode": ${destDatasource.writeMode},
                        "fieldDelimiter": ${destDatasource.fieldDelimiter},
                        "encoding": ${destDatasource.encoding},
                        "nullFormat": ${destDatasource.nullFormat},
                        "dateFormat": ${destDatasource.dateFormat},
                        "fileFormat": ${destDatasource.fileFormat},
			            "suffix": ${destDatasource.suffix},
                        "header": [
                            <#list destDatasource.headers as header>
                                ${header}<#if header_has_next>,</#if>
                            </#list>
                        ]
                    }
                }