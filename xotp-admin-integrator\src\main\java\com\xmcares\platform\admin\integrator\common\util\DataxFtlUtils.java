package com.xmcares.platform.admin.integrator.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xmcares.platform.admin.common.errors.BusinessException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.*;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/5 10:06
 */
public class DataxFtlUtils {

    /** 用户自定义插件的模板前缀 */
    public static final String KEY_DATAX_PLUGIN_CUSTOM = "custom";
    public static final String KEY = "key";
    public static final String VALUE = "value";

    /** 声明DataxFtl多项式配置的前缀， 在使用过程中， 程序会将该前缀去除后， 以Key:Value的形式传递给DataxFtl文件模板 */
    public static final String KEY_MULIT_PREX = "mulit-";
    /** 声明数据来源或者数据去向的数据源类型 */
    public static final String KEY_DATASOURCE_TYPE = "type";
    /** 声明数据来源或者数据去向的数据源配置信息 */
    public static final String KEY_DATASOURCE_CONFIG = "options";
    /** 声明DataxFtl通用配置项的公共Key，在DataxFtl文件模板中， 用 {orgin.youKey}的方式获取对应的值 */
    public static final String KEY_DATAX_ORGIN_CONFIG = "orgin";
    /** 声明DataxFtl通用配置项的数据源配置Key，在DataxFtl文件模板中， 用 {orginDatasource.youKey}的方式获取对应的值 */
    public static final String KEY_DATAX_ORGIN_DATASOURCE_CONFIG = "orginDatasource";
    /** 声明DataxFtl中模板的前缀Key，通常根据数据源类型来标识，仅当集成模型为用户自定义时，固定为“custom” */
    public static final String KEY_DATAX_ORGIN_PLUGIN = "readerPlugin";
    /** 声明DataxFtl中用户定义自定义的模板标识符，仅当集成模型为用户自定义时使用 */
    public static final String KEY_DATAX_ORGIN_PLUGIN_NAME = "orginPluginName";
    /** 声明DataxFtl中用户的列为单列时使用的Key，可以通过直接遍历该key获取值 */
    public static final String KEY_DATAX_ORGIN_SIMPLE_COLUMN = "orginFieldNames";
    /** 声明DataxFtl中用户的列为多列时使用的Key，可以通过该key获取一个MAP对象后，遍历MAP来获取Key：Value */
    public static final String KEY_DATAX_ORGIN_MULIT_COLUMN = "orginColumns";
    /** 声明DataxFtl中用户定义自定义的参数配置信息，仅当集成模型为用户自定义时使用 */
    public static final String KEY_DATAX_ORGIN_CUSTOM_CONFIG = "orginParams";

    /** 声明DataxFtl通用配置项的公共Key，在DataxFtl文件模板中， 用 {dest.youKey}的方式获取对应的值 */
    public static final String KEY_DATAX_DEST_CONFIG = "dest";
    /** 声明DataxFtl通用配置项的数据源配置Key，在DataxFtl文件模板中， 用 {destDatasource.youKey}的方式获取对应的值 */
    public static final String KEY_DATAX_DEST_DATASOURCE_CONFIG = "destDatasource";
    /** 声明DataxFtl中模板的前缀Key，通常根据数据源类型来标识，仅当集成模型为用户自定义时，固定为“custom” */
    public static final String KEY_DATAX_DEST_PLUGIN = "writerPlugin";
    /** 声明DataxFtl中用户定义自定义的模板标识符，仅当集成模型为用户自定义时使用 */
    public static final String KEY_DATAX_DEST_PLUGIN_NAME = "destPluginName";
    /** 声明DataxFtl中用户的列为单列时使用的Key，可以通过直接遍历该key获取值 */
    public static final String KEY_DATAX_DEST_SIMPLE_COLUMN = "destFieldNames";
    /** 声明DataxFtl中用户的列为多列时使用的Key，可以通过该key获取一个MAP对象后，遍历MAP来获取Key：Value */
    public static final String KEY_DATAX_DEST_MULIT_COLUMN = "destColumns";
    /** 声明DataxFtl中用户定义自定义的参数配置信息，仅当集成模型为用户自定义时使用 */
    public static final String KEY_DATAX_DEST_CUSTOM_CONFIG = "destParams";


    /**
     * 构建DataxFlt文件内容数据
     * @param entity 数据实体接口
     * @param datasource 数据源数据接口
     * @return DataxFlt文件内容数据
     */
    public static Map<String, Object> buildDataxFtlDatasource(IDataxFtlEntity entity, IDataxFtlDatasource datasource) {
        // 设置模板数据
        Map<String, Object> result = new HashMap<>();
        if (entity.getFromMod() == IntegratorMod.SYS) {
            Map<String, Object> fromSource = datasource.fromDatasource();
            Assert.notNull(fromSource.get(KEY_DATASOURCE_TYPE), "未找到数据来源的数据源类型");
            Assert.notNull(fromSource.get(KEY_DATASOURCE_CONFIG), "未找到数据来源的数据源配置");
            Map<String, Object> fromDataSource = buildSysReader(entity);
            result.put(KEY_DATAX_ORGIN_PLUGIN, fromSource.get(KEY_DATASOURCE_TYPE).toString().toLowerCase());
            result.put(KEY_DATAX_ORGIN_DATASOURCE_CONFIG, JSON.parse(fromSource.get(KEY_DATASOURCE_CONFIG).toString()));
            result.putAll(fromDataSource);
        } else {
            Map<String,Object> fromDataSource = buildCustomReader(entity);
            result.put(KEY_DATAX_ORGIN_PLUGIN_NAME, entity.getOrginPluginName());
            result.put(KEY_DATAX_ORGIN_PLUGIN, KEY_DATAX_PLUGIN_CUSTOM);
            result.putAll(fromDataSource);
        }
        if (entity.getToMod() == IntegratorMod.SYS) {
            Map<String, Object> toSource = datasource.toDatasource();
            Assert.notNull(toSource.get(KEY_DATASOURCE_TYPE), "未找到数据去向的数据源类型");
            Assert.notNull(toSource.get(KEY_DATASOURCE_CONFIG), "未找到数据去向的数据源配置");
            Map<String, Object> toDataSource = buildSysWrite(entity);
            result.put(KEY_DATAX_DEST_PLUGIN, toSource.get(KEY_DATASOURCE_TYPE).toString().toLowerCase());
            result.put(KEY_DATAX_DEST_DATASOURCE_CONFIG, JSON.parse(toSource.get(KEY_DATASOURCE_CONFIG).toString()));
            result.putAll(toDataSource);
        } else {
            Map<String,Object> toDataSource = buildCustomWrite(entity);
            result.put(KEY_DATAX_DEST_PLUGIN_NAME, entity.getDestPluginName());
            result.put(KEY_DATAX_DEST_PLUGIN, KEY_DATAX_PLUGIN_CUSTOM);
            result.putAll(toDataSource);
        }
        return result;
    }

    public static Map<String, Object> buildMulitConfigs(Map<String, Object> orgins) {
        Iterator<Map.Entry<String, Object>> iterator = orgins.entrySet().iterator();
        Map<String, Object> mulitMap = new HashMap<>();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> echo = iterator.next();
            if (echo.getValue() == null || !echo.getKey().startsWith(KEY_MULIT_PREX)) { continue; }
            String[] mulitValues = echo.getValue().toString().split("\n");
            List<Map<String, Object>> inputMulitValues = new ArrayList<>();
            for (String value : mulitValues) {
                if (StringUtils.isNotBlank(value) && value.contains("=")) {
                    String[] kvValues = value.split("=");
                    if (kvValues.length == 2 && StringUtils.isNotBlank(kvValues[0]) && StringUtils.isNotBlank(kvValues[1])) {
                        Map<String,Object> kvMap = new HashMap<>();
                        kvMap.put("key", kvValues[0]);
                        kvMap.put("value", kvValues[1]);
                        inputMulitValues.add(kvMap);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(inputMulitValues)) {
                mulitMap.put(echo.getKey().replace(KEY_MULIT_PREX, ""), inputMulitValues);
            }
            iterator.remove();
        }
        return mulitMap;
    }

    public static Map<String, Object> buildSysReader(IDataxFtlEntity entity) {
        Map<String, Object> result = new HashMap<>();
        result.put(KEY_DATAX_ORGIN_CONFIG, entity.buildOrginMapInfo());
        JSONArray columnArray = entity.getOrginColumnJsonEntities();
        if (CollectionUtils.isEmpty(columnArray)) {
            throw new BusinessException("请设置数据来源列信息");
        }
        JSONObject columnObject = columnArray.getJSONObject(0);
        List<String> columnKeies = new ArrayList<>(columnObject.keySet());
        if (columnObject.size() == 1) {
            List<String> columnValues = new ArrayList<>();
            for (int i = 0; i < columnArray.size(); i++) {
                columnValues.add(columnArray.getJSONObject(i).getString(columnKeies.get(0)));
            }
            result.put(KEY_DATAX_ORGIN_SIMPLE_COLUMN, columnValues);
        } else {
            List<Map<String, Object>> columns = new ArrayList<>();
            for (int i = 0; i < columnArray.size(); i++) {
                JSONObject handlerColumnObject = columnArray.getJSONObject(i);
                List<String> handlerColumnKeies = new ArrayList<>(handlerColumnObject.keySet());
                Map<String, Object> cValues = new HashMap<>();
                for (String handlerColumnKey : handlerColumnKeies) {
                    cValues.put(handlerColumnKey, handlerColumnObject.get(handlerColumnKey));
                }
                columns.add(cValues);
            }
            result.put(KEY_DATAX_ORGIN_MULIT_COLUMN, columns);
        }
        return result;
    }

    public static Map<String, Object> buildCustomReader(IDataxFtlEntity entity) {
        Map<String, Object> result = new HashMap<>();

        Map<String, Object> mapInfo = entity.buildOrginMapInfo();
        List<Map<String, Object>> mapValues = new ArrayList<>();
        mapInfo.forEach((key, value) -> {
            Map<String, Object> map = new HashMap<>();
            map.put(KEY, key);
            map.put(VALUE, value);
            mapValues.add(map);
        });

        // 处理列
        JSONArray columnArray = entity.getOrginColumnJsonEntities();
        result.put(KEY_DATAX_ORGIN_MULIT_COLUMN, columnArray);
        result.put(KEY_DATAX_ORGIN_CUSTOM_CONFIG, mapValues);
        return result;
    }

    public static Map<String, Object> buildSysWrite(IDataxFtlEntity entity) {
        Map<String, Object> result = new HashMap<>();
        result.put(KEY_DATAX_DEST_CONFIG, entity.buildDestMapInfo());
        JSONArray columnArray = entity.getDestColumnJsonEntities();
        if (CollectionUtils.isEmpty(columnArray)) {
            throw new BusinessException("请设置数据去向列信息");
        }
        JSONObject columnObject = columnArray.getJSONObject(0);
        List<String> columnKeies = new ArrayList<>(columnObject.keySet());
        if (columnObject.size() == 1) {
            List<String> columnValues = new ArrayList<>();            for (int i = 0; i < columnArray.size(); i++) {
                columnValues.add(columnArray.getJSONObject(i).getString(columnKeies.get(0)));
            }
            result.put(KEY_DATAX_DEST_SIMPLE_COLUMN, columnValues);
        } else {
            List<Map<String, Object>> columns = new ArrayList<>();
            for (int i = 0; i < columnArray.size(); i++) {
                JSONObject handlerColumnObject = columnArray.getJSONObject(i);
                List<String> handlerColumnKeies = new ArrayList<>(handlerColumnObject.keySet());
                Map<String, Object> cValues = new HashMap<>();
                for (String handlerColumnKey : handlerColumnKeies) {
                    cValues.put(handlerColumnKey, handlerColumnObject.get(handlerColumnKey));
                }
                columns.add(cValues);
            }
            result.put(KEY_DATAX_DEST_MULIT_COLUMN, columns);
        }
        return result;
    }

    public static Map<String, Object> buildCustomWrite(IDataxFtlEntity entity) {
        Map<String, Object> result = new HashMap<>();

        Map<String, Object> mapInfo = entity.buildDestMapInfo();
        List<Map<String, Object>> mapValues = new ArrayList<>();
        mapInfo.forEach((key, value) -> {
            Map<String, Object> map = new HashMap<>();
            map.put(KEY, key);
            map.put(VALUE, value);
            mapValues.add(map);
        });

        // 处理列
        JSONArray columnArray = entity.getDestColumnJsonEntities();
        result.put(KEY_DATAX_DEST_MULIT_COLUMN, columnArray);
        result.put(KEY_DATAX_DEST_CUSTOM_CONFIG, mapValues);

        return result;
    }

}
