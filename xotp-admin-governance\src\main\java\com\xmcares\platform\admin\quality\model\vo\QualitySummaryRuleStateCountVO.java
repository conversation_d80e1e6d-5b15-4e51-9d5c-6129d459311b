package com.xmcares.platform.admin.quality.model.vo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/16 12:10
 */
public class QualitySummaryRuleStateCountVO implements Serializable {

    private List<DatatableSateCount> datatableSateCounts;
    private Integer totalExecuteFaildCount;
    private Integer totalExecuteTotalCount;
    private Integer totalAlarmCount;


    public static class DatatableSateCount{
        private String datawareId;
        private String datatableId;
        private Integer executeFaildCount;
        private Integer executeTotalCount;
        private Integer alarmCount;

        private String date;

        public String getDatawareId() {
            return datawareId;
        }

        public void setDatawareId(String datawareId) {
            this.datawareId = datawareId;
        }

        public String getDatatableId() {
            return datatableId;
        }

        public void setDatatableId(String datatableId) {
            this.datatableId = datatableId;
        }

        public Integer getExecuteFaildCount() {
            return executeFaildCount;
        }

        public void setExecuteFaildCount(Integer executeFaildCount) {
            this.executeFaildCount = executeFaildCount;
        }

        public Integer getExecuteTotalCount() {
            return executeTotalCount;
        }

        public void setExecuteTotalCount(Integer executeTotalCount) {
            this.executeTotalCount = executeTotalCount;
        }

        public Integer getAlarmCount() {
            return alarmCount;
        }

        public void setAlarmCount(Integer alarmCount) {
            this.alarmCount = alarmCount;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }
    }


    public List<DatatableSateCount> getDatatableSateCounts() {
        return datatableSateCounts;
    }

    public void setDatatableSateCounts(List<DatatableSateCount> datatableSateCounts) {
        this.datatableSateCounts = datatableSateCounts;
    }

    public Integer getTotalExecuteFaildCount() {
        return totalExecuteFaildCount;
    }

    public void setTotalExecuteFaildCount(Integer totalExecuteFaildCount) {
        this.totalExecuteFaildCount = totalExecuteFaildCount;
    }

    public Integer getTotalExecuteTotalCount() {
        return totalExecuteTotalCount;
    }

    public void setTotalExecuteTotalCount(Integer totalExecuteTotalCount) {
        this.totalExecuteTotalCount = totalExecuteTotalCount;
    }

    public Integer getTotalAlarmCount() {
        return totalAlarmCount;
    }

    public void setTotalAlarmCount(Integer totalAlarmCount) {
        this.totalAlarmCount = totalAlarmCount;
    }
}
