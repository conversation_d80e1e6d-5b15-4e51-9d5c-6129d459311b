package com.xmcares.platform.admin.developer.dataflow.repository;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.developer.dataflow.model.DevDataflow;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class DevDataflowDefinitionRepository {
    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    /**
     * 查询定义列表根据条件
     * @param devDataflow 参数
     * @param page 分页信息
     * @return
     */
    public List<DevDataflow> queryDevDataflowPage(DevDataflow devDataflow, Page<DevDataflow> page) {
        Map<String, Object> conditions = buildCondition(devDataflow);
        Map<String, Object> map = DBUtils.queryList(DevDataflow.TABLE, conditions);
        String sql = DBUtils.getSql(map) + " ORDER BY update_time Desc ";
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntities(sql, args, page, DevDataflow.class);
    }

    /**
     * 统计数据流
     * @param devDataflow 查询参数
     * @return
     */
    public int countDevDataflow(DevDataflow devDataflow) {
        Map<String, Object> conditions = buildCondition(devDataflow);
        Map<String, Object> map = DBUtils.queryCount(DevDataflow.TABLE, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForObject(sql, Integer.class, args);
    }

    /**
     *
     * @param devDataflow
     * @return
     */
    public boolean add(DevDataflow devDataflow) {
        Map<String, Object> map = DBUtils.insertSqlAndObjects(devDataflow, DevDataflow.class, DevDataflow.TABLE);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.update(sql, args) > 0;
    }
    public boolean update(DevDataflow devDataflow) {
        Map<String, Object> map = DBUtils.updateSqlAndObjects("id", devDataflow, DevDataflow.class,  DevDataflow.TABLE);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.update(sql, args) > 0;
    }
    /**
     * 根据主键更新是否上架
     * @param id 主键
     * @param shelved 上架状态
     * @return
     */
    public boolean updateDevDataflowShelvedById(String id,String shelved){
        StringBuilder sql= new StringBuilder("update ");
        sql.append( DevDataflow.TABLE);
        sql.append(" set shelved=?  where id= ?");
        return this.xcfJdbcTemplate.update(sql.toString(),new Object[]{shelved,id}) > 0;
    }
    /**
     * 根据主键更新是否删除
     * @param id 主键
     * @param deleted 删除状态
     * @return
     */
    public boolean updateDevDataflowDeletedById(String id,String deleted){
        StringBuilder sql= new StringBuilder("update ");
        sql.append( DevDataflow.TABLE);
        sql.append(" set deleted=?  where id= ?");
        return this.xcfJdbcTemplate.update(sql.toString(),new Object[]{deleted,id}) > 0;
    }
    private Map<String, Object> buildCondition(DevDataflow devDataflow) {
        Map<String, Object> conditions = new HashMap<>();
        if (devDataflow != null) {
            if (devDataflow.getName() != null && !devDataflow.getName().equals("")) {
                conditions.put("name", "%" + devDataflow.getName() + "%");
            }
            if (devDataflow.getRemark() != null && !devDataflow.getRemark().equals("")) {
                conditions.put("remark", "%" + devDataflow.getRemark() + "%");
            }
            if (devDataflow.getDeleted() != null && !devDataflow.getDeleted().equals("")) {
                conditions.put("deleted", devDataflow.getDeleted());
            }
            if (devDataflow.getShelved() != null && !devDataflow.getShelved().equals("")) {
                conditions.put("shelved", devDataflow.getShelved());
            }
        }
        return conditions;
    }
    public List<DevDataflow> getByName(String name) {
        String sql = "select * from " + DevDataflow.TABLE + " where name= ? ";
        return xcfJdbcTemplate.queryForEntities(sql, new Object[]{name}, DevDataflow.class);
    }
    public List<DevDataflow> queryByDeleted(String deleted) {
        String sql = "select * from " + DevDataflow.TABLE + " where deleted= ? ";
        return xcfJdbcTemplate.queryForEntities(sql, new Object[]{deleted}, DevDataflow.class);
    }
    public boolean deleteByDeleted(String deleted) {
        String sql = "DELETE from " + DevDataflow.TABLE + " where deleted= ? ";
        return xcfJdbcTemplate.update(sql, new Object[]{deleted})>0;
    }

    public DevDataflow getById(String id) {
        HashMap<String, Object> conditions = new HashMap<>();
        conditions.put("id", String.valueOf(id));
        conditions.put("deleted", YNEnum.NO.getIntCharCode());
        Map<String, Object> map = DBUtils.queryList(DevDataflow.TABLE, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntity(sql, args, DevDataflow.class);
    }

    public boolean delete(String id) {
        String sql = "DELETE from " + DevDataflow.TABLE + " where id= ? ";
        return xcfJdbcTemplate.update(sql, new Object[]{id})>0;
    }
}
