package com.xmcares.platform.admin.integrator.datasync.model;

/**
 * JobLogVO
 *
 * <AUTHOR>
 * @Descriptions JobLogVO
 * @Date 2025/7/23 10:50
 */
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * JobLogVO
 * <AUTHOR>
 * @Descriptions Aggregated job log view object
 * @Date 2025/7/23
 */
@ApiModel(description = "Aggregated Log View Object")
public class JobLogVO {

    @ApiModelProperty(value = "XXL-Job scheduling log")
    private String xxlJobLog;

    @ApiModelProperty(value = "SeaTunnel engine execution log")
    private String seaTunnelLog;

    @ApiModelProperty(value = "XXL-Job 执行器详细过程日志")
    private String xxlJobDetailLog;

    @ApiModelProperty(value = "写入QPS")
    private Double writedQps;

    @ApiModelProperty(value = "写入数量")
    private Integer writedCount;

    @ApiModelProperty(value = "接收QPS")
    private Double receviedQps;

    @ApiModelProperty(value = "接收数量")
    private Integer receivedCount;

    public String getXxlJobLog() {
        return xxlJobLog;
    }

    public void setXxlJobLog(String xxlJobLog) {
        this.xxlJobLog = xxlJobLog;
    }

    public String getSeaTunnelLog() {
        return seaTunnelLog;
    }

    public void setSeaTunnelLog(String seaTunnelLog) {
        this.seaTunnelLog = seaTunnelLog;
    }

    public String getXxlJobDetailLog() {
        return xxlJobDetailLog;
    }

    public void setXxlJobDetailLog(String xxlJobDetailLog) {
        this.xxlJobDetailLog = xxlJobDetailLog;
    }

    public Double getWritedQps() {
        return writedQps;
    }

    public void setWritedQps(Double writedQps) {
        this.writedQps = writedQps;
    }

    public Integer getWritedCount() {
        return writedCount;
    }

    public void setWritedCount(Integer writedCount) {
        this.writedCount = writedCount;
    }

    public Double getReceviedQps() {
        return receviedQps;
    }

    public void setReceviedQps(Double receviedQps) {
        this.receviedQps = receviedQps;
    }

    public Integer getReceivedCount() {
        return receivedCount;
    }

    public void setReceivedCount(Integer receivedCount) {
        this.receivedCount = receivedCount;
    }
}
