package com.xmcares.platform.admin.integrator.datasync.repository.xxljob.model;

import java.util.Date;

/**
 * <AUTHOR> chenYG
 * @date : 2022/3/29 10:47
 */
public class XxlJobIncrement {
    private int id;  // id
    private int xxlJobInfoId; // xxl_job 表ID
    private String incMode; /** 增量方式 {@link com.xmcares.platform.admin.integrator.datasync.repository.xxljob.enums.XxlJobIncrementMode}*/
    private String primaryKey;  /** 增量建 */
    private Date incStartTime; /** 增量開始時間 */
    private String replaceParamType;  /** 时间格式 */
    private String incStartValue;  // 增量开始值
    private String partitionInfo; //分区信息
    private String replaceParam; // 动态参数
    private String readerTable; // 读取的表
    private String datasourceId; // 数据源ID

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getXxlJobInfoId() {
        return xxlJobInfoId;
    }

    public void setXxlJobInfoId(int xxlJobInfoId) {
        this.xxlJobInfoId = xxlJobInfoId;
    }

    public String getIncMode() {
        return incMode;
    }

    public void setIncMode(String incMode) {
        this.incMode = incMode;
    }

    public String getPrimaryKey() {
        return primaryKey;
    }

    public void setPrimaryKey(String primaryKey) {
        this.primaryKey = primaryKey;
    }

    public Date getIncStartTime() {
        return incStartTime;
    }

    public void setIncStartTime(Date incStartTime) {
        this.incStartTime = incStartTime;
    }

    public String getReplaceParamType() {
        return replaceParamType;
    }

    public void setReplaceParamType(String replaceParamType) {
        this.replaceParamType = replaceParamType;
    }

    public String getIncStartValue() {
        return incStartValue;
    }

    public void setIncStartValue(String incStartValue) {
        this.incStartValue = incStartValue;
    }

    public String getPartitionInfo() {
        return partitionInfo;
    }

    public void setPartitionInfo(String partitionInfo) {
        this.partitionInfo = partitionInfo;
    }

    public String getReplaceParam() {
        return replaceParam;
    }

    public void setReplaceParam(String replaceParam) {
        this.replaceParam = replaceParam;
    }

    public String getReaderTable() {
        return readerTable;
    }

    public void setReaderTable(String readerTable) {
        this.readerTable = readerTable;
    }

    public String getDatasourceId() {
        return datasourceId;
    }

    public void setDatasourceId(String datasourceId) {
        this.datasourceId = datasourceId;
    }
}
