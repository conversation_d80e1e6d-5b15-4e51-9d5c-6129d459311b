package com.xmcares.platform.admin.quality.repository;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.platform.admin.quality.model.QltyRuleScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/25 09:10
 */
@Repository
public class QualitySchedulerLogRepostitory {
    private static final Logger LOG = LoggerFactory.getLogger(QualitySchedulerLogRepostitory.class);
    public static final String TABLE_BDP_QLTY_RULE_SCHEDULER = "bdp_qlty_rule_scheduler";
    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    public Page<QltyRuleScheduler> queryDatatableSchedulers(String datawareId, String datatableId, Integer pageNo, Integer pageSize) {
        try {
            List<QltyRuleScheduler> qltyRuleSchedulers = xcfJdbcTemplate.<QltyRuleScheduler>queryForEntities(
                    " select " +
                            " * " +
                            " from " + TABLE_BDP_QLTY_RULE_SCHEDULER +
                            " where " +
                            ((datawareId == null && !"".equals(datawareId)) ? " true " : (" dataware_id ='" + datawareId + "' ")) +
                            " or " +
                            ((datatableId == null && !"".equals(datawareId)) ? " true " : (" datatable_id ='" + datatableId + "'")) +
                            " limit  " + ((pageNo-1) * pageSize) + "," + pageSize
                    ,
                    new Object[]{},
                    QltyRuleScheduler.class
            );

            Integer total = xcfJdbcTemplate.<Integer>queryForObject(
                    " select " +
                            " IFNULL(( " +
                            " select " +
                            " count(*) total " +
                            " from " + TABLE_BDP_QLTY_RULE_SCHEDULER +
                            " where " +
                            ((datawareId == null && !"".equals(datawareId)) ? " true " : (" dataware_id ='" + datawareId + "' ")) +
                            " or " +
                            ((datatableId == null && !"".equals(datawareId)) ? " true " : (" datatable_id ='" + datatableId + "'")) +
                            " ),0 ) total "
                    ,
                    Integer.class,
                    new Object[]{}
            );

            Page<QltyRuleScheduler> qltyRuleSchedulerPage = new Page<QltyRuleScheduler>();

            qltyRuleSchedulerPage.setData(qltyRuleSchedulers);
            qltyRuleSchedulerPage.setPageNo(pageNo);
            qltyRuleSchedulerPage.setPageSize(pageSize);
            qltyRuleSchedulerPage.setTotal(total);
            return qltyRuleSchedulerPage;
        } catch (Exception e) {
            LOG.warn("queryDatatableSchedulers", e);
            return null;
        }
    }

    public QltyRuleScheduler queryDatatableScheduler(String scheduleId) {
        try {
            QltyRuleScheduler qltyRuleScheduler = xcfJdbcTemplate.<QltyRuleScheduler>queryForEntity(
                    " select " +
                            " * " +
                            " from " + TABLE_BDP_QLTY_RULE_SCHEDULER +
                            " where " +
                            " id = '" + scheduleId + "'"
                    ,
                    new Object[]{},
                    QltyRuleScheduler.class
            );

            return qltyRuleScheduler;
        } catch (Exception e) {
            LOG.warn("QltyRuleScheduler", e);
            return null;
        }
    }

}
