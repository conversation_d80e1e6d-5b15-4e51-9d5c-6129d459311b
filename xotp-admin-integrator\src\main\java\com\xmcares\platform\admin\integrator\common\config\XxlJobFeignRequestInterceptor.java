package com.xmcares.platform.admin.integrator.common.config;

/**
 * XxlJobFeignRequestInterceptor
 *
 * <AUTHOR>
 * @Descriptions XxlJobFeignRequestInterceptor
 * @Date 2025/5/19 13:49
 */
import com.xxl.job.core.util.XxlJobRemotingUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

// 不要注册成全局配置，否则其他Feign客户端也会带上，请从 @Feign 的 configuration 中指定
public class XxlJobFeignRequestInterceptor implements RequestInterceptor {

    /**
     * XXL-JOB 管理端鉴权 token
     */
    @Value("${xbdp.feign.scheduler-service.xxl-job.access-token:}")
    private String accessToken;

    @Override
    public void apply(RequestTemplate template) {
        // 为所有通过此 Feign 客户端的请求添加请求头
        if (StringUtils.hasLength(accessToken)) {
            // 确保存在鉴权值
            template.header(XxlJobRemotingUtil.XXL_JOB_ACCESS_TOKEN, accessToken);
        }
    }
}
