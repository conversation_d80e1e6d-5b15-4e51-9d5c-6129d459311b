{
              "name": "mongodbwriter",
              "parameter": {
                  "address": [
<#list destDatasource.addresses as address>
    ${address}<#if field_has_next>,</#if>
</#list>
                  ],
                  "userName": ${destDatasource.userName},
                  "userPassword": ${destDatasource.userPassword},
                  "dbName": $${destDatasource.dbName},
                  "collectionName": ${destDatasource.collectionName},
                  "column": [

<#list destDatasource.columns as column>
    {
    ${column.name!},
    ${column.type!},
    ${column.spliter}
    }
    ${column}<#if column_has_next>,</#if>
</#list>
                  ],
  				"upsertInfo": {
  					"isUpsert": ${destDatasource.upsertInfo.isUpsert},
  					"upsertKey": ${destDatasource.upsertInfo.upsertKey}
  				}
              }
          }
      }