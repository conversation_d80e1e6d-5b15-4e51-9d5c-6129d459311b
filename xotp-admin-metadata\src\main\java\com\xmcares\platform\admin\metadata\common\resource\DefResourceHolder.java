package com.xmcares.platform.admin.metadata.common.resource;

import com.xmcares.framework.fsclient.FSTemplate;
import com.xmcares.framework.fsclient.FileDesc;
import org.springframework.util.StringUtils;

import java.io.InputStream;
import java.io.OutputStream;

public class DefResourceHolder implements IResourceHolder{

    private final FSTemplate fsTemplate;
    private final IErrorHandler handler;
    public DefResourceHolder(IErrorHandler handler, FSTemplate fsTemplate) {
        this.fsTemplate = fsTemplate;
        this.handler = handler;
    }

    @Override
    public void add(String path, InputStream stream) {
        try {
            fsTemplate.saveFile(new FileDesc.FileDescImpl(null, path), stream);
        } catch (Exception e) { handler.handler(path, e); }
    }

    @Override
    public void remove(String path) {
        try {
            fsTemplate.deleteFile(path);
        } catch (Exception e) { handler.handler(path, e); }
    }

    @Override
    public void read(String path, OutputStream stream) {
        try {
            // 标准化路径，将所有的反斜杠替换为正斜杠，解决混合路径分隔符的问题
            path = StringUtils.replace(path, "\\", "/");
            fsTemplate.loadFile(path, stream);
        } catch (Exception e) { handler.handler(path, e); }
    }
}
