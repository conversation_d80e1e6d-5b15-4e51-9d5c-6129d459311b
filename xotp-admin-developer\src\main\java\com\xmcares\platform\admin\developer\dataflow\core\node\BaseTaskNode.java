package com.xmcares.platform.admin.developer.dataflow.core.node;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xmcares.platform.admin.developer.dataflow.vo.DevDataflowNodeVo;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/6/29 16:20
 **/
public abstract class BaseTaskNode implements ITaskNode<DevDataflowNodeVo>{

    protected static final String KEY_SCHEDULER_TYPE = "schedulerType";
    protected static final String KEY_SCHEDULER_OPTION = "schedulerOptions";
    protected static final String KEY_SCHEDULER_RETRY_COUNT = "schedulerFailRetryCount";
    public static final Set<String> DEFINE_PARAM_KEY = new HashSet<>(3);
    static {
        CollectionUtils.addAll(DEFINE_PARAM_KEY, new String[]{"user","partial","global"});
    };

    protected String key;
    protected TaskNodeManager<DevDataflowNodeVo> manager;
    private DevDataflowNodeVo nodeInfo;
    public void init(TaskNodeFactory.CommonNodeVo nodeInfo, TaskNodeManager<DevDataflowNodeVo> manager) {
        this.nodeInfo = buildNodeInfo(nodeInfo);
        this.manager = manager;
        this.key = this.nodeInfo.nodeId();
    }
    private DevDataflowNodeVo buildNodeInfo(TaskNodeFactory.CommonNodeVo nodeInfo){
        DevDataflowNodeVo result = new DevDataflowNodeVo();
        result.setId(nodeInfo.getId());
        result.setLabel(nodeInfo.getLabel());
        result.setType(nodeInfo.getType());
        result.setGroupId(nodeInfo.getGroupId());
        result.setPreIds(nodeInfo.getPreIds());
        result.setPostIds(nodeInfo.getPostIds());
        result.setParams(thisParams(nodeInfo.getParams()));
        return result;
    }
    private List<DevDataflowNodeVo.DevDataflowNodeParam> thisParams(Object params) {
        if (!(params instanceof JSONObject)) { return null; }
        List<DevDataflowNodeVo.DevDataflowNodeParam> result = new ArrayList<>();
        for (Map.Entry<String, Object> entry : ((JSONObject) params).entrySet()) {
            if (DEFINE_PARAM_KEY.contains(entry.getKey()) && entry.getValue() != null && entry.getValue() instanceof JSONArray) {
                String prex = !"global".equals(entry.getKey()) ? entry.getKey() + "." : "";
                JSONArray handlerArray = (JSONArray) entry.getValue();
                for (Object o : handlerArray) {
                    if (o instanceof JSONObject && ((JSONObject) o).containsKey("key") && ((JSONObject) o).containsKey("value")) {
                        result.add(DevDataflowNodeVo.DevDataflowNodeParam.createFrom(prex + ((JSONObject) o).getString("key"), ((JSONObject) o).get("value")));
                    }
                }
            } else {
                result.add(DevDataflowNodeVo.DevDataflowNodeParam.createFrom(entry.getKey(), entry.getValue()));
            }
        }
        return result;
    }

    @Override
    public DevDataflowNodeVo nodeInfo() {
        return nodeInfo;
    }

}
