{
  "plugin_name": "Plugin",

  <#-- 智能判断输入源，逻辑与 jdbc_sink.ftl 保持一致 -->
  <#if fieldMappings?? && fieldMappings?has_content>
      "plugin_input": "${sink.plugin_input!'default_transform_output'}",
  <#else>
      "plugin_input": "${sink.plugin_input!'default_source_output'}",
  </#if>

  "writer_class_name": "${sink.writer_class_name}",

  <#-- 可选参数：只有在提供了 jar_path 时才生成此字段 -->
  <#if sink.writer_jar_path?? && sink.writer_jar_path?has_content>
      "writer_jar_path": "${sink.writer_jar_path}",
  </#if>

  <#--
    可选参数：自定义的 writer_options
    同样，这里假设 'sink.writer_options_json' 是一个已经序列化好的 JSON 字符串。
  -->
  <#if sink.writer_options_json?? && sink.writer_options_json?has_content>
      "writer_options": ${sink.writer_options_json}
  <#else>
      "writer_options": {}
  </#if>
}
