package com.xmcares.platform.admin.integrator.datasync.model;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.integrator.datasync.dto.DatasyncDto;
import com.xmcares.platform.admin.integrator.datasync.vo.DataxTempVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.codec.digest.PureJavaCrc32;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Date;

@ApiModel(description = "bdp_intg_datasync_job")
@TableName(value = "bdp_intg_datasync_job")
public class DatasyncJob implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 集成信息ID
     */
    @TableField(value = "datasync_id")
    @ApiModelProperty(value = "集成信息ID")
    private String datasyncId;

    /**
     * 发布时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "发布时间")
    private Date createTime;

    /**
     * 发布人
     */
    @TableField(value = "create_user")
    @ApiModelProperty(value = "发布人")
    private String createUser;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField(value = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 实例名称
     */
    @TableField(value = "job_name")
    @ApiModelProperty(value = "实例名称")
    private String jobName;

    /**
     * 同步作业运行配置
     */
    @TableField(value = "job_options")
    @ApiModelProperty(value = "同步作业运行配置")
    private String jobOptions;

    /**
     * 数据来源集成方式 0:内置 1:插件
     */
    @TableField(value = "orgin_type")
    @ApiModelProperty(value = "数据来源集成方式 0:内置 1:插件")
    private String orginType;

    /**
     * 数据来源数据源(或插件)名称
     */
    @TableField(value = "orgin_datasource_name")
    @ApiModelProperty(value = "数据来源数据源(或插件)名称")
    private String orginDatasourceName;

    /**
     * 数据来源插件路径
     */
    @TableField(value = "orgin_plugin_path")
    @ApiModelProperty(value = "数据来源插件路径")
    private String orginPluginPath;

    /**
     * 数据去向集成方式 0:内置 1：插件
     */
    @TableField(value = "dest_type")
    @ApiModelProperty(value = "数据去向集成方式 0:内置 1：插件")
    private String destType;

    /**
     * 数据去向数据源(或插件)名称
     */
    @TableField(value = "dest_datasource_name")
    @ApiModelProperty(value = "数据去向数据源(或插件)名称")
    private String destDatasourceName;

    /**
     * 数据去向插件路径
     */
    @TableField(value = "dest_plugin_path")
    @ApiModelProperty(value = "数据去向插件路径")
    private String destPluginPath;

    /**
     * 状态：0-停止，1-运行
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value = "状态：0-停止，1-运行")
    private Byte status;

    /**
     * 是否删除 0：否 1：是
     */
    @TableField(value = "deleted")
    @ApiModelProperty(value = "是否删除 0：否 1：是")
    private String deleted;

    /**
     * 调度任务ID
     */
    @TableField(value = "schedule_id")
    @ApiModelProperty(value = "调度任务ID")
    private String scheduleId;

    /**
     * 执行器主键ID
     */
    @TableField(value = "schedule_group")
    @ApiModelProperty(value = "执行器主键ID")
    private Integer scheduleGroup;

    /**
     * 调度类型
     */
    @TableField(value = "schedule_type")
    @ApiModelProperty(value = "调度类型")
    private String scheduleType;

    /**
     * 调度配置，值含义取决于调度类型
     */
    @TableField(value = "schedule_conf")
    @ApiModelProperty(value = "调度配置，值含义取决于调度类型")
    private String scheduleConf;

    /**
     * 调度过期策略
     */
    @TableField(value = "misfire_strategy")
    @ApiModelProperty(value = "调度过期策略")
    private String misfireStrategy;

    /**
     * 执行器路由策略
     */
    @TableField(value = "executor_route_strategy")
    @ApiModelProperty(value = "执行器路由策略")
    private String executorRouteStrategy;

    /**
     * 执行器任务handler
     */
    @TableField(value = "executor_handler")
    @ApiModelProperty(value = "执行器任务handler")
    private String executorHandler;

    /**
     * 执行器任务参数
     */
    @TableField(value = "executor_params")
    @ApiModelProperty(value = "执行器任务参数")
    private String executorParams;

    /**
     * 阻塞处理策略
     */
    @TableField(value = "executor_block_strategy")
    @ApiModelProperty(value = "阻塞处理策略")
    private String executorBlockStrategy;

    /**
     * 任务执行超时时间，单位秒
     */
    @TableField(value = "executor_timeout")
    @ApiModelProperty(value = "任务执行超时时间，单位秒")
    private Integer executorTimeout;

    /**
     * 失败重试次数
     */
    @TableField(value = "executor_fail_retry_count")
    @ApiModelProperty(value = "失败重试次数")
    private Integer executorFailRetryCount;

    /**
     * 子任务ID，多个逗号分隔
     */
    @TableField(value = "child_jobids")
    @ApiModelProperty(value = "子任务ID，多个逗号分隔")
    private String childJobids;

    public static DatasyncJob newDatasyncJobInstance(String instanceName, String code, DataxTempVo context, String filePath, DatasyncDto datasync) {
        DatasyncJob datasyncJob = new DatasyncJob();
        datasyncJob.setId(SnowflakeGenerator.getNextStringId());
        datasyncJob.setDatasyncId(datasync.getId());
        datasyncJob.setJobName(instanceName);
        datasyncJob.setJobOptions(context.getContext());
        PureJavaCrc32 crc32 = new PureJavaCrc32();
        byte[] bytes;
        try {
            bytes = StringUtils.getBytes(context.getContext(), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new IllegalArgumentException(String.format("获取[%s]字节数组异常", context.getContext()), e);
        }
        crc32.update(bytes, 0, bytes.length);
        System.out.println("模板代码：" + crc32.getValue());
        datasyncJob.setOrginType(datasync.getOrginType());
        datasyncJob.setOrginDatasourceName(datasync.getOrginDatasourceName());
        datasyncJob.setOrginPluginPath(datasync.getOrginPluginPath());
        datasyncJob.setDestType(datasync.getDestType());
        datasyncJob.setDestDatasourceName(datasync.getDestDatasourceName());
        datasyncJob.setDestPluginPath(datasync.getDestPluginPath());
        datasyncJob.setDeleted(YNEnum.NO.getIntCharCode());
        datasyncJob.setCreateTime(DateUtil.date());
        datasyncJob.setCreateUser(UserContextHolder.getUserContext().getUsername());
        datasyncJob.setUpdateTime(DateUtil.date());
        datasyncJob.setUpdateUser(UserContextHolder.getUserContext().getUsername());
        return datasyncJob;
    }


    /**
     * 获取ID
     *
     * @return id - ID
     */
    public String getId() {
        return id;
    }

    /**
     * 设置ID
     *
     * @param id ID
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取集成信息ID
     *
     * @return datasync_id - 集成信息ID
     */
    public String getDatasyncId() {
        return datasyncId;
    }

    /**
     * 设置集成信息ID
     *
     * @param datasyncId 集成信息ID
     */
    public void setDatasyncId(String datasyncId) {
        this.datasyncId = datasyncId;
    }

    /**
     * 获取发布时间
     *
     * @return create_time - 发布时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置发布时间
     *
     * @param createTime 发布时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取发布人
     *
     * @return create_user - 发布人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置发布人
     *
     * @param createUser 发布人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新人
     *
     * @return update_user - 更新人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新人
     *
     * @param updateUser 更新人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    /**
     * 获取实例名称
     *
     * @return job_name - 实例名称
     */
    public String getJobName() {
        return jobName;
    }

    /**
     * 设置实例名称
     *
     * @param jobName 实例名称
     */
    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    /**
     * 获取同步作业运行配置
     *
     * @return job_options - 同步作业运行配置
     */
    public String getJobOptions() {
        return jobOptions;
    }

    /**
     * 设置同步作业运行配置
     *
     * @param jobOptions 同步作业运行配置
     */
    public void setJobOptions(String jobOptions) {
        this.jobOptions = jobOptions;
    }

    /**
     * 获取数据来源集成方式 0:内置 1:插件
     *
     * @return orgin_type - 数据来源集成方式 0:内置 1:插件
     */
    public String getOrginType() {
        return orginType;
    }

    /**
     * 设置数据来源集成方式 0:内置 1:插件
     *
     * @param orginType 数据来源集成方式 0:内置 1:插件
     */
    public void setOrginType(String orginType) {
        this.orginType = orginType;
    }

    /**
     * 获取数据来源数据源(或插件)名称
     *
     * @return orgin_datasource_name - 数据来源数据源(或插件)名称
     */
    public String getOrginDatasourceName() {
        return orginDatasourceName;
    }

    /**
     * 设置数据来源数据源(或插件)名称
     *
     * @param orginDatasourceName 数据来源数据源(或插件)名称
     */
    public void setOrginDatasourceName(String orginDatasourceName) {
        this.orginDatasourceName = orginDatasourceName;
    }

    /**
     * 获取数据来源插件路径
     *
     * @return orgin_plugin_path - 数据来源插件路径
     */
    public String getOrginPluginPath() {
        return orginPluginPath;
    }

    /**
     * 设置数据来源插件路径
     *
     * @param orginPluginPath 数据来源插件路径
     */
    public void setOrginPluginPath(String orginPluginPath) {
        this.orginPluginPath = orginPluginPath;
    }

    /**
     * 获取数据去向集成方式 0:内置 1：插件
     *
     * @return dest_type - 数据去向集成方式 0:内置 1：插件
     */
    public String getDestType() {
        return destType;
    }

    /**
     * 设置数据去向集成方式 0:内置 1：插件
     *
     * @param destType 数据去向集成方式 0:内置 1：插件
     */
    public void setDestType(String destType) {
        this.destType = destType;
    }

    /**
     * 获取数据去向数据源(或插件)名称
     *
     * @return dest_datasource_name - 数据去向数据源(或插件)名称
     */
    public String getDestDatasourceName() {
        return destDatasourceName;
    }

    /**
     * 设置数据去向数据源(或插件)名称
     *
     * @param destDatasourceName 数据去向数据源(或插件)名称
     */
    public void setDestDatasourceName(String destDatasourceName) {
        this.destDatasourceName = destDatasourceName;
    }

    /**
     * 获取数据去向插件路径
     *
     * @return dest_plugin_path - 数据去向插件路径
     */
    public String getDestPluginPath() {
        return destPluginPath;
    }

    /**
     * 设置数据去向插件路径
     *
     * @param destPluginPath 数据去向插件路径
     */
    public void setDestPluginPath(String destPluginPath) {
        this.destPluginPath = destPluginPath;
    }

    /**
     * 获取状态：0-停止，1-运行
     *
     * @return status - 状态：0-停止，1-运行
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置状态：0-停止，1-运行
     *
     * @param status 状态：0-停止，1-运行
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * 获取是否删除 0：否 1：是
     *
     * @return deleted - 是否删除 0：否 1：是
     */
    public String getDeleted() {
        return deleted;
    }

    /**
     * 设置是否删除 0：否 1：是
     *
     * @param deleted 是否删除 0：否 1：是
     */
    public void setDeleted(String deleted) {
        this.deleted = deleted;
    }

    /**
     * 获取调度任务ID
     *
     * @return schedule_id - 调度任务ID
     */
    public String getScheduleId() {
        return scheduleId;
    }

    /**
     * 设置调度任务ID
     *
     * @param scheduleId 调度任务ID
     */
    public void setScheduleId(String scheduleId) {
        this.scheduleId = scheduleId;
    }

    /**
     * 获取执行器主键ID
     *
     * @return schedule_group - 执行器主键ID
     */
    public Integer getScheduleGroup() {
        return scheduleGroup;
    }

    /**
     * 设置执行器主键ID
     *
     * @param scheduleGroup 执行器主键ID
     */
    public void setScheduleGroup(Integer scheduleGroup) {
        this.scheduleGroup = scheduleGroup;
    }

    /**
     * 获取调度类型
     *
     * @return schedule_type - 调度类型
     */
    public String getScheduleType() {
        return scheduleType;
    }

    /**
     * 设置调度类型
     *
     * @param scheduleType 调度类型
     */
    public void setScheduleType(String scheduleType) {
        this.scheduleType = scheduleType;
    }

    /**
     * 获取调度配置，值含义取决于调度类型
     *
     * @return schedule_conf - 调度配置，值含义取决于调度类型
     */
    public String getScheduleConf() {
        return scheduleConf;
    }

    /**
     * 设置调度配置，值含义取决于调度类型
     *
     * @param scheduleConf 调度配置，值含义取决于调度类型
     */
    public void setScheduleConf(String scheduleConf) {
        this.scheduleConf = scheduleConf;
    }

    /**
     * 获取调度过期策略
     *
     * @return misfire_strategy - 调度过期策略
     */
    public String getMisfireStrategy() {
        return misfireStrategy;
    }

    /**
     * 设置调度过期策略
     *
     * @param misfireStrategy 调度过期策略
     */
    public void setMisfireStrategy(String misfireStrategy) {
        this.misfireStrategy = misfireStrategy;
    }

    /**
     * 获取执行器路由策略
     *
     * @return executor_route_strategy - 执行器路由策略
     */
    public String getExecutorRouteStrategy() {
        return executorRouteStrategy;
    }

    /**
     * 设置执行器路由策略
     *
     * @param executorRouteStrategy 执行器路由策略
     */
    public void setExecutorRouteStrategy(String executorRouteStrategy) {
        this.executorRouteStrategy = executorRouteStrategy;
    }

    /**
     * 获取执行器任务handler
     *
     * @return executor_handler - 执行器任务handler
     */
    public String getExecutorHandler() {
        return executorHandler;
    }

    /**
     * 设置执行器任务handler
     *
     * @param executorHandler 执行器任务handler
     */
    public void setExecutorHandler(String executorHandler) {
        this.executorHandler = executorHandler;
    }

    /**
     * 获取执行器任务参数
     *
     * @return executor_params - 执行器任务参数
     */
    public String getExecutorParams() {
        return executorParams;
    }

    /**
     * 设置执行器任务参数
     *
     * @param executorParams 执行器任务参数
     */
    public void setExecutorParams(String executorParams) {
        this.executorParams = executorParams;
    }

    /**
     * 获取阻塞处理策略
     *
     * @return executor_block_strategy - 阻塞处理策略
     */
    public String getExecutorBlockStrategy() {
        return executorBlockStrategy;
    }

    /**
     * 设置阻塞处理策略
     *
     * @param executorBlockStrategy 阻塞处理策略
     */
    public void setExecutorBlockStrategy(String executorBlockStrategy) {
        this.executorBlockStrategy = executorBlockStrategy;
    }

    /**
     * 获取任务执行超时时间，单位秒
     *
     * @return executor_timeout - 任务执行超时时间，单位秒
     */
    public Integer getExecutorTimeout() {
        return executorTimeout;
    }

    /**
     * 设置任务执行超时时间，单位秒
     *
     * @param executorTimeout 任务执行超时时间，单位秒
     */
    public void setExecutorTimeout(Integer executorTimeout) {
        this.executorTimeout = executorTimeout;
    }

    /**
     * 获取失败重试次数
     *
     * @return executor_fail_retry_count - 失败重试次数
     */
    public Integer getExecutorFailRetryCount() {
        return executorFailRetryCount;
    }

    /**
     * 设置失败重试次数
     *
     * @param executorFailRetryCount 失败重试次数
     */
    public void setExecutorFailRetryCount(Integer executorFailRetryCount) {
        this.executorFailRetryCount = executorFailRetryCount;
    }

    /**
     * 获取子任务ID，多个逗号分隔
     *
     * @return child_jobids - 子任务ID，多个逗号分隔
     */
    public String getChildJobids() {
        return childJobids;
    }

    /**
     * 设置子任务ID，多个逗号分隔
     *
     * @param childJobids 子任务ID，多个逗号分隔
     */
    public void setChildJobids(String childJobids) {
        this.childJobids = childJobids;
    }


    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", datasyncId=").append(datasyncId);
        sb.append(", createTime=").append(createTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", jobName=").append(jobName);
        sb.append(", jobOptions=").append(jobOptions);
        sb.append(", orginType=").append(orginType);
        sb.append(", orginDatasourceName=").append(orginDatasourceName);
        sb.append(", orginPluginPath=").append(orginPluginPath);
        sb.append(", destType=").append(destType);
        sb.append(", destDatasourceName=").append(destDatasourceName);
        sb.append(", destPluginPath=").append(destPluginPath);
        sb.append(", status=").append(status);
        sb.append(", deleted=").append(deleted);
        sb.append(", scheduleId=").append(scheduleId);
        sb.append(", scheduleGroup=").append(scheduleGroup);
        sb.append(", scheduleType=").append(scheduleType);
        sb.append(", scheduleConf=").append(scheduleConf);
        sb.append(", misfireStrategy=").append(misfireStrategy);
        sb.append(", executorRouteStrategy=").append(executorRouteStrategy);
        sb.append(", executorHandler=").append(executorHandler);
        sb.append(", executorParams=").append(executorParams);
        sb.append(", executorBlockStrategy=").append(executorBlockStrategy);
        sb.append(", executorTimeout=").append(executorTimeout);
        sb.append(", executorFailRetryCount=").append(executorFailRetryCount);
        sb.append(", childJobids=").append(childJobids);
        sb.append("]");
        return sb.toString();
    }
}
