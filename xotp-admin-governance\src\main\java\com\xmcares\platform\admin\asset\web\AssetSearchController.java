package com.xmcares.platform.admin.asset.web;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.platform.admin.asset.model.MetaDatatable;
import com.xmcares.platform.admin.asset.model.MetaDatatableColumn;
import com.xmcares.platform.admin.asset.model.MetaDataware;
import com.xmcares.platform.admin.asset.model.vo.JobPageQueryVO;
import com.xmcares.platform.admin.asset.model.vo.ServicePageQueryVO;
import com.xmcares.platform.admin.asset.repository.AssetSearchRepository;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/25 09:59
 */
@Api(value = "资产搜索服务")
@Validated
@RestController
@RequestMapping("/asset/search")
public class AssetSearchController {

    @Autowired
    AssetSearchRepository assetSearchRepository;

    @ApiOperation("查询仓库列表")
    @GetMapping("/dataware/list-query")
    @ResponseBody
    public List<MetaDataware> datawareListQuery() {
        return assetSearchRepository.datawareListQuery();
    }

    @ApiOperation("查询仓库的表列表")
    @GetMapping("/datatable/list-query")
    @ResponseBody
    public List<MetaDatatable> datatableListQuery(
            @RequestParam(name = "datawareId") String datawareId
    ) {
        return assetSearchRepository.datatableListQuery(datawareId);
    }

    @ApiOperation("搜索表")
    @GetMapping("/datatable/page-query")
    @ResponseBody
    public Page<MetaDatatable> datatablePageQuery(
            String name,
            String datawareId,
            @RequestParam(name = "pageNo") Integer pageNo,
            @RequestParam(name = "pageSize") Integer pageSize
    ) {
        return assetSearchRepository.datatablePageQuery(name, datawareId, pageNo, pageSize);
    }

    @ApiOperation("搜索数据列")
    @GetMapping("/datatable/column/page-query")
    @ResponseBody
    public Page<MetaDatatableColumn> datatableColumnPageQuery(
            String name,
            @RequestParam(name = "datawareId") String datawareId,
            @RequestParam(name = "datatableId") String datatableId,
            @RequestParam(name = "pageNo") Integer pageNo,
            @RequestParam(name = "pageSize") Integer pageSize
    ) {
        return assetSearchRepository.datatableColumnPageQuery(name, datawareId, datatableId, pageNo, pageSize);
    }

    @ApiOperation("搜索服务")
    @GetMapping("/service/page-query")
    @ResponseBody
    public Page<ServicePageQueryVO> servicePageQuery(
            String name,
            String datawareId,
            @RequestParam(name = "pageNo") Integer pageNo,
            @RequestParam(name = "pageSize") Integer pageSize
    ) {
        return assetSearchRepository.servicePageQuery(name, datawareId, pageNo, pageSize);
    }

    @ApiOperation("搜索任务")
    @GetMapping("/job/page-query")
    @ResponseBody
    public Page<JobPageQueryVO> jobPageQuery(
            String name,
            String status,
            @RequestParam(name = "pageNo") Integer pageNo,
            @RequestParam(name = "pageSize") Integer pageSize
    ) {
        return assetSearchRepository.jobPageQuery(name, status, pageNo, pageSize);
    }

}
