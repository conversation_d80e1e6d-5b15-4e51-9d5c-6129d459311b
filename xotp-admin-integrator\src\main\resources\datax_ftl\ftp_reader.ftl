{
                    "name": "ftpreader",
                    "parameter": {
                        "protocol": ${orginDatasource.protocol},
                        "host": ${orginDatasource.host},
                        "port": ${orginDatasource.port},
                        "username": ${orginDatasource.username},
                        "password": ${orginDatasource.password},
                        "path": [
                            <#list orginDatasource.paths as path>
                                ${path}<#if path_has_next>,</#if>
                            </#list>
                        ],
                        "column": [
                            <#list orginDatasource.columns as column>
                                {
                                    ${column.index!},
                                    ${column.type},
                                    ${column.format}
                                }
                                ${column}<#if column_has_next>,</#if>
                            </#list>
                        ],
                        "encoding": ${orginDatasource.encoding},
                        "fieldDelimiter": ${orginDatasource.fieldDelimiter}
                    }
                }