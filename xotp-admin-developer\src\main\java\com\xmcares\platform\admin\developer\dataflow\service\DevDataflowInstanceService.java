package com.xmcares.platform.admin.developer.dataflow.service;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.errors.SystemException;
import com.xmcares.platform.admin.common.errors.XbdpErrorCode;
import com.xmcares.platform.admin.developer.dataflow.model.DevDataflowProcess;
import com.xmcares.platform.admin.developer.dataflow.model.DevDataflowProcessResource;
import com.xmcares.platform.admin.developer.dataflow.model.ProcessStatusEnum;
import com.xmcares.platform.admin.developer.dataflow.repository.DevDataflowInstanceRepository;
import com.xmcares.platform.admin.developer.dataflow.repository.DevDataflowProcessResourceRepository;
import com.xmcares.platform.admin.developer.dataflow.repository.xxljob.ProcessTaskClient;
import com.xmcares.platform.admin.common.vo.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
public class DevDataflowInstanceService {
    private static final Logger LOG = LoggerFactory.getLogger(DevDataflowInstanceService.class);
    @Autowired
    private DevDataflowInstanceRepository instanceRepository;
    @Autowired
    private DevDataflowProcessResourceRepository devDataflowProcessResourceRepository;
    @Autowired
    private ProcessTaskClient processTaskClient;

    /**
     * 获取实例分页列表
     * @param devDataflowProcess 查询条件
     * @param pagination 分页信息
     * @return
     */
    public Page<DevDataflowProcess> pageDevDataflowProcess(DevDataflowProcess devDataflowProcess, Pagination pagination) {
        List<DevDataflowProcess> datasources =  instanceRepository.queryDevDataflowProcessPage(devDataflowProcess, new Page<>(pagination.getPageNo()-1, pagination.getPageSize()));
        int total = instanceRepository.countDevDataflowProcess(devDataflowProcess);
        Page<DevDataflowProcess> page = new Page<>();
        page.setData(datasources);
        page.setTotal(total);
        page.setPageNo(pagination.getPageNo());
        page.setPageSize(pagination.getPageSize());
        return page;
    }

    public Page<DevDataflowProcessResource> pageDevDataflowProcessResource(DevDataflowProcessResource devDataflowResource, Pagination pagination) {
        List<DevDataflowProcessResource> devDataflowResourceList = devDataflowProcessResourceRepository.queryDevDataflowProcessResourcePage(devDataflowResource, new Page<>(pagination.getPageNo() - 1, pagination.getPageSize()));
        int total = devDataflowProcessResourceRepository.countDevDataflowProcessResource(devDataflowResource);
        Page<DevDataflowProcessResource> page = new Page<>();
        page.setData(devDataflowResourceList);
        page.setTotal(total);
        page.setPageNo(pagination.getPageNo());
        page.setPageSize(pagination.getPageSize());
        return page;
    }


    public DevDataflowProcess detail(String id) {
        return this.instanceRepository.get(id);
    }

    @Transactional(rollbackFor = SystemException.class)
    public void begin(String id) {
        // 判断模板下是否有运行的实例

        // 判断是否未运行
        DevDataflowProcess devDataflowProcess = this.checkExist(id);
        if (ProcessStatusEnum.RUNNING.getValue().equals(devDataflowProcess.getStatus())) {
            throw new BusinessException("实例已运行");
        }

        // 0. 保留之前的运行状态
        String status = devDataflowProcess.getStatus();

        // 1. 先更新状态为开始运行
        devDataflowProcess.setStatus(ProcessStatusEnum.RUNNING.getValue());
        devDataflowProcess.setUpdateTime(new Date());
        instanceRepository.update(devDataflowProcess);

        // 2. 调用调度服务开始运行
        try {
            ReturnT<Boolean> result;
            if (ProcessStatusEnum.NOT_RUNNING.getValue().equals(status)) {
                // 触发流程控制模块开始运行
                result = processTaskClient.begin(id);
            } else if(ProcessStatusEnum.PAUSED.getValue().equals(status)) {
                // 触发流程控制模块重新运行
                result = processTaskClient.reRun(id);
            } else {
                throw new BusinessException("错误的状态");
            }
            // 2.1. 校验运行结果
            if (result.getCode() != ReturnT.SUCCESS_CODE) {
                if (result.getCode() == XbdpErrorCode.E10001.code()) {
                    return;
                } else if (result.getCode() == XbdpErrorCode.E10002.code() || result.getCode() == XbdpErrorCode.E10003.code()) {
                    throw new BusinessException(result.getMsg());
                } else {
                    throw new SystemException(result.getMsg());
                }
            }
        } catch (Exception e) {
            LOG.error("调用开始或重新运行调度服务异常", e);
            if (e instanceof BusinessException) {
                throw new SystemException(e.getMessage());
            }
            throw new SystemException("开始运行失败：服务调用异常");
        }
    }

    @Transactional(rollbackFor = SystemException.class)
    public void stop(String id) {
        // 判断是否运行中
        DevDataflowProcess devDataflowProcess = this.checkExist(id);
        if (!ProcessStatusEnum.RUNNING.getValue().equals(devDataflowProcess.getStatus())) {
            throw new BusinessException("实例未运行");
        }

        // 1. 先更新状态为未运行
        devDataflowProcess.setStatus(ProcessStatusEnum.NOT_RUNNING.getValue());
        devDataflowProcess.setUpdateTime(new Date());
        instanceRepository.update(devDataflowProcess);

        // 2. 调用调度服务停止运行
        try {
            ReturnT<Boolean> result = processTaskClient.stop(id);
            if (result.getCode() != ReturnT.SUCCESS_CODE) {
                if (result.getCode() ==  XbdpErrorCode.E10003.code()) {
                    // 任务当前是暂停运行状态
                    devDataflowProcess.setStatus(ProcessStatusEnum.PAUSED.getValue());
                    devDataflowProcess.setUpdateTime(new Date());
                    instanceRepository.update(devDataflowProcess);
                } else if (result.getCode() == XbdpErrorCode.E10002.code()) {
                    throw new BusinessException(result.getMsg());
                } else {
                    throw new SystemException(result.getMsg());
                }
            }
        } catch (Exception e) {
            LOG.error("调用停止运行调度服务异常", e);
            if (e instanceof BusinessException) {
                throw new SystemException(e.getMessage());
            }
            throw new SystemException("停止运行失败：服务调用异常");
        }
    }

    @Transactional(rollbackFor = SystemException.class)
    public void pause(String id) {
        // 判断是否运行中
        DevDataflowProcess devDataflowProcess = this.checkExist(id);
        if (!ProcessStatusEnum.RUNNING.getValue().equals(devDataflowProcess.getStatus())) {
            throw new BusinessException("实例未运行");
        }

        // 1. 先更新状态为暂停运行
        devDataflowProcess.setStatus(ProcessStatusEnum.PAUSED.getValue());
        devDataflowProcess.setUpdateTime(new Date());
        instanceRepository.update(devDataflowProcess);

        // 2. 调用调度服务暂停运行
        try {
            ReturnT<Boolean> result = processTaskClient.pause(id);
            if (result.getCode() != ReturnT.SUCCESS_CODE) {
                if (result.getCode() ==  XbdpErrorCode.E10003.code()) {
                    // 任务当前已经是暂停运行状态
                } else if (result.getCode() == XbdpErrorCode.E10002.code()) {
                    throw new BusinessException(result.getMsg());
                } else {
                    throw new SystemException(result.getMsg());
                }
            }
        } catch (Exception e) {
            LOG.error("调用暂停运行调度服务异常", e);
            if (e instanceof BusinessException) {
                throw new SystemException(e.getMessage());
            }
            throw new SystemException("暂停运行失败：服务调用异常");
        }

    }

    @Transactional(rollbackFor = SystemException.class)
    public void go(String id) {
        // 判断是否已暂停
        DevDataflowProcess devDataflowProcess = this.checkExist(id);
        if (!ProcessStatusEnum.PAUSED.getValue().equals(devDataflowProcess.getStatus())) {
            throw new BusinessException("实例未暂停");
        }

        // 1. 先更新状态为运行中
        devDataflowProcess.setStatus(ProcessStatusEnum.RUNNING.getValue());
        devDataflowProcess.setUpdateTime(new Date());
        instanceRepository.update(devDataflowProcess);

        // 2. 调用调度服务继续运行
        try {
            ReturnT<Boolean> result = processTaskClient.goOn(id);
            // 2.1. 校验运行结果
            if (result.getCode() != ReturnT.SUCCESS_CODE) {
                if (result.getCode() == XbdpErrorCode.E10001.code()) {
                    return;
                } else if (result.getCode() == XbdpErrorCode.E10002.code() || result.getCode() == XbdpErrorCode.E10003.code()) {
                    throw new BusinessException(result.getMsg());
                } else {
                    throw new SystemException(result.getMsg());
                }
            }
        } catch (Exception e) {
            LOG.error("调用继续运行调度服务异常", e);
            if (e instanceof BusinessException) {
                throw new SystemException(e.getMessage());
            }
            throw new SystemException("继续运行失败：服务调用异常");
        }

    }

    /**
     * 删除运行实例
     * @param id
     * @return
     */
    @Transactional(rollbackFor = SystemException.class)
    public Boolean instanceDelete(String id){
        ReturnT<Boolean> returnT;
        try {
            this.devDataflowProcessResourceRepository.deleteByProcessId(id);
            this.instanceRepository.delete(id);
            returnT = processTaskClient.remove(id);
        } catch (Exception e) {
            throw new SystemException("删除实例异常", e);
        }
        if(ReturnT.SUCCESS_CODE != returnT.getCode()){
            throw new SystemException(String.format("删除实例下的流程失败：%s", returnT.getMsg()));
        }
        return true;
    }


    private DevDataflowProcess checkExist(String id) {
        DevDataflowProcess devDataflowProcess = instanceRepository.get(id);
        if(devDataflowProcess == null) {
            throw new BusinessException("实例不存在");
        }
        return devDataflowProcess;
    }

    public List<DevDataflowProcess> queryByDataflowId(String dataflowId) {
        return instanceRepository.queryByDataflowIdAndStatus(dataflowId, null);
    }


}
