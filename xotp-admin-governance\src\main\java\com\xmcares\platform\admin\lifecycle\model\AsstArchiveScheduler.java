package com.xmcares.platform.admin.lifecycle.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(value = AsstArchiveScheduler.TABLE, description = "数据资产归档调度")
public class AsstArchiveScheduler {
    public static final String TABLE = "bdp_asst_archive_scheduler";

    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "名称(策略名称)")
    private String name;
    @ApiModelProperty(value = "CRON表达式")
    private String cron_expr;
    @ApiModelProperty(value = "路由策略")
    private String route_strategy;
    @ApiModelProperty(value = "阻塞策略")
    private String block_strategy;
    @ApiModelProperty(value = "执行器超时")
    private Integer executor_timeout;
    @ApiModelProperty(value = "执行器失败重试")
    private Integer executor_retry_count;
    @ApiModelProperty(value = "启动|停止")
    private String started;
    @ApiModelProperty(value = "调度器代理ID")
    private String dispatch_id;
    @ApiModelProperty(value = "create_user")
    private String create_user;
    @ApiModelProperty(value = "create_time")
    private Date create_time;
    @ApiModelProperty(value = "update_user")
    private String update_user;
    @ApiModelProperty(value = "update_time")
    private Date update_time;

    //@ApiModelProperty(value = "archive_datatable_id")
    //private String archive_datatable_id;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCron_expr() {
        return cron_expr;
    }

    public void setCron_expr(String cron_expr) {
        this.cron_expr = cron_expr;
    }

    public String getRoute_strategy() {
        return route_strategy;
    }

    public void setRoute_strategy(String route_strategy) {
        this.route_strategy = route_strategy;
    }

    public String getBlock_strategy() {
        return block_strategy;
    }

    public void setBlock_strategy(String block_strategy) {
        this.block_strategy = block_strategy;
    }

    public Integer getExecutor_timeout() {
        return executor_timeout;
    }

    public void setExecutor_timeout(Integer executor_timeout) {
        this.executor_timeout = executor_timeout;
    }

    public Integer getExecutor_retry_count() {
        return executor_retry_count;
    }

    public void setExecutor_retry_count(Integer executor_retry_count) {
        this.executor_retry_count = executor_retry_count;
    }

    public String getStarted() {
        return started;
    }

    public void setStarted(String started) {
        this.started = started;
    }

    public String getDispatch_id() {
        return dispatch_id;
    }

    public void setDispatch_id(String dispatch_id) {
        this.dispatch_id = dispatch_id;
    }

    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_user() {
        return update_user;
    }

    public void setUpdate_user(String update_user) {
        this.update_user = update_user;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }

  /*  public String getArchive_datatable_id() {
        return archive_datatable_id;
    }

    public void setArchive_datatable_id(String archive_datatable_id) {
        this.archive_datatable_id = archive_datatable_id;
    }*/
}
