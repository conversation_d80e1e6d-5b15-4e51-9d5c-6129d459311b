package com.xmcares.platform.admin.common.util;

/**
 * <AUTHOR> chenYG
 * @date : 2022/3/30 16:52
 */
public class CommonConstants {

    public static final String COMMON_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String DATAX_DISPATCH_PARAMS_SPLIT = "\n;-";
    public static final String FIELDS_SPLIT = "&&";
    public static final char KV_SPLIT = '=';
    public static final String DATAX_DISPATCH_PARAMS_PARAM = "\n;-param ";
    public static final String DATAX_DISPATCH_PARAMS_PARAM_KEY = "param";
    public static final String DATAX_DISPATCH_PARAMS_PARAM_FILEPATH = "filePath";
    public static final String DATAX_DISPATCH_PARAMS_JVM = "\n;-jvm ";
    public static final String DATAX_DISPATCH_PARAMS_JVM_KEY = "jvm";
    public static final String DATAX_DISPATCH_PARAMS_INC = "\n;-increment ";
    public static final String DATAX_DISPATCH_PARAMS_INC_KEY = "increment";
    public static final String DATAX_DISPATCH_PARAMS_INC_INCMODE = "incMode";
    public static final String DATAX_DISPATCH_PARAMS_INC_PRIMARYKEY = "primaryKey";
    public static final String DATAX_DISPATCH_PARAMS_INC_TRIGGERTIME = "triggerTime";
    public static final String DATAX_DISPATCH_PARAMS_INC_INCSTARTTIME = "incStartTime";
    public static final String DATAX_DISPATCH_PARAMS_INC_REPLACEPARAMTYPE = "replaceParamType";
    public static final String DATAX_DISPATCH_PARAMS_INC_INCSTARTVALUE = "incStartValue";
    public static final String DATAX_DISPATCH_PARAMS_INC_INCENDVALUE = "incEndValue";
    public static final String DATAX_DISPATCH_PARAMS_INC_PARTITIONINFO = "partitionInfo";
    public static final String DATAX_DISPATCH_PARAMS_INC_REPLACEPARAM = "replaceParam";
    public static final String DATAX_DISPATCH_PARAMS_SYS = "\n;-sys ";
    public static final String DATAX_DISPATCH_PARAMS_SYS_KEY = "sys";
    public static final String DATAX_DISPATCH_PARAMS_SYS_LOGID = "logId";
    public static final String DATAX_DISPATCH_PARAMS_SYS_LOGDATETIME = "logDateTime";
    public static final String DATAX_DISPATCH_PARAMS_DOWNLOAD_FILE = "\n;-downloadFile ";
    public static final String DATAX_DISPATCH_PARAMS_DOWNLOAD_FILE_KEY = "downloadFile";
    public static final String DATAX_DISPATCH_PARAMS_SYNC = "\n;-dynamicParams ";
    public static final String DATAX_DISPATCH_PARAMS_SYNC_KEY = "dynamicParams";


    public static final String DEVELOP_DISPATCH_PARAMS_PARTIAL = "\n;-devPartial ";
    public static final String DEVELOP_DISPATCH_PARAMS_PARTIAL_KEY = "devPartial";
    public static final String DEVELOP_DISPATCH_PARAMS_SYS_TASKID = "taskId";
    public static final String DEVELOP_DISPATCH_PARAMS_SYS_NODEID = "nodeId";

    public static final String DEVELOP_DISPATCH_PARAMS_INC = "\n;-inc ";
    public static final String DEVELOP_DISPATCH_PARAMS_INC_KEY = "inc";
    public static final String DEVELOP_DISPATCH_PARAMS_INC_LAST_DATE_TIME = "last_end_time";


    public static final String DATASOURCE_COMMON_PARAM_USERNAME = "username";
    public static final String DATASOURCE_COMMON_PARAM_PASSWORD = "password";
    public static final String DATASOURCE_COMMON_PARAM_URL = "url";
    public static final String DATASOURCE_COMMON_PARAM_DRIVER = "driver";
    public static final String DATASOURCE_COMMON_PARAM_ZKADDRESS = "zkAddress";
    public static final String DATASOURCE_COMMON_PARAM_DATABASENAME = "databaseName";
    public static final String DATASOURCE_COMMON_PARAM_TABLE = "table";


    public static final Integer DATAX_TASK_PROCESS_CALLBACK_CODE = *********;
    public static final Integer DATAX_KILL_PROCESS_CALLBACK_CODE = *********;
    public static final Integer DEV_TASK_PROCESS_CALLBACK_CODE = *********;





    public static final String MYSQL_DATABASE = "Unknown database";
    public static final String MYSQL_CONNEXP = "Communications link failure";
    public static final String MYSQL_ACCDENIED = "Access denied";
    public static final String MYSQL_TABLE_NAME_ERR1 = "Table";
    public static final String MYSQL_TABLE_NAME_ERR2 = "doesn't exist";
    public static final String MYSQL_SELECT_PRI = "SELECT command denied to user";
    public static final String MYSQL_COLUMN1 = "Unknown column";
    public static final String MYSQL_COLUMN2 = "field list";
    public static final String MYSQL_WHERE = "where clause";

    public static final String ORACLE_DATABASE = "ORA-12505";
    public static final String ORACLE_CONNEXP = "The Network Adapter could not establish the connection";
    public static final String ORACLE_ACCDENIED = "ORA-01017";
    public static final String ORACLE_TABLE_NAME = "table or view does not exist";
    public static final String ORACLE_SELECT_PRI = "insufficient privileges";
    public static final String ORACLE_SQL = "invalid identifier";

    public static final String PARAMS_CM_V_PT = "-Dpartition=%s";

    public static final String SPLIT_COMMA = ",";
    public static final String SPLIT_AT = "@";
    public static final String SPLIT_COLON = ";";
    public static final String SPLIT_POINT = ".";
    public static final String SPLIT_SCOLON=":";
    public static final String SPLIT_HYPHEN = "-";
    public static final String SPLIT_DIVIDE = "/";
    public static final String SPLIT_STAR = "*";
    public static final String SPLIT_QUESTION = "?";
    public static final String EQUAL = "=";
    public static final String SPLIT_AMPERSAND = "&";
    public static final String AND = "AND";
    public static final String SPACE = " ";
    public static final String STRING_BLANK = "";
    public static final String MONGO_URL_PREFIX = "mongodb://";


    public static String buildSysParamKey(String prx) {
        return DATAX_DISPATCH_PARAMS_SYS_KEY + SPLIT_POINT + prx;
    }

}
