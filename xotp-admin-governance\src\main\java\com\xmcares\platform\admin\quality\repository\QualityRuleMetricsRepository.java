package com.xmcares.platform.admin.quality.repository;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.platform.admin.quality.model.vo.QualityRuleMetricsListQueryVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/25 09:10
 */
@Repository
public class QualityRuleMetricsRepository {
    private static final Logger LOG = LoggerFactory.getLogger(QualityRuleMetricsRepository.class);

    public static final String TABLE_BDP_QLTY_RULE_SCHEDULER = "bdp_qlty_rule_scheduler";
    public static final String TABLE_BDP_QLTY_RULE_METRICS = "bdp_qlty_rule_metrics";
    public static final String TABLE_BDP_META_DATATABLE = "bdp_meta_datatable";
    public static final String TABLE_BDP_QLTY_RULE = "bdp_qlty_rule";
    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    public Page<QualityRuleMetricsListQueryVO> listQuery(String datawareId, String datatableName, String ruleName, String[] ruleSchedulerTaskIds, Integer pageNo, Integer pageSize) {
        try {

            List<QualityRuleMetricsListQueryVO> qualityDatatableListQueryVOS = xcfJdbcTemplate.<QualityRuleMetricsListQueryVO>queryForEntities(
                    " select * from ( " +
                            " select " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".id id, " +
                            " " + TABLE_BDP_QLTY_RULE + ".id ruleId, " +
                            " " + TABLE_BDP_QLTY_RULE + ".rule_name ruleName, " +
                            " " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".name ruleSchedulerTaskName, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".check_status checkStatus, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".check_time checkTime, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".metric_date metricDate, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".metric_value metricValue, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".warn_status warmStatus " +
                            " from " + TABLE_BDP_QLTY_RULE_SCHEDULER +
                            " left join " + TABLE_BDP_QLTY_RULE_METRICS +
                            "  on " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".id=" + TABLE_BDP_QLTY_RULE_METRICS + ".rule_scheduler_id " +
                            " left join " + TABLE_BDP_META_DATATABLE +
                            "  on " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".datatable_id=" + TABLE_BDP_META_DATATABLE + ".id " +
                            " left join " + TABLE_BDP_QLTY_RULE +
                            "  on " + TABLE_BDP_QLTY_RULE_METRICS + ".rule_id=" + TABLE_BDP_QLTY_RULE + ".id " +
                            " where true " +
                            ((datawareId != null && !"".equals(datawareId)) ? (" and " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".dataware_id='" + datawareId + "'  ") : "  ") +
                            ((datatableName != null && !"".equals(datatableName)) ? (" and " + TABLE_BDP_META_DATATABLE + ".name like '%" + datatableName + "%' ") : " ") +
                            ((ruleName != null && !"".equals(ruleName)) ? (" and " + TABLE_BDP_QLTY_RULE + ".rule_name like '%" + ruleName + "%' ") : "  ") +
                            ((ruleSchedulerTaskIds != null && ruleSchedulerTaskIds.length > 0) ? (" and " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".id in " + Arrays.stream(ruleSchedulerTaskIds).collect(Collectors.joining(",", "(", ")")) + "  " ) : "  ") +
                            " group by " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".id " +
                            " ) t " +
                            " limit  " + ((pageNo - 1) * pageSize) + "," + pageSize
                    ,
                    new Object[]{},
                    QualityRuleMetricsListQueryVO.class
            );
            Integer total = xcfJdbcTemplate.<Integer>queryForObject(
                    " select " +
                            " IFNULL(( " +
                            " select " +
                            " count(*) total " +
                            " from ( " +
                            " select " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".id id, " +
                            " " + TABLE_BDP_QLTY_RULE + ".id ruleId, " +
                            " " + TABLE_BDP_QLTY_RULE + ".rule_name ruleName, " +
                            " " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".name ruleSchedulerTaskName, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".check_status checkStatus, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".check_time checkTime, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".metric_date metricDate, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".metric_value metricValue, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".warn_status warmStatus " +
                            " from " + TABLE_BDP_QLTY_RULE_SCHEDULER +
                            " left join " + TABLE_BDP_QLTY_RULE_METRICS +
                            "  on " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".id=" + TABLE_BDP_QLTY_RULE_METRICS + ".rule_scheduler_id " +
                            " left join " + TABLE_BDP_META_DATATABLE +
                            "  on " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".datatable_id=" + TABLE_BDP_META_DATATABLE + ".id " +
                            " left join " + TABLE_BDP_QLTY_RULE +
                            "  on " + TABLE_BDP_QLTY_RULE_METRICS + ".rule_id=" + TABLE_BDP_QLTY_RULE + ".id " +
                            " where true " +
                            ((datawareId != null && !"".equals(datawareId)) ? (" and " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".dataware_id='" + datawareId + "'  ") : "  ") +
                            ((datatableName != null && !"".equals(datatableName)) ? (" and " + TABLE_BDP_META_DATATABLE + ".name like '%" + datatableName + "%' ") : " ") +
                            ((ruleName != null && !"".equals(ruleName)) ? (" and " + TABLE_BDP_QLTY_RULE + ".rule_name like '%" + ruleName + "%' ") : "  ") +
                            ((ruleSchedulerTaskIds != null && ruleSchedulerTaskIds.length > 0) ? (" and " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".id in " + Arrays.stream(ruleSchedulerTaskIds).collect(Collectors.joining(",", "(", ")")) + "  " ) : "  ") +
                            " group by " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".id " +
                            " ) t " +
                            " ),0 ) total "
                    ,
                    Integer.class,
                    new Object[]{}
            );

            Page<QualityRuleMetricsListQueryVO> qualityRuleMetricsListQueryVOPage = new Page<QualityRuleMetricsListQueryVO>();

            qualityRuleMetricsListQueryVOPage.setData(qualityDatatableListQueryVOS);
            qualityRuleMetricsListQueryVOPage.setPageNo(pageNo);
            qualityRuleMetricsListQueryVOPage.setPageSize(pageSize);
            qualityRuleMetricsListQueryVOPage.setTotal(total);
            return qualityRuleMetricsListQueryVOPage;
        } catch (Exception e) {
            LOG.warn("listQuery", e);
            return null;
        }
    }

}
