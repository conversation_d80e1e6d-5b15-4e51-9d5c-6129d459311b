package com.xmcares.platform.admin.developer.common.enums;

import org.apache.commons.lang3.StringUtils;

/** 数据开发调度类型定义 */
public enum DeveloperDispatchType {

    NONE("未知", "NONE"),
    CRON("CRON表达式", "CRON"),
    PRE_TASK("等待前一个运行", "PRE_TASK"),
    STREAM("永久运行", "STREAM")
    ;

    /** 任务中文标识 */
    private String title;
    private String xxlJobDispatchType;

    DeveloperDispatchType(String title, String xxlJobDispatchType) {
        this.title = title;
        this.xxlJobDispatchType = xxlJobDispatchType;
    }

    public String getTitle() {
        return title;
    }

    public static DeveloperDispatchType match(String type, DeveloperDispatchType def) {
        if (StringUtils.isEmpty(type)) {
            return def;
        }
        try {
            return DeveloperDispatchType.valueOf(type.toUpperCase());
        } catch (Exception e) {
            return def;
        }
    }

    public String getXxlJobDispatchType() {
        return xxlJobDispatchType;
    }
}
