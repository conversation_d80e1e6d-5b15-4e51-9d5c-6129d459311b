package com.xmcares.platform.admin.asset.model.vo;

import com.xmcares.platform.admin.asset.model.MetaDatatable;
import com.xmcares.platform.admin.asset.model.MetaDatatableColumn;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/4 14:29
 */
public class DatatableAllQueryVO {
    private MetaDatatable metaDatatable;
    private List<MetaDatatableColumn> metaDatatableColumns;

    public MetaDatatable getMetaDatatable() {
        return metaDatatable;
    }

    public void setMetaDatatable(MetaDatatable metaDatatable) {
        this.metaDatatable = metaDatatable;
    }

    public List<MetaDatatableColumn> getMetaDatatableColumns() {
        return metaDatatableColumns;
    }

    public void setMetaDatatableColumns(List<MetaDatatableColumn> metaDatatableColumns) {
        this.metaDatatableColumns = metaDatatableColumns;
    }
}
