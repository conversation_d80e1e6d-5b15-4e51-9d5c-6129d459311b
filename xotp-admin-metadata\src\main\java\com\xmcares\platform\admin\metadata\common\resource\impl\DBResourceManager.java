package com.xmcares.platform.admin.metadata.common.resource.impl;

import com.xmcares.framework.commons.util.io.FileUtils;
import com.xmcares.platform.admin.common.datasource.DataSourceOptions;
import com.xmcares.platform.admin.common.util.HashUtils;
import com.xmcares.platform.admin.metadata.common.resource.BaseResourceManager;
import com.xmcares.platform.admin.metadata.common.resource.IResourceHolder;
import com.xmcares.platform.admin.metadata.database.model.DatasourceResource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

public class DBResourceManager extends BaseResourceManager<DataSourceOptions> {
    private static final Logger LOG = LoggerFactory.getLogger(DBResourceManager.class);
    private Map<String, DatasourceResource> resources;
    private List<String> types = null;
    @Override
    public void init(IResourceHolder holder, DataSourceOptions param) {
        super.init(holder, param);
        resources = new HashMap<>();
        this.types = new ArrayList<>();
        Set<String> allKey = param.keySet();
        if (CollectionUtils.isEmpty(allKey)) { return; }
        List<String> resourceTypes = allKey.stream().filter(key -> key.length() > 4 && "PATH".equalsIgnoreCase(key.substring(key.length() - 4))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resourceTypes)) {return;}
        for (String type : resourceTypes) {
            DatasourceResource resource = parser((Map<String, Object>)param.get(type));
            if (resource != null) {
                this.types.add(type);
                resources.put(type, resource);
            }
        }
    }

    @Override
    public String addResource(String type, InputStream stream) {
        super.addResource(type, stream);
        if (resources.containsKey(type)) {
            String path = buildRealPath(resources.get(type).getPath(), param.getId() + "", type);
            holder.add(path, stream);
            return path;
        }
        return null;
    }

    @Override
    public void removeResource(String type) {
        super.removeResource(type);
        if (resources.containsKey(type)) {
            holder.remove(buildRealPath(resources.get(type).getPath(), param.getId() + "", type));
        }
    }

    @Override
    public void downloadResource(String type, OutputStream stream) {
        super.downloadResource(type, stream);
        if (resources.containsKey(type)) {
            holder.read(resources.get(type).getPath(), stream);
        }
    }

    public static String buildRealPath(String path, String id, String type) {
        String name = FileUtils.getName(path);
        int hashCode = HashUtils.apHash(type);
        if (hashCode < 0) { hashCode = -hashCode; }
        return StringUtils.replace(path, name, id + "_" + hashCode  + "_" + name)
                .replace("datasource", "DATASOURCE_RESOURCE_USE");
    }

    public static DatasourceResource parser(Map<String, Object> json) {
        try {
            if (json.containsKey("name") && json.containsKey("key")) {
                DatasourceResource result = new DatasourceResource();
                result.setName((String) json.get("name"));
                result.setPath((String) json.get("key"));
                return result;
            }
        } catch (Exception e) {
            LOG.error("解析资源字符串异常, " + json, e);
        }
        return null;
    }

    @Override
    public List<String> types() {
        return types;
    }

    @Override
    public String findPathByType(String type) {
        if (param.containsKey(type)) {
            return (String) ((Map<String, Object>)param.get(type)).get("path");
        }
        return null;
    }

    @Override
    public void doChanger(DataSourceOptions oldDataSourceOptions, IDoChange doChange) {
        Map<String, DatasourceResource> oldResources = new HashMap<>();
        List<String> types = types();
        if (CollectionUtils.isNotEmpty(types)) {
            for (String type : types) {
                if (oldDataSourceOptions.containsKey(type)) {
                    DatasourceResource parser = parser((Map<String, Object>) oldDataSourceOptions.get(type));
                    if (parser != null) {
                        oldResources.put(type, parser);
                    }
                }
            }
        }

        // 两个都是空的， 直接返回
        if (MapUtils.isEmpty(oldResources) && MapUtils.isEmpty(resources)) {
            return;
        }

        // 旧的为空，新的不为空，全部新增
        if (MapUtils.isEmpty(oldResources) && MapUtils.isNotEmpty(resources)) {
            for (Map.Entry<String, DatasourceResource> echo : resources.entrySet()) {
                doChange.doAdd(echo.getKey());
            }
            return;
        }

        // 旧的不为空，新的为空，全部删除
        if (MapUtils.isNotEmpty(oldResources) &&  MapUtils.isEmpty(resources)) {
            for (Map.Entry<String, DatasourceResource> echo : oldResources.entrySet()) {
                doChange.doRemove(echo.getKey());
            }
            return;
        }

        List<String> needRemoves = new ArrayList<>();
        List<String> needAdds = new ArrayList<>();
        List<String> changePaths = new ArrayList<>();

        for (Map.Entry<String, DatasourceResource> oldEcho : oldResources.entrySet()) {
            if (resources.containsKey(oldEcho.getKey())) {
                if (oldEcho.getValue() != null && !resources.get(oldEcho.getKey()).getName().equals(oldEcho.getValue().getName())) {
                    // 移除旧的， 上传新的
                    needRemoves.add(oldEcho.getKey());
                    needAdds.add(oldEcho.getKey());
                } else {
                    changePaths.add(oldEcho.getKey());
                }
            } else {
                // 旧的不在新的中， 删除
                needRemoves.add(oldEcho.getKey());
            }
        }

        for (String needRemove : needRemoves) {
            doChange.doRemove(needRemove);
        }

        for (String needAdd : needAdds) {
            doChange.doAdd(needAdd);
        }

        for (String changePath : changePaths) {
            doChange.doChange(changePath);
        }

    }
}
