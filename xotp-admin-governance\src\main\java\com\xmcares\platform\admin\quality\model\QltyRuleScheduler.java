package com.xmcares.platform.admin.quality.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/12 14:23
 */
@ApiModel(value = QltyRuleScheduler.TABLE, description = "数据质量规则调度")
public class QltyRuleScheduler implements Serializable {
    public static final String TABLE = "bdp_qlty_rule_scheduler";
    @JsonAlias({"id"})
    @ApiModelProperty(value = "ID")
    private String id;
    @JsonAlias({"datawareId"})
    @ApiModelProperty(value = "仓库ID")
    private String dataware_id;
    @JsonAlias({"datatableId"})
    @ApiModelProperty(value = "数据表ID")
    private String datatable_id;
    @JsonAlias({"datatableName"})
    @ApiModelProperty(value = "数据表名")
    private String datatable_name = "";
    @JsonAlias({"name"})
    @ApiModelProperty(value = "名称(调度策略名)")
    private String name;
    @JsonAlias({"type"})
    @ApiModelProperty(value = "类型（CRON：离线周期，TASK：关联生产任务）")
    private String type;
    @JsonAlias({"cronExpr"})
    @ApiModelProperty(value = "CRON表达式")
    private String cron_expr;
    @JsonAlias({"taskId"})
    @ApiModelProperty(value = "关联生产任务ID")
    private String task_id;
    @JsonAlias({"routeStrategy"})
    @ApiModelProperty(value = "路由策略")
    private String route_strategy;
    @JsonAlias({"blockStrategy"})
    @ApiModelProperty(value = "阻塞策略")
    private String block_strategy;
    @JsonAlias({"executorTimeout"})
    @ApiModelProperty(value = "执行器超时")
    private Integer executor_timeout;
    @JsonAlias({"executorRetryCount"})
    @ApiModelProperty(value = "执行器失败重试")
    private Integer executor_retry_count;
    @JsonAlias({"started"})
    @ApiModelProperty(value = "启动|停止（0：停止，1：启动）")
    private String started;
    @JsonAlias({"dispatchId"})
    @ApiModelProperty(value = "调度器代理ID")
    private Long dispatch_id;
    @JsonAlias({"createUser"})
    @ApiModelProperty(value = "create_user")
    private String create_user;
    @JsonAlias({"createTime"})
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "create_time")
    private String create_time;
    @JsonAlias({"updateUser"})
    @ApiModelProperty(value = "update_user")
    private String update_user;
    @JsonAlias({"updateTime"})
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "update_time")
    private String update_time;
    @JsonAlias({"executeParams"})
    @ApiModelProperty(value = "用户执行参数")
    private String execute_params;
    @JsonAlias({"callBackParams"})
    @ApiModelProperty(value = "任务回调参数")
    private String call_back_params;

    public static String getTABLE() {
        return TABLE;
    }

    @JsonProperty("id")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    @JsonProperty("datawareId")
    public String getDataware_id() {
        return dataware_id;
    }

    public void setDataware_id(String dataware_id) {
        this.dataware_id = dataware_id;
    }
    @JsonProperty("datatableId")
    public String getDatatable_id() {
        return datatable_id;
    }

    public void setDatatable_id(String datatable_id) {
        this.datatable_id = datatable_id;
    }
    @JsonProperty("datatableName")
    public String getDatatable_name() {
        return datatable_name;
    }

    public void setDatatable_name(String datatable_name) {
        this.datatable_name = datatable_name;
    }
    @JsonProperty("name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    @JsonProperty("type")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    @JsonProperty("cronExpr")
    public String getCron_expr() {
        return cron_expr;
    }

    public void setCron_expr(String cron_expr) {
        this.cron_expr = cron_expr;
    }
    @JsonProperty("taskId")
    public String getTask_id() {
        return task_id;
    }

    public void setTask_id(String task_id) {
        this.task_id = task_id;
    }
    @JsonProperty("routeStrategy")
    public String getRoute_strategy() {
        return route_strategy;
    }

    public void setRoute_strategy(String route_strategy) {
        this.route_strategy = route_strategy;
    }
    @JsonProperty("blockStrategy")
    public String getBlock_strategy() {
        return block_strategy;
    }

    public void setBlock_strategy(String block_strategy) {
        this.block_strategy = block_strategy;
    }
    @JsonProperty("executorTimeout")
    public Integer getExecutor_timeout() {
        return executor_timeout;
    }

    public void setExecutor_timeout(Integer executor_timeout) {
        this.executor_timeout = executor_timeout;
    }
    @JsonProperty("executorRetryCount")
    public Integer getExecutor_retry_count() {
        return executor_retry_count;
    }

    public void setExecutor_retry_count(Integer executor_retry_count) {
        this.executor_retry_count = executor_retry_count;
    }
    @JsonProperty("started")
    public String getStarted() {
        return started;
    }

    public void setStarted(String started) {
        this.started = started;
    }
    @JsonProperty("dispatchId")
    public Long getDispatch_id() {
        return dispatch_id;
    }

    public void setDispatch_id(Long dispatch_id) {
        this.dispatch_id = dispatch_id;
    }
    @JsonProperty("createUser")
    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }
    @JsonProperty("createTime")
    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }
    @JsonProperty("updateUser")
    public String getUpdate_user() {
        return update_user;
    }

    public void setUpdate_user(String update_user) {
        this.update_user = update_user;
    }
    @JsonProperty("updateTime")
    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    @JsonProperty("executeParams")
    public String getExecute_params() {
        return execute_params;
    }

    public void setExecute_params(String execute_params) {
        this.execute_params = execute_params;
    }
    @JsonProperty("callBackParams")
    public String getCall_back_params() {
        return call_back_params;
    }

    public void setCall_back_params(String call_back_params) {
        this.call_back_params = call_back_params;
    }
}
