/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2023/5/26
 */
package com.xmcares.platform.admin.common.datasource.mq;

import java.util.Collection;

/**
 * Mq 客户端接口
 * <AUTHOR>
 * @since 1.4.1
 */
public interface MqClient extends MqAdmin{

    default void sendMessage(String topic, byte[] body) {
        sendMessage(topic, new MessageHeaders(null), body);
    }

    default void pullMessage(String topic, String group, Callback callback) {
        pullMessage(topic, group, new MessageHeaders(null), callback);
    }

    void sendMessage(String topic, MessageHeaders headers, byte[] body);

    void pullMessage(String topic, String group, MessageHeaders headers, Callback callback);

    void addMessageListener(String topic, String group, MqMessageListener listener);

    void removeMessageListener(String topic, String group, MqMessageListener listener);

    @FunctionalInterface
    interface Callback {
        boolean invoke(Collection<byte[]> messages);
    }

    @FunctionalInterface
    interface MqMessageListener {
        void onMessage(MessageHeaders headers, byte[] message);
    }
}
