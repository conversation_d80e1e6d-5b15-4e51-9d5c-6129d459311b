package com.xmcares.platform.admin.metadata.database.service;

import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.errors.SystemException;
import com.xmcares.platform.admin.common.database.DatabaseMetaQuery;
import com.xmcares.platform.admin.common.database.DatabaseMetaQueryFactory;
import com.xmcares.platform.admin.common.database.metainfo.ColumnInfo;
import com.xmcares.platform.admin.common.database.metainfo.TableInfo;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import com.xmcares.platform.admin.metadata.database.model.DatasourceColumn;
import com.xmcares.platform.admin.metadata.database.model.DatasourceTable;
import com.xmcares.platform.admin.metadata.database.repository.DatasourceColumnRepository;
import com.xmcares.platform.admin.metadata.database.repository.DatasourceRepository;
import com.xmcares.platform.admin.metadata.database.repository.DatasourceTableRepository;
import com.xmcares.platform.admin.metadata.database.vo.DatasourceTableDetailVo;
import com.xmcares.platform.admin.metadata.database.vo.DatatableSyncResultVo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/16 16:31
 */
@Component
public class DatasourceTableService {

    private static final Logger LOG = LoggerFactory.getLogger(DatasourceTableService.class);

    @Autowired
    private DatasourceTableRepository datasourceTableRepository;

    @Autowired
    private DatasourceColumnRepository datasourceColumnRepository;

    @Autowired
    private DatasourceRepository datasourceRepository;

    @Autowired
    private DatabaseMetaQueryFactory metaQueryFactory;

    @Transactional(rollbackFor = SystemException.class)
    public List<DatatableSyncResultVo> syncTables(String datasourceId, List<String> tables) {
        List<DatatableSyncResultVo> result = new ArrayList<>();
        //1. 获取数据源
        Datasource datasource = datasourceRepository.getDatasource(datasourceId);
        if (datasource == null) {
            throw new BusinessException("获取数据源信息失败");
        }
        //2. 从数据源中获取表与列的信息
        List<TableInfo> tableInfos = new ArrayList<>();
        DatabaseMetaQuery metaQuery = metaQueryFactory.getDatabaseMetaQuery(datasource.toDataSourceOptions());
        for (String table : tables) {
            try {
                tableInfos.add(metaQuery.getTableInfoWithColumns(table));
            } catch (Exception e) {
                result.add(DatatableSyncResultVo.addError(table, "获取表信息失败！" + e.getMessage()));
            }
        }

        //3. 同步这些信息到我们的数据源中
        if (CollectionUtils.isNotEmpty(tableInfos)) {
            List<DatasourceTable> datasourceTables = new ArrayList<>();
            List<DatasourceColumn> datasourceColumns = new ArrayList<>();
            tableInfos.forEach(tableInfo -> {
                DatasourceTable createResult = DatasourceTable.createBy(datasourceId, tableInfo);
                datasourceTables.add(createResult);
                datasourceColumns.addAll(DatasourceColumn.createBy(createResult, tableInfo.getColumns()));
            });
            try {
                datasourceTableRepository.batchSave(datasourceTables);
                datasourceColumnRepository.batchSave(datasourceColumns);
                datasourceTables.forEach((table) -> {
                    result.add(DatatableSyncResultVo.addOk(table.getName()));
                });
            } catch (Exception e) {
                LOG.error("同步数据源表，将数据批量入库时异常", e);
                throw new SystemException("数据批量保存失败");
            }
        }
        return result;
    }

    @Transactional(rollbackFor = SystemException.class)
    public void removeTables(List<String> tableIds) {
        try {
            datasourceTableRepository.removeAll(tableIds);
            datasourceColumnRepository.removeByTableIds(tableIds);
        } catch (Exception e) {
            LOG.error("移除表记录异常", e);
            throw new SystemException("数据删除异常");
        }
    }

    public List<DatasourceTable> listDatasourceTable(String datasourceId) {
        return datasourceTableRepository.listDatasourceTable(datasourceId);
    }

    public DatasourceTableDetailVo listDatasourceTableDetail(String datasourceTableId) {
        DatasourceTable datasourceTable = datasourceTableRepository.getDatasourceTable(datasourceTableId);
        List<DatasourceColumn> datasourceColumns = datasourceColumnRepository.listDatasourceColumns(datasourceTableId);

        return new DatasourceTableDetailVo(datasourceTable, datasourceColumns);
    }


    public DatasourceTableDetailVo listDatasourceTableDetailByName(String datasourceTableName,String dataSourceId) {
        DatasourceTable datasourceTable = datasourceTableRepository.getDatasourceTableByName(datasourceTableName,dataSourceId);
        List<DatasourceColumn> datasourceColumns = datasourceColumnRepository.listDatasourceColumnsByName(datasourceTableName,dataSourceId);

        return new DatasourceTableDetailVo(datasourceTable, datasourceColumns);
    }

    public boolean deleteDetail(String datasourceTableId) {
        int deleteDatasourcetable = datasourceTableRepository.deleteDatasourceTable(datasourceTableId);
        int deleteDatasourceColumns = datasourceColumnRepository.deleteDatasourceColumns(datasourceTableId);
        return deleteDatasourcetable > 0 && deleteDatasourceColumns > 0;
    }

    public boolean updateTable(DatasourceTable datasourceTable) {
        return datasourceTableRepository.updateTable(datasourceTable);
    }

    public List<DatasourceColumn> listSyncFields(String datasourceTableId, DatasourceService datasourceService) {
        // 1. 获取表信息
        DatasourceTable datatable = Optional.of(datasourceTableRepository.getDatatable(datasourceTableId)).orElseThrow(() -> new BusinessException("未找到表信息"));
        // 2. 获取数据源信息
        Datasource datasource = Optional.of(datasourceService.getDatasource(datatable.getDatasourceId())).orElseThrow(() -> new BusinessException("未找到数据源信息"));
        // 3. 获取数据源下的表的列信息
        DatabaseMetaQuery metaQuery = metaQueryFactory.getDatabaseMetaQuery(datasource.toDataSourceOptions());
        List<ColumnInfo> columns = metaQuery.getColumnInfos(datatable.getName());
        // 4. 加载数据库中保存的列信息
        Map<String, DatasourceColumn> datasourceColumns = Optional.of(datasourceColumnRepository.listSyncDatasourceColumn(datasourceTableId)).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(DatasourceColumn::getName, t->t));
        for (ColumnInfo column : columns) {
            if (!datasourceColumns.containsKey(column.getName())) {
                datasourceColumns.put(column.getName(), DatasourceColumn.createBy(datatable, column));
            }
        }
        return new ArrayList<>(datasourceColumns.values());
    }

    public boolean saveColumns(String datasourceTableId, List<DatasourceColumn> datasourceColumns) {
        return datasourceColumnRepository.saveColumns(datasourceTableId, datasourceColumns);
    }


}
