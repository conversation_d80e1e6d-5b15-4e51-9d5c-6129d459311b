{
                    "name": "hdfswriter",
                    "parameter": {
                        "defaultFS": ${destDatasource.defaultFS},
                        "fileType": ${destDatasource.fileType},
                        "path": ${destDatasource.path},
                        "fileName": ${destDatasource.fileName},
                        "column": [
<#list destDatasource.columns as column>
                         {
    ${column.name!},
    ${column.type},
                       }
    ${column}<#if column_has_next>,</#if>
</#list>
                        ],
                        "writeMode": ${destDatasource.writerMode},
                        "fieldDelimiter": ${destDatasource.fieldDelimiter},
                        "compress":${destDatasource.compress}
                    }
                }