package com.xmcares.platform.admin.metadata.database.web;

import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.database.DatabaseMetaQuery;
import com.xmcares.platform.admin.common.database.DatabaseMetaQueryFactory;
import com.xmcares.platform.admin.common.database.metainfo.TableInfo;
import com.xmcares.platform.admin.common.util.ListUtils;
import com.xmcares.platform.admin.common.validation.Insert;
import com.xmcares.platform.admin.common.validation.Update;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import com.xmcares.platform.admin.metadata.database.model.DatasourceColumn;
import com.xmcares.platform.admin.metadata.database.model.DatasourceTable;
import com.xmcares.platform.admin.metadata.database.service.DatasourceService;
import com.xmcares.platform.admin.metadata.database.service.DatasourceTableService;
import com.xmcares.platform.admin.metadata.database.vo.DatacolumnSaveVo;
import com.xmcares.platform.admin.metadata.database.vo.DatasourceTableDetailVo;
import com.xmcares.platform.admin.metadata.database.vo.DatatableSyncResultVo;
import com.xmcares.platform.admin.metadata.database.vo.DatatableUnSyncResultVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/16 16:30
 */
@Api(value = "数据源表信息访问控制器")
@RestController
@RequestMapping("/datasource-table")
public class DatasourceTableController {

    private static final Set<String> SYNC_DATASOURCE = new HashSet<>();
    private static final Object SYNC_DATASOURCE_LOCK = new Object();

    @Autowired
    DatasourceTableService datasourceTableService;
    @Autowired
    private DatasourceService datasourceService;

    @Autowired
    private DatabaseMetaQueryFactory metaQueryFactory;

    @GetMapping("/unSync-find")
    @ResponseBody
    @ApiOperation("获取所有未同步的表")
    public DatatableUnSyncResultVo findUnSyncTables(@RequestParam(name = "datasourceId") String datasourceId) {
        //1. 获取数据源
        Datasource datasource = datasourceService.get(datasourceId);
        if (datasource == null) {
            throw new BusinessException("未找到数据源信息");
        }
        //2. 获取已经同步的表
        List<String> findSyncTableResult = Optional.of(datasourceTableService.listDatasourceTable(datasourceId)).orElse(new ArrayList<>())
                .stream().map(DatasourceTable::getName).collect(Collectors.toList());
        //3. 获取数据源下所有的表
        DatabaseMetaQuery metaQuery = metaQueryFactory.getDatabaseMetaQuery(datasource.toDataSourceOptions());
        List<TableInfo> tableInfos = metaQuery.getTableInfos();
        List<String> datasourceResults = tableInfos.stream().map(TableInfo::getName).collect(Collectors.toList());
        //4. 去掉已同步的, 剩下就是未同步的
        datasourceResults.removeAll(findSyncTableResult);
        return new DatatableUnSyncResultVo(datasourceId, datasourceResults, findSyncTableResult);
    }

    @PostMapping("/table-sync")
    @ResponseBody
    public List<DatatableSyncResultVo> syncTable(@RequestBody DatatableUnSyncResultVo datatableSyncVo) {
        synchronized (SYNC_DATASOURCE_LOCK) {
            if (SYNC_DATASOURCE.contains(datatableSyncVo.getDatasourceId())) {
                throw new BusinessException("当前数据源正在同步中，请勿重复操作！");
            } else {
                SYNC_DATASOURCE.add(datatableSyncVo.getDatasourceId());
            }
        }
        try {
            List<DatatableSyncResultVo> result = new ArrayList<>();
            //1. 获取数据库中已经同步的表
            List<DatasourceTable> findResult = Optional.of(datasourceTableService.listDatasourceTable(datatableSyncVo.getDatasourceId())).orElse(new ArrayList<>());
            //2. 找两个表的不同
            List<String> collA = findResult.stream().map(DatasourceTable::getName).collect(Collectors.toList());
            List<String> collB = Optional.of(datatableSyncVo.getSyncTables()).orElse(new ArrayList<>());
            ListUtils.Different findDiffResult = ListUtils.findDifferent(collA, collB);
            //3. 新增需要新增的
            if (CollectionUtils.isNotEmpty(findDiffResult.getAdds())) {
                try {
                    result.addAll(datasourceTableService.syncTables(datatableSyncVo.getDatasourceId(), findDiffResult.getAdds()));
                } catch (Exception e) {
                    findDiffResult.getAdds().forEach(table -> result.add(DatatableSyncResultVo.addError(table, e.getMessage())));
                }
            }
            //4. 删除需要删除的
            if (CollectionUtils.isNotEmpty(findDiffResult.getRemoves())) {
                try {
                    datasourceTableService.removeTables(findResult.stream()
                            .filter(table -> findDiffResult.getRemoves().contains(table.getName()))
                            .map(DatasourceTable::getId)
                            .collect(Collectors.toList()));
                    findDiffResult.getRemoves().forEach(table -> result.add(DatatableSyncResultVo.removeOk(table)));
                } catch (Exception e) {
                    findDiffResult.getRemoves().forEach(table -> result.add(DatatableSyncResultVo.removeError(table, e.getMessage())));
                }
            }
            return result;
        } finally {
            synchronized (SYNC_DATASOURCE_LOCK) {
                SYNC_DATASOURCE.remove(datatableSyncVo.getDatasourceId());
            }
        }
    }


    @GetMapping("/list-query")
    @ResponseBody
    public List<DatasourceTable> listDatasourceTable(@RequestParam(name = "datasourceId") String datasourceId) {
        return datasourceTableService.listDatasourceTable(datasourceId);
    }

    @GetMapping("/detail-query")
    @ResponseBody
    public DatasourceTableDetailVo listDatasourceTableDetail(@RequestParam(name = "tableId") String tableId) {
        return datasourceTableService.listDatasourceTableDetail(tableId);
    }

    @GetMapping("/detail-queryByName")
    @ResponseBody
    public DatasourceTableDetailVo listDatasourceTableDetailByName(@RequestParam(name = "tableName") String tableName,@RequestParam(name = "dataSourceId") String dataSourceId) {
        return datasourceTableService.listDatasourceTableDetailByName(tableName,dataSourceId);
    }

    @GetMapping("/delete")
    @ResponseBody
    public boolean deleteDetail(@RequestParam(name = "tableId") String tableId) {
        return datasourceTableService.deleteDetail(tableId);
    }

    @PostMapping("/update")
    @ResponseBody
    public boolean updateTable(@RequestBody @Validated({Update.class}) DatasourceTable datasourceTable) {
        return datasourceTableService.updateTable(datasourceTable);
    }

    @GetMapping("/sync-query")
    @ResponseBody
    public List<DatasourceColumn> listSyncFields(@RequestParam(name = "tableId") String tableId) {
        return datasourceTableService.listSyncFields(tableId, datasourceService);
    }

    @PostMapping("/columns-save")
    @ResponseBody
    public boolean saveColumns(@RequestBody @Validated({Insert.class}) DatacolumnSaveVo datacolumnSaveVo) {
        return datasourceTableService.saveColumns(datacolumnSaveVo.getTableId(), datacolumnSaveVo.getColumns());
    }
}
