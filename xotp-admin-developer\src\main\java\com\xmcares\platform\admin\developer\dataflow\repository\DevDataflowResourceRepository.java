package com.xmcares.platform.admin.developer.dataflow.repository;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.developer.dataflow.model.DevDataflowResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class DevDataflowResourceRepository {

    public static final String TABLE_BDP_DEV_DATAFLOW_RESOURCE = "bdp_dev_dataflow_resource";

    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    public Boolean add(DevDataflowResource devDataflowResource) {
        Map map = DBUtils.insertSqlAndObjects(devDataflowResource, DevDataflowResource.class, TABLE_BDP_DEV_DATAFLOW_RESOURCE);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.update(sql, args) > 0;
    }

    /**
     * 查询定义列表根据条件
     *
     * @param devDataflowResource 参数
     * @param page                分页信息
     * @return
     */
    public List<DevDataflowResource> queryDevDataflowResourcePage(DevDataflowResource devDataflowResource, Page<DevDataflowResource> page) {
        Map<String, Object> conditions = buildCondition(devDataflowResource);
        Map<String, Object> map = DBUtils.queryList(DevDataflowResource.TABLE, conditions);
        String sql = DBUtils.getSql(map) + " ORDER BY upload_time Desc ";
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntities(sql, args, page, DevDataflowResource.class);
    }

    /**
     * 统计数据流
     *
     * @param devDataflowResource 查询参数
     * @return
     */
    public int countDevDataflowResource(DevDataflowResource devDataflowResource) {
        Map<String, Object> conditions = buildCondition(devDataflowResource);
        Map<String, Object> map = DBUtils.queryCount(DevDataflowResource.TABLE, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForObject(sql, Integer.class, args);
    }
    private Map<String, Object> buildCondition(DevDataflowResource devDataflowResource) {
        Map<String, Object> conditions = new HashMap<>();
        if (devDataflowResource != null) {
            if (devDataflowResource.getName() != null && !devDataflowResource.getName().equals("")) {
                conditions.put("name", "%" + devDataflowResource.getName() + "%");
            }
            if (devDataflowResource.getRemark() != null && !devDataflowResource.getRemark().equals("")) {
                conditions.put("remark", "%" + devDataflowResource.getRemark() + "%");
            }
            if (devDataflowResource.getDataflowId() != null && !devDataflowResource.getDataflowId().equals("")) {
                conditions.put("dataflow_id", devDataflowResource.getDataflowId());
            }
        }
        return conditions;
    }



    /**
     * 根据主键将记录标记为删除
     * @param id 主键
     * @return
     */
    public Boolean updateToDeletedById(String id) {
        String sql = "update " + DevDataflowResource.TABLE + " set deleted=? where id= ? ";
        return xcfJdbcTemplate.update(sql, new Object[]{YNEnum.YES.getIntCode(),  id}) > 0;
    }
    /**
     * 根据主键删除
     * @param id 主键
     * @return
     */
    public Boolean delete(String id) {
        String sql = "DELETE from " + DevDataflowResource.TABLE + " where id= ? ";
        return xcfJdbcTemplate.update(sql, id) > 0;
    }
    /**
     * 根据外键删除
     * @param dataFlowId 流程定义主键
     * @return
     */
    public Boolean deleteByDataflowId(String dataFlowId) {
        String sql = "DELETE from " + DevDataflowResource.TABLE + " where dataflow_id= ? ";
        return xcfJdbcTemplate.update(sql, dataFlowId) > 0;
    }
    public DevDataflowResource getById(String id) {
        HashMap<String, Object> conditions = new HashMap<>();
        conditions.put("id", String.valueOf(id));
        Map<String, Object> map = DBUtils.queryList(DevDataflowResource.TABLE, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntity(sql, args, DevDataflowResource.class);
    }

    public List<DevDataflowResource> findByIds(List<String> ids) {
        String[] args = new String[ids.size()];
        StringBuilder sqlBuilder = new StringBuilder("SELECT * FROM ").append(DevDataflowResource.TABLE).append(" WHERE id IN ");
        sqlBuilder.append("(").append("?");
        args[0] = ids.get(0);
        if (ids.size() > 1) {
            for (int i = 1; i < ids.size(); i++) {
                sqlBuilder.append(",").append("?");
                args[i] = ids.get(i);
            }
        }
        sqlBuilder.append(")");
        return xcfJdbcTemplate.queryForEntities(sqlBuilder.toString(), args, DevDataflowResource.class);
    }

    public DevDataflowResource getByName(String name) {
        HashMap<String, Object> conditions = new HashMap<>();
        conditions.put("name", name);
        Map<String, Object> map = DBUtils.queryList(TABLE_BDP_DEV_DATAFLOW_RESOURCE, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntity(sql, args, DevDataflowResource.class);
    }
}
