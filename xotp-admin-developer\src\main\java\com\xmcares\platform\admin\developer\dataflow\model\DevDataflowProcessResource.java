package com.xmcares.platform.admin.developer.dataflow.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.common.validation.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@ApiModel(value = DevDataflowProcessResource.TABLE, description = "数据开发实例资源信息")
public class DevDataflowProcessResource implements Serializable {

    public static final String TABLE = "bdp_dev_dataflow_process_resource";

    /** ID */
    @NotNull(message = "ID不允许为空", groups = Update.class)
    @ApiModelProperty(value = "ID")
    private String id;

    /** 资源ID */
    @ApiModelProperty(value = "资源ID")
    private String processId;

    /** 创建时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date uploadTime;

    /** 更新时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 维护人ID */
    @ApiModelProperty(value = "维护人ID")
    private String uploadUser;

    /** 资源名称 */
    @ApiModelProperty(value = "资源名称")
    private String name;

    /** 资源路径 */
    @ApiModelProperty(value = "资源路径")
    private String path;

    /** 资源描述 */
    @ApiModelProperty(value = "资源描述")
    private String remark;

    public static List<DevDataflowProcessResource> createAll(String processId, List<DevDataflowResource> resources) {
        return Optional.ofNullable(resources).orElse(new ArrayList<>()).stream().map(resource-> DevDataflowProcessResource.createBy(processId, resource))
                .collect(Collectors.toList());
    }

    private static DevDataflowProcessResource createBy(String processId, DevDataflowResource resource) {
        DevDataflowProcessResource result = new DevDataflowProcessResource();
        result.setId(SnowflakeGenerator.getNextId() + "");
        result.setProcessId(processId);
        result.setUploadTime(new Date());
        result.setUpdateTime(new Date());
        result.setUploadUser(resource.getUploadUser());
        result.setName(resource.getName());
        result.setPath(resource.getPath());
        result.setRemark(resource.getRemark());
        return result;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUploadUser() {
        return uploadUser;
    }

    public void setUploadUser(String uploadUser) {
        this.uploadUser = uploadUser;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
