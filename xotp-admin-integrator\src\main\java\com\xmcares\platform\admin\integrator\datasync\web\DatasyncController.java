package com.xmcares.platform.admin.integrator.datasync.web;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.platform.admin.common.validation.Insert;
import com.xmcares.platform.admin.common.validation.Update;
import com.xmcares.platform.admin.integrator.datasync.model.Datasync;
import com.xmcares.platform.admin.integrator.datasync.service.DatasyncService;
import com.xmcares.platform.admin.integrator.datasync.vo.PublishDatasyncTask;
import com.xmcares.platform.admin.integrator.datasync.vo.QueryDatasync;
import com.xmcares.platform.admin.integrator.datasync.vo.SaveDatasync;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/2/23 09:59
 */
@Validated
@RestController
@RequestMapping("/datasync")
public class DatasyncController {

    @Autowired
    DatasyncService datasyncService;

    @GetMapping("/page-query")
    @ResponseBody
    public Page<Datasync> page(QueryDatasync queryInfo, Integer page, Integer rows) {
        Pagination pagination = new Pagination();
        pagination.setPageNo(page!=null ? page : 1);
        pagination.setPageSize(rows!=null ? rows : 10);
        return datasyncService.findByPage(queryInfo, pagination);
    }

    @GetMapping("/get")
    @ResponseBody
    public Datasync get(@NotBlank(message = "id不能为空") String id) {
        return datasyncService.get(id);
    }

    @PostMapping("/add")
    @ResponseBody
    public Boolean add(@RequestBody @Validated({Insert.class}) SaveDatasync saveDatasync) {
        datasyncService.add(saveDatasync);
        return true;
    }

    @PostMapping("/update")
    @ResponseBody
    public Boolean update(@RequestBody @Validated({Update.class}) SaveDatasync saveDatasync) {
        datasyncService.update(saveDatasync);
        return true;
    }

    @PostMapping("/publish")
    @ResponseBody
    public Boolean publish(@RequestBody @Validated() PublishDatasyncTask publishDatasyncTask) {
        datasyncService.publish(publishDatasyncTask.getId(), publishDatasyncTask.getInstanceName());
        return true;
    }

    @GetMapping("/delete")
    @ResponseBody
    public Boolean remove(@RequestParam(name = "id") String id) {

        return datasyncService.transitionToDelete(id);
    }

    @GetMapping("/isUsed")
    @ResponseBody
    public Boolean isUsed(@RequestParam(name = "datasourceId") String datasourceId) {
        return datasyncService.isUsed(datasourceId);
    }

    @GetMapping("/displayJson")
    @ResponseBody
    public String displayJson(@RequestParam(name = "id") String id) {
        Datasync datasync = datasyncService.get(id);
        if (datasync == null) {
            throw new IllegalArgumentException("数据不存在");
        }
        return datasyncService.readerJsonInfo(datasync);
    }
}

