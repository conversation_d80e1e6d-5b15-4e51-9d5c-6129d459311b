package com.xmcares.platform.admin.quality.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/12 14:23
 */
@ApiModel(value = QltyRule.TABLE, description = "数据质量规则")
public class QltyRule implements Serializable {
    public static final String TABLE = "bdp_qlty_rule";

    @ApiModelProperty(value = "id")
    private String id;

    @NotNull
    @JsonAlias({"datawareId"})
    @ApiModelProperty(value = "仓库ID")
    private String dataware_id;
    @NotNull
    @JsonAlias({"datatableId"})
    @ApiModelProperty(value = "数据表ID")
    private String datatable_id;
    @JsonAlias({"datatableName"})
    @ApiModelProperty(value = "数据表名（冗余）")
    private String datatable_name;
    @JsonAlias({"datatableNameAlias"})
    @ApiModelProperty(value = "数据表名（冗余）中文名")
    private String datatable_name_alias;
    @NotNull
    @JsonAlias({"ruleName"})
    @ApiModelProperty(value = "规则名称")
    private String rule_name;
    @NotNull
    @JsonAlias({"ruleType"})
    @ApiModelProperty(value = "规则类型（TMPL：系统模板，SQL：用户自定义SQL）")
    private String rule_type;
    @NotNull
    @JsonAlias({"ruleLevel"})
    @ApiModelProperty(value = "规则级别（TAB：表级；COL：字段级）")
    private String rule_level;
    @NotNull
    @JsonAlias({"ruleTmplId"})
    @ApiModelProperty(value = "规则模板ID")
    private String rule_tmpl_id;
    @NotNull
    @JsonAlias({"ruleTargets"})
    @ApiModelProperty(value = "规则数据对象")
    private String rule_targets;
    @JsonAlias({"ruleOptions"})
    @ApiModelProperty(value = "规则其他配置项")
    private String rule_options;
    @JsonAlias({"ruleSqlExpr"})
    @ApiModelProperty(value = "规则自定义SQL表达式")
    private String rule_sql_expr;
    @JsonAlias({"ruleRangeExpr"})
    @ApiModelProperty(value = "规则数据范围表达式")
    private String rule_range_expr;
    @NotNull
    @JsonAlias({"dimCode"})
    @ApiModelProperty(value = "质量维度编码")
    private String dim_code;
    @NotNull
    @JsonAlias({"checkType"})
    @ApiModelProperty(value = "检查(值比较)方式（FIXED：固定值，WAVED：波动值(区分正向、负向评分)）")
    private String check_type;
    @NotNull
    @JsonAlias({"checkOptions"})
    @ApiModelProperty(value = "检查的配置项")
    private String check_options;

    @JsonAlias({"remark"})
    @ApiModelProperty(value = "备注")
    private String remark;
    @JsonAlias({"disabled"})
    @ApiModelProperty(value = "启用|禁用（0：启用，1：禁用）")
    private String disabled;
    @JsonAlias({"createUser"})
    @ApiModelProperty(value = "create_user")
    private String create_user;
    @JsonAlias({"createTime"})
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "create_time")
    private String create_time;
    @JsonAlias({"updateUser"})
    @ApiModelProperty(value = "update_user")
    private String update_user;
    @JsonAlias({"updateTime"})
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "update_time")
    private String update_time;


    public static String getTABLE() {
        return TABLE;
    }

    @JsonProperty("id")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @JsonProperty("datawareId")
    public String getDataware_id() {
        return dataware_id;
    }

    public void setDataware_id(String dataware_id) {
        this.dataware_id = dataware_id;
    }

    @JsonProperty("datatableId")
    public String getDatatable_id() {
        return datatable_id;
    }

    public void setDatatable_id(String datatable_id) {
        this.datatable_id = datatable_id;
    }

    @JsonProperty("datatableName")
    public String getDatatable_name() {
        return datatable_name;
    }

    public void setDatatable_name(String datatable_name) {
        this.datatable_name = datatable_name;
    }

    @JsonProperty("datatableNameAlias")
    public String getDatatable_name_alias() {
        return datatable_name_alias;
    }

    public void setDatatable_name_alias(String datatable_name_alias) {
        this.datatable_name_alias = datatable_name_alias;
    }

    @JsonProperty("ruleName")
    public String getRule_name() {
        return rule_name;
    }

    public void setRule_name(String rule_name) {
        this.rule_name = rule_name;
    }

    @JsonProperty("ruleType")
    public String getRule_type() {
        return rule_type;
    }

    public void setRule_type(String rule_type) {
        this.rule_type = rule_type;
    }

    @JsonProperty("ruleLevel")
    public String getRule_level() {
        return rule_level;
    }

    public void setRule_level(String rule_level) {
        this.rule_level = rule_level;
    }

    @JsonProperty("ruleTmplId")
    public String getRule_tmpl_id() {
        return rule_tmpl_id;
    }

    public void setRule_tmpl_id(String rule_tmpl_id) {
        this.rule_tmpl_id = rule_tmpl_id;
    }

    @JsonProperty("ruleTargets")
    public String getRule_targets() {
        return rule_targets;
    }

    public void setRule_targets(String rule_targets) {
        this.rule_targets = rule_targets;
    }

    @JsonProperty("ruleOptions")
    public String getRule_options() {
        return rule_options;
    }

    public void setRule_options(String rule_options) {
        this.rule_options = rule_options;
    }

    @JsonProperty("ruleSqlExpr")
    public String getRule_sql_expr() {
        return rule_sql_expr;
    }

    public void setRule_sql_expr(String rule_sql_expr) {
        this.rule_sql_expr = rule_sql_expr;
    }

    @JsonProperty("ruleRangeExpr")
    public String getRule_range_expr() {
        return rule_range_expr;
    }

    public void setRule_range_expr(String rule_range_expr) {
        this.rule_range_expr = rule_range_expr;
    }

    @JsonProperty("dimCode")
    public String getDim_code() {
        return dim_code;
    }

    public void setDim_code(String dim_code) {
        this.dim_code = dim_code;
    }

    @JsonProperty("checkType")
    public String getCheck_type() {
        return check_type;
    }

    public void setCheck_type(String check_type) {
        this.check_type = check_type;
    }

    @JsonProperty("checkOptions")
    public String getCheck_options() {
        return check_options;
    }

    public void setCheck_options(String check_options) {
        this.check_options = check_options;
    }

    @JsonProperty("remark")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @JsonProperty("disabled")
    public String getDisabled() {
        return disabled;
    }

    public void setDisabled(String disabled) {
        this.disabled = disabled;
    }

    @JsonProperty("createUse")
    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    @JsonProperty("createTime")
    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    @JsonProperty("updateUser")
    public String getUpdate_user() {
        return update_user;
    }

    public void setUpdate_user(String update_user) {
        this.update_user = update_user;
    }

    @JsonProperty("updateTime")
    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }
}
