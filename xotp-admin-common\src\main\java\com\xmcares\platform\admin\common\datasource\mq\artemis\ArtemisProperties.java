/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/10
 */
package com.xmcares.platform.admin.common.datasource.mq.artemis;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public class ArtemisProperties {

    /**
     * Broker 服务地址，默认：tcp://localhost:61616
     */
    private String brokerUrl = "tcp://localhost:61616";

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    // Getters and Setters
    public String getBrokerUrl() {
        return brokerUrl;
    }

    public void setBrokerUrl(String brokerUrl) {
        this.brokerUrl = brokerUrl;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

}
