package com.xmcares.platform.admin.quality.model.vo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/12 15:27
 */
public class QualityDatatableListQueryVO implements Serializable {
    private String datawareId;
    private String datawareName;
    private String datatableId;
    private String datatableName;
    private String datatableAlias;
    private String ruleCount;
    private String schedulerTactics;
    private String schedulerState;


    public String getDatawareId() {
        return datawareId;
    }

    public void setDatawareId(String datawareId) {
        this.datawareId = datawareId;
    }

    public String getDatatableId() {
        return datatableId;
    }

    public void setDatatableId(String datatableId) {
        this.datatableId = datatableId;
    }

    public String getDatatableName() {
        return datatableName;
    }

    public void setDatatableName(String datatableName) {
        this.datatableName = datatableName;
    }

    public String getDatatableAlias() {
        return datatableAlias;
    }

    public void setDatatableAlias(String datatableAlias) {
        this.datatableAlias = datatableAlias;
    }

    public String getRuleCount() {
        return ruleCount;
    }

    public void setRuleCount(String ruleCount) {
        this.ruleCount = ruleCount;
    }

    public String getSchedulerTactics() {
        return schedulerTactics;
    }

    public void setSchedulerTactics(String schedulerTactics) {
        this.schedulerTactics = schedulerTactics;
    }

    public String getSchedulerState() {
        return schedulerState;
    }

    public void setSchedulerState(String schedulerState) {
        this.schedulerState = schedulerState;
    }

    public String getDatawareName() {
        return datawareName;
    }

    public void setDatawareName(String datawareName) {
        this.datawareName = datawareName;
    }
}
