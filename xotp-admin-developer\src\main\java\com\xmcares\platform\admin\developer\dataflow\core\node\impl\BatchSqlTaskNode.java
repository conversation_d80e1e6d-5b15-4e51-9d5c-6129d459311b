package com.xmcares.platform.admin.developer.dataflow.core.node.impl;


import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.developer.common.enums.DeveloperDispatchType;
import com.xmcares.platform.admin.developer.dataflow.core.node.BaseTaskNode;
import com.xmcares.platform.admin.developer.dataflow.vo.DevDataflowNodeVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.quartz.CronExpression;

import java.util.ArrayList;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/30 10:50
 **/
public class BatchSqlTaskNode extends BaseTaskNode {

    private static final String KEY_DATASOURCE_ID = "datasourceId";
    private static final String KEY_EXECUTE_SQL = "executeSql";

    /**
     * jar 或者 batchsql类型
     */
    private static final String KEY_BATCH_SQL_TYPE = "type";

    @Override
    public void validate() throws BusinessException {
        // 1. 先校验调度类型
        Map<String, Object> handler = Optional.of(nodeInfo().getParams()).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(DevDataflowNodeVo.DevDataflowNodeParam::getKey, DevDataflowNodeVo.DevDataflowNodeParam::getValue));
        if (!handler.containsKey(KEY_SCHEDULER_TYPE) || ObjectUtils.isEmpty(handler.get(KEY_SCHEDULER_TYPE)) || StringUtils.isEmpty((String)handler.get(KEY_SCHEDULER_TYPE))) {
            throw new BusinessException(String.format("节点【%s】未设置调度类型", nodeInfo().getLabel()));
        }
        DeveloperDispatchType thisType = DeveloperDispatchType.match(handler.get(KEY_SCHEDULER_TYPE).toString(), DeveloperDispatchType.NONE);
        switch (thisType) {
            case CRON:
                if (CollectionUtils.isNotEmpty(nodeInfo().getPreIds())) {
                    throw new BusinessException("CRON类型的节点【" + nodeInfo().getLabel() + "】不允许存在前置节点！");
                }
                if (!handler.containsKey(KEY_SCHEDULER_OPTION) || ObjectUtils.isEmpty(handler.get(KEY_SCHEDULER_OPTION)) || StringUtils.isEmpty((String)handler.get(KEY_SCHEDULER_OPTION))) {
                    throw new BusinessException(String.format("节点【%s】未设置调度参数", nodeInfo().getLabel()));
                }
                if (!CronExpression.isValidExpression(handler.get(KEY_SCHEDULER_OPTION).toString())) {
                    throw new BusinessException(String.format("节点【%s】的调度参数配置不正确！", nodeInfo().getLabel()));
                }
                if (!handler.containsKey(KEY_SCHEDULER_RETRY_COUNT) || ObjectUtils.isEmpty(handler.get(KEY_SCHEDULER_RETRY_COUNT)) || !NumberUtils.isNumber(handler.get(KEY_SCHEDULER_RETRY_COUNT).toString())) {
                    throw new BusinessException(String.format("节点【%s】未设置调度重试次数", nodeInfo().getLabel()));
                }
                break;
            case PRE_TASK:
                if (CollectionUtils.isEmpty(nodeInfo().getPreIds())) {
                    throw new BusinessException("等待前一个运行类型的节点【" + nodeInfo().getLabel() + "】必须存在前置节点！");
                }
                break;
            default:
                throw new BusinessException(String.format("节点【%s】调度类型不正确，该节点仅支持【CRON，等待上一个节点】", nodeInfo().getLabel()));
        }
        // 非jar类型的才需要判断数据源和batchSql
        if(!(handler.containsKey(KEY_BATCH_SQL_TYPE) && ObjectUtils.isNotEmpty(handler.get(KEY_BATCH_SQL_TYPE)) && StringUtils.equals(handler.get(KEY_BATCH_SQL_TYPE).toString(),"jar"))){
            // 2. 其他必要参数的校验
            if (!handler.containsKey(KEY_DATASOURCE_ID) || ObjectUtils.isEmpty(handler.get(KEY_DATASOURCE_ID)) || StringUtils.isEmpty((String)handler.get(KEY_DATASOURCE_ID))) {
                throw new BusinessException(String.format("节点【%s】未设置数据源", nodeInfo().getLabel()));
            }
            // 3. 校验SQL
            if (!handler.containsKey(KEY_EXECUTE_SQL) || ObjectUtils.isEmpty(handler.get(KEY_EXECUTE_SQL)) || StringUtils.isEmpty((String)handler.get(KEY_EXECUTE_SQL))) {
                throw new BusinessException(String.format("节点【%s】未设置需要运行的SQL", nodeInfo().getLabel()));
            }
        }


    }

}
