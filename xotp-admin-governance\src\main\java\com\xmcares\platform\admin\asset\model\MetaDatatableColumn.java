package com.xmcares.platform.admin.asset.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/27 15:53
 */
@ApiModel(value = MetaDatatableColumn.TABLE, description = "表字段")
public class MetaDatatableColumn implements Serializable {
    public static final String TABLE = "bdp_meta_datatable_column";

    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "资产编码")
    private String code;

    @ApiModelProperty(value = "列名")
    private String name;

    @ApiModelProperty(value = "列别名（中文名）")
    private String alisa;

    @ApiModelProperty(value = "数据源ID（冗余）")
    private String datasource_id;

    @ApiModelProperty(value = "表ID")
    private String datatable_id;

    @ApiModelProperty(value = "表名称（冗余）")
    private String datatable_name;

    @ApiModelProperty(value = "列类型")
    private String type;

    @ApiModelProperty(value = "列长度")
    private Integer length;

    @ApiModelProperty(value = "列精度(小数点)")
    private Integer accuracy;

    @ApiModelProperty(value = "允许空（0：不允许 1：允许）")
    private String allow_empty;

    @ApiModelProperty(value = "是否键（0：不是 1：是）")
    private String column_key;

    @ApiModelProperty(value = "是否键（0：不是 1：是）")
    private String has_system;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "create_user")
    private String create_user;

    @ApiModelProperty(value = "create_time")
    private Date create_time;

    @ApiModelProperty(value = "update_user")
    private String update_user;

    @ApiModelProperty(value = "update_time")
    private Date update_time;

    public static String getTABLE() {
        return TABLE;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAlisa() {
        return alisa;
    }

    public void setAlisa(String alisa) {
        this.alisa = alisa;
    }

    public String getDatasource_id() {
        return datasource_id;
    }

    public void setDatasource_id(String datasource_id) {
        this.datasource_id = datasource_id;
    }

    public String getDatatable_id() {
        return datatable_id;
    }

    public void setDatatable_id(String datatable_id) {
        this.datatable_id = datatable_id;
    }

    public String getDatatable_name() {
        return datatable_name;
    }

    public void setDatatable_name(String datatable_name) {
        this.datatable_name = datatable_name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getLength() {
        return length;
    }

    public void setLength(Integer length) {
        this.length = length;
    }

    public Integer getAccuracy() {
        return accuracy;
    }

    public void setAccuracy(Integer accuracy) {
        this.accuracy = accuracy;
    }

    public String getAllow_empty() {
        return allow_empty;
    }

    public void setAllow_empty(String allow_empty) {
        this.allow_empty = allow_empty;
    }

    public String getColumn_key() {
        return column_key;
    }

    public void setColumn_key(String column_key) {
        this.column_key = column_key;
    }

    public String getHas_system() {
        return has_system;
    }

    public void setHas_system(String has_system) {
        this.has_system = has_system;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public String getUpdate_user() {
        return update_user;
    }

    public void setUpdate_user(String update_user) {
        this.update_user = update_user;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }
}
