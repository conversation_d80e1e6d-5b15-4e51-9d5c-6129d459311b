package com.xmcares.platform.admin.common.database.metaquery;

import javax.sql.DataSource;

/**
 * OpenGauss元数据查询
 * <AUTHOR>
 * @since 1.0.0
 */
public class OpenGaussMetaQuery extends PostgreMetaQuery {
    public OpenGaussMetaQuery(DataSource dataSource, String schema) {
        super(dataSource, schema);
    }

    public OpenGaussMetaQuery(DataSource dataSource) {
        super(dataSource);
    }
}
