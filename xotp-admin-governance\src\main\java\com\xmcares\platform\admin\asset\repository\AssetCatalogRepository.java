package com.xmcares.platform.admin.asset.repository;

import com.alibaba.fastjson.JSON;
import com.xmcares.framework.commons.error.BusinessException;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.asset.model.*;
import com.xmcares.platform.admin.asset.model.vo.DatatableAllQueryVO;
import com.xmcares.platform.admin.asset.model.vo.DatatableColumnSyncQueryVO;
import com.xmcares.platform.admin.asset.model.vo.DatatableSyncQueryVO;
import com.xmcares.platform.admin.asset.model.vo.TreeQueryVO;
import com.xmcares.platform.admin.common.datasource.jdbc.JdbcDataSourceManager;
import com.xmcares.platform.admin.common.jdbc.JdbcQuery;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;

import static com.xmcares.platform.admin.metadata.database.repository.DatasourceRepository.TABLE_DATASOURCE;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/27 13:36
 */
@Repository
public class AssetCatalogRepository {
    private static final Logger LOG = LoggerFactory.getLogger(AssetCatalogRepository.class);
    public static final String TABLE_BDP_META_DATAWARE = "bdp_meta_dataware";
    public static final String TABLE_BDP_META_CATALOG = "bdp_meta_catalog";
    public static final String TABLE_BDP_META_DATATABLE = "bdp_meta_datatable";
    public static final String TABLE_BDP_META_DATATABLE_COLUMN = "bdp_meta_datatable_column";
    public static final String TABLE_BDP_META_DATASOURCE = "bdp_meta_datasource";

    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    @Autowired
    private JdbcDataSourceManager dataSourceManager;



    public List<MetaDataware> datawareListQuery() {
        try {
            List<MetaDataware> metaDatawares = xcfJdbcTemplate.<MetaDataware>queryForEntities(
                    " select * " +
                            " from " + TABLE_BDP_META_DATAWARE,
                    new Object[]{},
                    MetaDataware.class
            );

            return metaDatawares;
        } catch (Exception e) {
            LOG.warn("datawareListQuery", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }

    @Transactional
    public String datawareAdd(MetaDataware metaDataware) {
        try {
            if (metaDataware.getId() == null) {
                metaDataware.setId(SnowflakeGenerator.getNextId().toString());
            }
            if (metaDataware.getType() == null) {
                metaDataware.setType("TAB");
            }

            MetaDatasource metaDatasource = xcfJdbcTemplate.<MetaDatasource>queryForEntity(
                    " select * " +
                            " from " + MetaDatasource.TABLE +
                            " where " +
                            " id='" + metaDataware.getDatasource_id() + "'"
                    ,
                    new Object[]{},
                    MetaDatasource.class
            );

            metaDataware.setDatasource_options(metaDatasource.getOptions());
            metaDataware.setType(metaDatasource.getType());
            metaDataware.setDeleted("0");
            metaDataware.setCreate_user("xbdp");

            Map<String, Object> maps = DBUtils.insertSqlAndObjects(metaDataware, MetaDataware.class, TABLE_BDP_META_DATAWARE);
            String sql = DBUtils.getSql(maps);
            Object[] objs = DBUtils.getObjects(maps);
            return String.valueOf(xcfJdbcTemplate.update(sql, objs) == 1);
        } catch (Exception e) {
            LOG.warn("datawareAdd", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }

    }


    public String datawareUpdate(MetaDataware metaDataware) {
        try {
            Map<String, Object> maps = DBUtils.updateSqlAndObjects("id", metaDataware, MetaDataware.class, TABLE_BDP_META_DATAWARE);
            String sql = DBUtils.getSql(maps);
            Object[] objs = DBUtils.getObjects(maps);
            return String.valueOf(xcfJdbcTemplate.update(sql, objs) == 1);
        } catch (Exception e) {
            LOG.warn("datawareAdd", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }


    public String datawareDelete(String id) {
        try {
            String sql = DBUtils.deleteSql(TABLE_BDP_META_DATAWARE, "id");
            return String.valueOf(xcfJdbcTemplate.update(sql, id) == 1);
        } catch (Exception e) {
            LOG.warn("datawareDelete", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }

    public List<TreeQueryVO> treeQuery(String name, String datawareId) {
        try {
            if (name == null) {
                name = "_";
            }
            List<TreeQueryVO> treeQreeVOS = new ArrayList<TreeQueryVO>();

            //查询所有的父目录
            List<MetaCatalog> rootCatalogs = xcfJdbcTemplate.<MetaCatalog>queryForEntities(
                    " select *   " +
                            " from " + TABLE_BDP_META_CATALOG +
                            " where " +
                            " name like '%" + name + "%' " +
                            " and dataware_id='" + datawareId + "' " +
                            ("_".equals(name) ? " and isnull(parent_id) " : "")
                    ,
                    new Object[]{},
                    MetaCatalog.class
            );
            //从所有的父目录构建树
            for (int i = 0; i < rootCatalogs.size(); i++) {
                MetaCatalog rootCatalog = rootCatalogs.get(i);
                TreeQueryVO treeQueryVO = new TreeQueryVO();
                treeQueryVO.setDatatables(new ArrayList<>());
                treeQueryVO.setChildrenCatalogs(new ArrayList<>());
                treeQueryVO.setMetaCatalog(rootCatalog);
                treeQreeVOS.add(treeQueryVO);

                Stack<TreeQueryVO> treeQueryVOStack = new Stack<>();
                treeQueryVOStack.push(treeQueryVO);
                while (!treeQueryVOStack.isEmpty()) {
                    TreeQueryVO treeQueryVONowNode = treeQueryVOStack.pop();

                    List<MetaCatalog> childrenCataLogs = xcfJdbcTemplate.<MetaCatalog>queryForEntities(
                            " select *   " +
                                    " from " + TABLE_BDP_META_CATALOG +
                                    " where parent_id ='" + treeQueryVONowNode.getMetaCatalog().getId() + "' ",
                            new Object[]{},
                            MetaCatalog.class
                    );
                    List<MetaDatatable> datatables = xcfJdbcTemplate.<MetaDatatable>queryForEntities(
                            " select *   " +
                                    " from " + TABLE_BDP_META_DATATABLE +
                                    " where catalog_id ='" + treeQueryVONowNode.getMetaCatalog().getId() + "' ",
                            new Object[]{},
                            MetaDatatable.class
                    );
                    treeQueryVONowNode.getDatatables().addAll(datatables);
                    if (childrenCataLogs.size() == 0) {
                        treeQueryVONowNode.setLeaf(true);
                    } else {
                        childrenCataLogs.stream().forEach(v -> {
                            TreeQueryVO treeQueryVONextNode = new TreeQueryVO();
                            treeQueryVONextNode.setMetaCatalog(v);
                            treeQueryVONextNode.setDatatables(new ArrayList<>());
                            treeQueryVONextNode.setChildrenCatalogs(new ArrayList<>());
                            treeQueryVONowNode.getChildrenCatalogs().add(treeQueryVONextNode);
                            treeQueryVOStack.push(treeQueryVONextNode);
                        });
                        treeQueryVONowNode.setLeaf(false);
                    }
                }

            }
            return treeQreeVOS;
        } catch (Exception e) {
            LOG.warn("treeQuery", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }


    public String add(MetaCatalog metaCatalog) {
        try {
            if (metaCatalog.getId() == null) {
                metaCatalog.setId(SnowflakeGenerator.getNextId().toString());
            }
            metaCatalog.setCreate_time(new Date());
            metaCatalog.setUpdate_time(new Date());
            metaCatalog.setCreate_user("XBDP");
            Map<String, Object> maps = DBUtils.insertSqlAndObjects(metaCatalog, MetaCatalog.class, TABLE_BDP_META_CATALOG);
            String sql = DBUtils.getSql(maps);
            Object[] objs = DBUtils.getObjects(maps);
            return String.valueOf(xcfJdbcTemplate.update(sql, objs) == 1);
        } catch (Exception e) {
            LOG.warn("add", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }

    public String update(MetaCatalog metaCatalog) {
        try {
            Map<String, Object> maps = DBUtils.updateSqlAndObjects("id", metaCatalog, MetaCatalog.class, TABLE_BDP_META_CATALOG);
            String sql = DBUtils.getSql(maps);
            Object[] objs = DBUtils.getObjects(maps);
            return String.valueOf(xcfJdbcTemplate.update(sql, objs) == 1);
        } catch (Exception e) {
            LOG.warn("update", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }


    public String delete(String id) {
        try {
            String sql = DBUtils.deleteSql(TABLE_BDP_META_CATALOG, "id");
            return String.valueOf(xcfJdbcTemplate.update(sql, id) == 1);
        } catch (Exception e) {
            LOG.warn("delete", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }

    public String datatableMove(String datatableId, String categoryId) {
        try {

            return String.valueOf(
                    xcfJdbcTemplate.update(
                            "UPDATE " + TABLE_BDP_META_DATATABLE +
                                    " SET catalog_id ='" + categoryId + "'" +
                                    " WHERE id = '" + datatableId + "'") == 1
            );
        } catch (Exception e) {
            LOG.warn("datatableMove ", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }

    public String datatableUpdate(MetaDatatable metaDatatable) {
        try {
            Map<String, Object> maps = DBUtils.updateSqlAndObjects("id", metaDatatable, MetaDatatable.class, TABLE_BDP_META_DATATABLE);
            String sql = DBUtils.getSql(maps);
            Object[] objs = DBUtils.getObjects(maps);
            return String.valueOf(xcfJdbcTemplate.update(sql, objs) == 1);
        } catch (Exception e) {
            LOG.warn("datatableUpdate", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }

    public String datatableColumnUpdate(MetaDatatableColumn metaDatatableColumn) {
        try {
            Map<String, Object> maps = DBUtils.updateSqlAndObjects("id", metaDatatableColumn, MetaDatatableColumn.class, TABLE_BDP_META_DATATABLE_COLUMN);
            String sql = DBUtils.getSql(maps);
            Object[] objs = DBUtils.getObjects(maps);
            return String.valueOf(xcfJdbcTemplate.update(sql, objs) == 1);
        } catch (Exception e) {
            LOG.warn("datatableColumnUpdate", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }


    public DatatableSyncQueryVO datatableSyncQuery(String datawareId) {
        try {

            MetaDataware metaDataware = xcfJdbcTemplate.<MetaDataware>queryForEntity(
                    " select * " +
                            " from " + TABLE_BDP_META_DATAWARE +
                            " where id ='" + datawareId + "'"
                    ,
                    new Object[]{},
                    MetaDataware.class
            );

            List<MetaDatatable> syncMetaDatatables = xcfJdbcTemplate.<MetaDatatable>queryForEntities(
                    " select * " +
                            " from " + TABLE_BDP_META_DATATABLE +
                            " where dataware_id='" + datawareId + "'",
                    new Object[]{},
                    MetaDatatable.class
            );

            MetaDatasource metaDatasource = xcfJdbcTemplate.<MetaDatasource>queryForEntity(
                    " select * " +
                            " from " + TABLE_BDP_META_DATASOURCE +
                            " where id='" + metaDataware.getDatasource_id() + "'",
                    new Object[]{},
                    MetaDatasource.class
            );


            List<Map<String, Object>> jdbcDatas = null;
            HashMap datasourceOptions = JSON.parseObject(metaDatasource.getOptions(), HashMap.class);
            Datasource datasource = getDatasourceById(metaDataware.getDatasource_id());

            DataSource dataSource = dataSourceManager.getOrCreateDataSource(datasource.toDataSourceOptions());
            Connection connection = dataSource.getConnection();
            String dbName = JdbcQuery.getDBName(metaDatasource.getType(), connection);
            switch (metaDatasource.getType().toLowerCase()) {
                case "oracle":
                    jdbcDatas = JdbcQuery.getJdbcData(connection,
                            "SELECT table_name as name, table_comment as remark FROM information_schema.tables WHERE table_schema = '" + dbName + "'"
                    );
                    break;
                case "hive1.1":
                case "hive":
                    jdbcDatas = JdbcQuery.getJdbcData(connection,
                            "SHOW TABLES"
                    );
                    List<Map<String, Object>> result = new ArrayList<>();
                    List<Map<String, Object>> tempResult = null;
                    if (jdbcDatas != null) {
                        for (Map<String, Object> dbLine : jdbcDatas) {
                            Object tabName = dbLine.get("tab_name");
                            if (tabName != null) {
                                HashMap<String, Object> resultMap = new HashMap<>();
                                resultMap.put("name", tabName);
                                result.add(resultMap);
                                tempResult = JdbcQuery.getJdbcData(connection,
                                        "DESCRIBE FORMATTED " + tabName
                                );
                                if (tempResult != null) {
                                    for (Map<String, Object> tableLine : tempResult) {
                                        Object dataType = tableLine.get("data_type");
                                        if (dataType != null) {
                                            if ("comment             ".equals(String.valueOf(dataType))) {
                                                Object commentContent = tableLine.get("comment");
                                                if (commentContent != null) {
                                                    resultMap.put("remark", new String(String.valueOf(commentContent).getBytes(), "UTF-8").trim());
                                                }
                                            }
                                        }
                                    }

                                }
                            }
                        }
                    }
                    jdbcDatas = result;
                    break;
                case "mysql":
                    jdbcDatas = JdbcQuery.getJdbcData(connection,
                            "SELECT TABLE_NAME as name, TABLE_COMMENT as remark FROM information_schema.tables WHERE table_schema = '" + dbName + "'"
                    );
                    break;
                default:
                    throw new Exception("un support datasource type.");
            }
            ArrayList<DatatableSyncQueryVO.UnSyncDatatable> unSyncDatatables = new ArrayList<>();
            if (jdbcDatas != null) {

                Set<String> synTableName = syncMetaDatatables.stream().map(MetaDatatable::getName).collect(Collectors.toSet());


                for (Map<String, Object> jdbcData : jdbcDatas) {
                    if (synTableName.contains(jdbcData.get("name"))) {
                        continue;
                    }
                    unSyncDatatables.add(new DatatableSyncQueryVO.UnSyncDatatable() {{
                        setName(String.valueOf(jdbcData.get("name")));
                        setRemark(String.valueOf(jdbcData.get("remark")));
                    }});
                }
            }

            DatatableSyncQueryVO datatableSyncQueryVO = new DatatableSyncQueryVO();
            datatableSyncQueryVO.setSyncList(syncMetaDatatables);
            datatableSyncQueryVO.setUnSyncList(unSyncDatatables);
            return datatableSyncQueryVO;
        } catch (Exception e) {
            LOG.warn("datatableSyncQuery", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }


    public DatatableAllQueryVO datatableAllQuery(String datatableId) {
        try {
            MetaDatatable metaDatatable = xcfJdbcTemplate.<MetaDatatable>queryForEntity(
                    " select * " +
                            " from " + TABLE_BDP_META_DATATABLE +
                            " where id='" + datatableId + "'",
                    new Object[]{},
                    MetaDatatable.class
            );

            List<MetaDatatableColumn> metaDatatableColumns = xcfJdbcTemplate.<MetaDatatableColumn>queryForEntities(
                    " select " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".id id, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".code code, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".name name, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".alisa alisa, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".datasource_id datasource_id, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".datatable_id datatable_id, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".type type, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".length length, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".accuracy accuracy, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".allow_empty allow_empty, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".column_key column_key, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".has_system has_system, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".remark remark, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".create_user create_user, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".create_time create_time, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".update_user update_user, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".update_time update_time, " +
                            " " + TABLE_BDP_META_DATATABLE + ".name datatable_name " +
                            " from " + TABLE_BDP_META_DATATABLE_COLUMN +
                            " join " + TABLE_BDP_META_DATATABLE + " on " + TABLE_BDP_META_DATATABLE + ".id=" + TABLE_BDP_META_DATATABLE_COLUMN + ".datatable_id and " +
                            " " + TABLE_BDP_META_DATATABLE + ".id='" + datatableId + "'",
                    new Object[]{},
                    MetaDatatableColumn.class
            );

            DatatableAllQueryVO datatableAllQueryVO = new DatatableAllQueryVO();
            datatableAllQueryVO.setMetaDatatable(metaDatatable);
            datatableAllQueryVO.setMetaDatatableColumns(metaDatatableColumns);
            return datatableAllQueryVO;

        } catch (Exception e) {
            LOG.warn("datatableAllQuery", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }

    @Transactional
    public String datatableSyncSave(DatatableSyncQueryVO datatableSyncQueryVO) {
        try {
            Date nowDate = new Date();
            if (datatableSyncQueryVO.getSyncList() != null && datatableSyncQueryVO.getSyncList().size() > 0) {

                MetaDataware metaDataware = xcfJdbcTemplate.<MetaDataware>queryForEntity(
                        " select * " +
                                " from " + TABLE_BDP_META_DATAWARE +
                                " where id ='" + datatableSyncQueryVO.getDatawareId() + "'"
                        ,
                        new Object[]{},
                        MetaDataware.class
                );


                for (MetaDatatable metaDatatable : datatableSyncQueryVO.getSyncList()) {
                    MetaDatatable metaDatatableOld = xcfJdbcTemplate.<MetaDatatable>queryForEntity(
                            " select * " +
                                    " from " + TABLE_BDP_META_DATATABLE +
                                    " where name='" + metaDatatable.getName() + "' and " +
                                    " dataware_id='" + datatableSyncQueryVO.getDatawareId() + "' "
                            ,
                            new Object[]{},
                            MetaDatatable.class
                    );

                    if (metaDatatableOld != null) {
                        metaDatatable.setId(metaDatatableOld.getId());
                        metaDatatable.setDatasource_id(metaDataware.getDatasource_id());
                        metaDatatable.setDataware_id(datatableSyncQueryVO.getDatawareId());
                        metaDatatable.setCatalog_id(metaDatatableOld.getCatalog_id() != null ? metaDatatableOld.getCatalog_id() : datatableSyncQueryVO.getCatalogId());
                        metaDatatable.setUpdate_time(nowDate);
                        Map<String, Object> maps = DBUtils.updateSqlAndObjects("id", metaDatatable, MetaDatatable.class, TABLE_BDP_META_DATATABLE);
                        String sql = DBUtils.getSql(maps);
                        Object[] objs = DBUtils.getObjects(maps);
                        xcfJdbcTemplate.update(sql, objs);
                    } else {
                        metaDatatable.setId(SnowflakeGenerator.getNextId().toString());
                        metaDatatable.setDatasource_id(metaDataware.getDatasource_id());
                        metaDatatable.setDataware_id(datatableSyncQueryVO.getDatawareId());
                        metaDatatable.setCatalog_id(datatableSyncQueryVO.getCatalogId());
                        metaDatatable.setCode(SnowflakeGenerator.getNextId().toString());
                        metaDatatable.setUpdate_time(nowDate);
                        metaDatatable.setCreate_time(nowDate);
                        Map<String, Object> maps = DBUtils.insertSqlAndObjects(metaDatatable, MetaDatatable.class, TABLE_BDP_META_DATATABLE);
                        String sql = DBUtils.getSql(maps);
                        Object[] objs = DBUtils.getObjects(maps);
                        xcfJdbcTemplate.update(sql, objs);
                    }
                }
            }
            if (datatableSyncQueryVO.getUnSyncList() != null && datatableSyncQueryVO.getUnSyncList().size() > 0) {
                for (DatatableSyncQueryVO.UnSyncDatatable unSyncDatatable : datatableSyncQueryVO.getUnSyncList()) {
                    MetaDatatable metaDatatableOld = xcfJdbcTemplate.<MetaDatatable>queryForEntity(
                            " select * " +
                                    " from " + TABLE_BDP_META_DATATABLE +
                                    " where name='" + unSyncDatatable.getName() + "' and " +
                                    " dataware_id='" + datatableSyncQueryVO.getDatawareId() + "' "
                            ,
                            new Object[]{},
                            MetaDatatable.class
                    );
                    if (metaDatatableOld != null) {
                        String deleteTableSql = DBUtils.deleteSql(TABLE_BDP_META_DATATABLE, "id");
                        xcfJdbcTemplate.update(deleteTableSql, metaDatatableOld.getId());
                        xcfJdbcTemplate.update(
                                " delete from " + TABLE_BDP_META_DATATABLE_COLUMN +
                                        " where datasource_id='" + metaDatatableOld.getDatasource_id() + "' and " +
                                        "  datatable_id='" + metaDatatableOld.getId() + "'"
                        );
                    }
                }
            }
            return "true";

        } catch (Exception e) {
            LOG.warn("datatableSyncSave", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }


    public List<Map<String, Object>> datatableRecordQuery(String datatableId, Integer pageNo, Integer pageSize) {
        try {
            MetaDatatable metaDatatable = xcfJdbcTemplate.<MetaDatatable>queryForEntity(
                    " select * " +
                            " from " + TABLE_BDP_META_DATATABLE +
                            " where id='" + datatableId + "'",
                    new Object[]{},
                    MetaDatatable.class
            );

            if (metaDatatable == null) {
                throw new Exception("can't search table");
            }

            MetaDatasource metaDatasource = xcfJdbcTemplate.<MetaDatasource>queryForEntity(
                    " select * " +
                            " from " + TABLE_BDP_META_DATASOURCE +
                            " where id='" + metaDatatable.getDatasource_id() + "'",
                    new Object[]{},
                    MetaDatasource.class
            );


            List<Map<String, Object>> jdbcDatas = null;
            HashMap datasourceOptions = JSON.parseObject(metaDatasource.getOptions(), HashMap.class);
            Datasource datasource = getDatasourceById(metaDatasource.getId());

            DataSource dataSource = dataSourceManager.getOrCreateDataSource(datasource.toDataSourceOptions());
            Connection connection = dataSource.getConnection();
            jdbcDatas = JdbcQuery.getJdbcData(connection,
                    " select * " +
                            " from " + metaDatatable.getName() +
                            " limit  " + (pageNo * pageSize) + "," + pageSize
            );

            if (jdbcDatas == null) {
                throw new Exception("can't search datasource table meta info.");
            }
            return jdbcDatas;
        } catch (Exception e) {
            LOG.warn("datatableRecordQuery", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }


    public DatatableColumnSyncQueryVO datatableColumnSyncQuery(String datatableId) {
        try {
            MetaDatatable metaDatatable = xcfJdbcTemplate.<MetaDatatable>queryForEntity(
                    " select * " +
                            " from " + TABLE_BDP_META_DATATABLE +
                            " where id='" + datatableId + "'",
                    new Object[]{},
                    MetaDatatable.class
            );

            if (metaDatatable == null) {
                throw new Exception("can't search table");
            }

            MetaDatasource metaDatasource = xcfJdbcTemplate.<MetaDatasource>queryForEntity(
                    " select * " +
                            " from " + TABLE_BDP_META_DATASOURCE +
                            " where id='" + metaDatatable.getDatasource_id() + "'",
                    new Object[]{},
                    MetaDatasource.class
            );

            List<MetaDatatableColumn> syncMetaDatatableColumns = xcfJdbcTemplate.<MetaDatatableColumn>queryForEntities(
                    " select " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".id id, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".code code, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".name name, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".alisa alisa, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".datasource_id datasource_id, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".datatable_id datatable_id, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".type type, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".length length, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".accuracy accuracy, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".allow_empty allow_empty, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".column_key column_key, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".has_system has_system, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".remark remark, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".create_user create_user, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".create_time create_time, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".update_user update_user, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".update_time update_time, " +
                            " " + TABLE_BDP_META_DATATABLE + ".name datatable_name " +
                            " from " + TABLE_BDP_META_DATATABLE_COLUMN +
                            " join " + TABLE_BDP_META_DATATABLE + " on " + TABLE_BDP_META_DATATABLE + ".id=" + TABLE_BDP_META_DATATABLE_COLUMN + ".datatable_id and " +
                            " " + TABLE_BDP_META_DATATABLE + ".id='" + datatableId + "'",
                    new Object[]{},
                    MetaDatatableColumn.class
            );


            List<Map<String, Object>> jdbcDatas = null;
            HashMap datasourceOptions = JSON.parseObject(metaDatasource.getOptions(), HashMap.class);
            //TODO  ?? datasource 和 MetaDataSource 是不是同一个东西
            Datasource datasource = getDatasourceById(metaDatasource.getId());

            DataSource dataSource = dataSourceManager.getOrCreateDataSource(datasource.toDataSourceOptions());
            Connection connection =  dataSource.getConnection();

            switch (metaDatasource.getType().toLowerCase()) {
                case "oracle":
                    jdbcDatas = JdbcQuery.getJdbcData(connection,
                            "SELECT COLUMN_NAME name, COMMENTS remark, COLUMN_TYPE type, CHARACTER_MAXIMUM_LENGTH length FROM USER_COL_COMMENTS  WHERE TABLE_NAME = '" + metaDatatable.getName() + "'"
                    );
                    break;
                case "hive":
                case "hive1.1":
                    jdbcDatas = JdbcQuery.getJdbcData(connection,
                            "DESCRIBE FORMATTED " + metaDatatable.getName()
                    );
                    List<Map<String, Object>> result = new ArrayList<>();
                    int i = 0;
                    for (Map<String, Object> jdbcData : jdbcDatas) {
                        if (
                                (jdbcData.get("col_name") != null) &&
                                        !jdbcData.get("col_name").equals("# col_name            ") &&
                                        !"".equals(jdbcData.get("col_name"))
                        ) {
                            Map<String, Object> resultLine = new HashMap<>();
                            resultLine.put("name", jdbcData.get("col_name"));
                            resultLine.put("type", jdbcData.get("data_type"));
                            if (jdbcData.get("comment") != null) {
                                resultLine.put("remark", new String(String.valueOf(jdbcData.get("comment")).getBytes(), "UTF-8").trim());
                            }
                            resultLine.put("length", "-1");
                            result.add(resultLine);
                        } else {
                            i++;
                        }
                        if (i > 2) {
                            break;
                        }
                    }
                    jdbcDatas = result;
                    break;
                case "mysql":
                    jdbcDatas = JdbcQuery.getJdbcData(connection,
                            "SELECT COLUMN_NAME name, COLUMN_COMMENT remark, COLUMN_TYPE type, CHARACTER_MAXIMUM_LENGTH length FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '" + String.valueOf(datasourceOptions.get("schema")) + "'" + " AND TABLE_NAME = '" + metaDatatable.getName() + "'"
                    );
                    break;
                default:
                    throw new Exception("un support datasource type.");
            }


            Set<String> synColumnName = syncMetaDatatableColumns.stream().map(MetaDatatableColumn::getName).collect(Collectors.toSet());

            ArrayList<DatatableColumnSyncQueryVO.UnSyncDatatableColumn> unSyncDatatableColumns = new ArrayList<>();

            for (Map<String, Object> jdbcData : jdbcDatas) {

                if (synColumnName.contains(jdbcData.get("name"))) {
                    continue;

                }
                unSyncDatatableColumns.add(new DatatableColumnSyncQueryVO.UnSyncDatatableColumn() {{
                    setName(String.valueOf(jdbcData.get("name")));
                    setRemark(String.valueOf(jdbcData.get("remark")));
                    setType(String.valueOf(jdbcData.get("type")));
                    setLength(String.valueOf(jdbcData.get("length")));
                }});

            }

            DatatableColumnSyncQueryVO datatableColumnSyncQueryVO = new DatatableColumnSyncQueryVO();
            datatableColumnSyncQueryVO.setSyncList(syncMetaDatatableColumns);
            datatableColumnSyncQueryVO.setUnSyncList(unSyncDatatableColumns);
            return datatableColumnSyncQueryVO;
        } catch (Exception e) {
            LOG.warn("datatableColumnSyncQuery", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }

    @Transactional
    public String datatableColumnSyncSave(DatatableColumnSyncQueryVO datatableColumnSyncQueryVO) {
        try {
            Date nowDate = new Date();
            MetaDatatable metaDatatable = xcfJdbcTemplate.<MetaDatatable>queryForEntity(
                    " select * " +
                            " from " + MetaDatatable.getTABLE() +
                            " where  " +
                            " id='" + datatableColumnSyncQueryVO.getDatatableId() + "' "
                    ,
                    new Object[]{},
                    MetaDatatable.class
            );
            if (datatableColumnSyncQueryVO.getSyncList() != null) {
                for (MetaDatatableColumn metaDatatableColumn : datatableColumnSyncQueryVO.getSyncList()) {
                    MetaDatatableColumn metaDatatableColumnOld = xcfJdbcTemplate.<MetaDatatableColumn>queryForEntity(
                            " select * " +
                                    " from " + TABLE_BDP_META_DATATABLE_COLUMN +
                                    " where name='" + metaDatatableColumn.getName() + "' and " +
                                    " datatable_id='" + datatableColumnSyncQueryVO.getDatatableId() + "' and " +
                                    " datasource_id='" + datatableColumnSyncQueryVO.getDatasourceId() + "'"
                            ,
                            new Object[]{},
                            MetaDatatableColumn.class
                    );
                    if (metaDatatableColumnOld != null) {
                        metaDatatableColumn.setId(metaDatatableColumnOld.getId());
                        metaDatatableColumn.setDatatable_name(metaDatatable.getName());
                        metaDatatableColumn.setDatasource_id(datatableColumnSyncQueryVO.getDatasourceId());
                        metaDatatableColumn.setDatatable_id(datatableColumnSyncQueryVO.getDatatableId());
                        metaDatatableColumn.setUpdate_time(nowDate);
                        Map<String, Object> maps = DBUtils.updateSqlAndObjects("id", metaDatatableColumn, MetaDatatableColumn.class, TABLE_BDP_META_DATATABLE_COLUMN);
                        String sql = DBUtils.getSql(maps);
                        Object[] objs = DBUtils.getObjects(maps);
                        xcfJdbcTemplate.update(sql, objs);

                    } else {
                        metaDatatableColumn.setId(SnowflakeGenerator.getNextId().toString());
                        metaDatatableColumn.setCode(SnowflakeGenerator.getNextId().toString());
                        metaDatatableColumn.setDatatable_name(metaDatatable.getName());
                        metaDatatableColumn.setDatasource_id(datatableColumnSyncQueryVO.getDatasourceId());
                        metaDatatableColumn.setDatatable_id(datatableColumnSyncQueryVO.getDatatableId());
                        metaDatatableColumn.setCreate_time(nowDate);
                        metaDatatableColumn.setUpdate_time(nowDate);
                        Map<String, Object> maps = DBUtils.insertSqlAndObjects(metaDatatableColumn, MetaDatatableColumn.class, TABLE_BDP_META_DATATABLE_COLUMN);
                        String sql = DBUtils.getSql(maps);
                        Object[] objs = DBUtils.getObjects(maps);
                        xcfJdbcTemplate.update(sql, objs);

                    }
                }
            }

            if (datatableColumnSyncQueryVO.getUnSyncList() != null) {
                for (DatatableColumnSyncQueryVO.UnSyncDatatableColumn unSyncDatatableColumn : datatableColumnSyncQueryVO.getUnSyncList()) {
                    MetaDatatableColumn metaDatatableColumnOld = xcfJdbcTemplate.<MetaDatatableColumn>queryForEntity(
                            " select * " +
                                    " from " + TABLE_BDP_META_DATATABLE_COLUMN +
                                    " where name='" + unSyncDatatableColumn.getName() + "' and " +
                                    " datatable_id='" + datatableColumnSyncQueryVO.getDatatableId() + "' and " +
                                    " datasource_id='" + datatableColumnSyncQueryVO.getDatasourceId() + "'"
                            ,
                            new Object[]{},
                            MetaDatatableColumn.class
                    );
                    if (metaDatatableColumnOld != null) {
                        String deleteColumnSql = DBUtils.deleteSql(TABLE_BDP_META_DATATABLE_COLUMN, "id");
                        xcfJdbcTemplate.update(deleteColumnSql, metaDatatableColumnOld.getId());
                    }
                }
            }


            return "true";
        } catch (Exception e) {
            LOG.warn("datatableColumnSyncSave", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }

    public Datasource getDatasourceById(String id) {
        HashMap<String, Object> conditions = new HashMap<>();
        conditions.put("id", String.valueOf(id));
        Map<String, Object> map = DBUtils.queryList(TABLE_DATASOURCE, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntity(sql, args, Datasource.class);
    }

    public List<MetaDatatableColumn> datatableColumnSyncedQuery(String datasourceId, String datatableId) {
        try {
            List<MetaDatatableColumn> metaDatatableColumns = xcfJdbcTemplate.<MetaDatatableColumn>queryForEntities(
                    " select * " +
                            " from " + TABLE_BDP_META_DATATABLE_COLUMN +
                            " where " +
                            " datatable_id='" + datatableId + "' and " +
                            " datasource_id='" + datasourceId + "'"
                    ,
                    new Object[]{},
                    MetaDatatableColumn.class
            );
            return metaDatatableColumns;
        } catch (Exception e) {
            LOG.warn("datatableColumnSyncedQuery", e);
            throw new BusinessException("{'error':'" + e + "'}");
        }
    }
}
