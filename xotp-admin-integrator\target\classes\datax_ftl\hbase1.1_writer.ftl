{
          "name": "hbase11xwriter",
          "parameter": {
            "hbaseConfig": {
                "hbase.zookeeper.quorum": "${destDatasource.zkAddress}"
                <#if dest.configs?? && (dest.size > 0)>,
                    <#list dest.configs as config>
                        "${config.key}":"${config.value}"<#if config_has_next>,</#if>
                    </#list>
                </#if>
            },
            "table": "${dest.table}",
            "mode": "normal",
            "nullMode": "${dest.nullMode}",
            "walFlag": ${dest.walFlag?c},
            "writeBufferSize": "${dest.writeBufferSize}",
            <#if dest.versionColumnIndex??>
            "versionColumn": {
                "index": "${dest.versionColumnIndex}"
                <#if dest.versionColumnValue??>"value": "${dest.versionColumnValue}"</#if>
            },
            </#if>
            "rowkeyColumn": [
                <#assign rokKeySign = "0">
                <#list destColumns as column>
                    <#if column.cType == 'rowkey'>
                        <#if rokKeySign != "0">
                            ,{
                                "index": ${column.index},
                                "type": "${column.type}"
                            }
                        <#else>
                            {
                                "index": ${column.index},
                                "type": "${column.type}"
                            }
                            <#assign rokKeySign = "1">
                        </#if>
                    </#if>
                </#list>
                <#if dest.rowkeySplit??>
                    ,{
                        "index": -1,
                        "type": "string",
                        "value": "${dest.rowkeySplit}"
                    }
                </#if>
            ],
            "column": [
                <#assign sign = "0">
                <#list destColumns as column>
                    <#if column.cType == 'simple'>
                        <#if sign != "0">
                            ,{
                               "index": ${column.index},
                               "name": "${column.name}",
                               "type": "${column.type}"
                            }
                        <#else>
                            {
                                "index": ${column.index},
                                "name": "${column.name}",
                                "type": "${column.type}"
                            }
                            <#assign sign = "1">
                        </#if>
                    </#if>
                </#list>
            ],
            "encoding": "${dest.encoding}"
          }
        }
