package com.xmcares.platform.admin.developer.dataflow.core.node;


import com.xmcares.platform.admin.common.errors.BusinessException;

/**
 * <AUTHOR>
 * @date 2022/6/29 16:15
 **/
public interface ITaskNode<R> {

    /**
     * 校验节点数据的完整性
     * @throws BusinessException 当校验失败时，将抛出该异常
     */
    void validate() throws BusinessException;

    /**
     * 节点信息
     * @return
     */
    R nodeInfo();

}
