package com.xmcares.platform.admin.developer.dataflow.vo;

import com.alibaba.fastjson.JSON;
import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.developer.common.config.DeveloperConfiguration;
import com.xmcares.platform.admin.developer.common.enums.DeveloperTaskType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@ApiModel(value = "AddProcessTaskVo", description = "流程调度任务信息")
public class AddProcessTaskVo implements Serializable {

    /** ID */
    @ApiModelProperty(value = "ID")
    private String id;

    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /** 任务所在应用的名称 */
    @ApiModelProperty(value = "应用的名称")
    private String appName;

    /** 任务名称 */
    @ApiModelProperty(value = "任务名称")
    private String name;

    /** 分组ID */
    @ApiModelProperty(value = "分组ID")
    private String groupId;

    /** 流程ID */
    @ApiModelProperty(value = "流程ID")
    private String processId;

    /** 任务类型 */
    @ApiModelProperty(value = "任务类型")
    private String type;

    /** 调度类型 */
    @ApiModelProperty(value = "调度类型")
    private String schedulerType;

    /** 调度配置 */
    @ApiModelProperty(value = "调度配置")
    private String schedulerOptions;

    /** 执行失败重试次数 */
    @ApiModelProperty(value = "执行失败重试次数")
    private int executorFailRetryCount;

    /** 任务执行时的参数信息 */
    @ApiModelProperty(value = "任务执行时的参数信息")
    private String executeParams;

    /** 前置任务ID列表 */
    @ApiModelProperty(value = "前置任务ID列表")
    private String preTaskIds;

    /** 后置任务ID列表 */
    @ApiModelProperty(value = "后置任务ID列表")
    private String postTaskIds;

    /** 配置文件路径 */
    @ApiModelProperty(value = "配置文件路径")
    private String path;

    /**
     * 创建任务信息
     * @param processId 流程ID
     * @param nodes 任务节点信息
     * @param path 任务文件路径
     * @return
     */
    public static List<AddProcessTaskVo> createFrom(String processId, List<DevDataflowNodeVo> nodes, String path) {
        String createUser = UserContextHolder.getUserContext().getUsername();
        List<AddProcessTaskVo> result = new ArrayList<>();
        for (DevDataflowNodeVo node : nodes) {
            // 1. 全局配置节点 不当做任务节点提交
            if (node.getType().equals(DeveloperTaskType.SYS_GLOBAL_PARAM.name())) { continue; }
            // 2. 其他的均需要提交
            boolean isHeader = CollectionUtils.isEmpty(node.getPreIds()) && CollectionUtils.isNotEmpty(node.getPostIds());
            Map<String, DevDataflowNodeVo.DevDataflowNodeParam> params = Optional.ofNullable(node.getParams()).orElse(new ArrayList<>())
                    .stream().collect(Collectors.toMap(DevDataflowNodeVo.DevDataflowNodeParam::getKey, t->t));
            AddProcessTaskVo task = new AddProcessTaskVo();
            task.setId(node.getId());
            task.setCreateUser(createUser);
            task.setAppName(DeveloperConfiguration.getInstance().getProperties().getScheduler().getGroup());
            task.setName(node.getLabel());
            task.setGroupId(node.getGroupId());
            task.setProcessId(processId);
            task.setType(node.getType());
            task.setSchedulerType(params.containsKey("schedulerType") ?params.get("schedulerType").getValue().toString() : "");
            task.setSchedulerOptions(params.containsKey("schedulerOptions") ? params.get("schedulerOptions").getValue().toString() : "");
            String schedulerFailRetryCount = params.containsKey("schedulerFailRetryCount") ? params.get("schedulerFailRetryCount").getValue().toString() : "";
            task.setExecutorFailRetryCount(NumberUtils.isNumber(schedulerFailRetryCount) ? Integer.parseInt(schedulerFailRetryCount) : 0);
            task.setExecuteParams(!isHeader ? null: createPartialParams(params.values()));
            task.setPreTaskIds(StringUtils.join(node.getPreIds(), ","));
            task.setPostTaskIds(StringUtils.join(node.getPostIds(), ","));
            task.setPath(path);
            result.add(task);
        }
        return result;
    }

    /**
     * 创建局部参数
     * @param values 原始参数集合
     * @return
     */
    private static String createPartialParams(Collection<DevDataflowNodeVo.DevDataflowNodeParam> values) {
        List<DevDataflowNodeVo.DevDataflowNodeParam> filterResult = Optional.ofNullable(values).orElse(
                new ArrayList<>()).stream().filter(param->param.getKey().contains("partial.")).collect(Collectors.toList());
        if (filterResult.isEmpty()) { return null; }
        return JSON.toJSONString(filterResult);
    }

    /**
     * 创建组信息
     * @param nodes
     * @return
     */
    private static Map<String, String> createGroup(List<DevDataflowNodeVo> nodes) {
        Map<String, String> result = new HashMap<>();
        Map<String, List<DevDataflowNodeVo>> groupNodeResult = nodes.stream().collect(Collectors.groupingBy(node -> {
            // 单一的无关联节点
            if (CollectionUtils.isEmpty(node.getPreIds()) && CollectionUtils.isEmpty(node.getPostIds())) {
                return "simple";
            }
            // 存在关联关系的父节点
            if (CollectionUtils.isEmpty(node.getPreIds()) && CollectionUtils.isNotEmpty(node.getPostIds())) {
                return "header";
            }
            // 身体节点 （包含End）
            return "body";
        }));
        // 处理单一无关联节点的组
        result.putAll(Optional.ofNullable(groupNodeResult.get("simple")).orElse(
                new ArrayList<>()).stream().map(node-> new AbstractMap.SimpleEntry<>(node.getId(), SnowflakeGenerator.getNextId() + ""))
                        .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue)));
        // 处理头节点的组
        result.putAll(Optional.ofNullable(groupNodeResult.get("header")).orElse(
                new ArrayList<>()).stream().map(node-> new AbstractMap.SimpleEntry<>(node.getId(), SnowflakeGenerator.getNextId() + ""))
                .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue)
        ));
        // 处理身体节点
        List<DevDataflowNodeVo> headerNodes = Optional.ofNullable(groupNodeResult.get("header")).orElse(
                new ArrayList<>());
        Map<String, DevDataflowNodeVo> bodyNodes = Optional.ofNullable(groupNodeResult.get("body")).orElse(
                new ArrayList<>()).stream().collect(Collectors.toMap(DevDataflowNodeVo::getId, t->t));
        for (DevDataflowNodeVo headerNode : headerNodes) {
            createBodyGroup(result, bodyNodes, result.get(headerNode.getId()), headerNode.getPostIds());
        }
        return result;
    }

    /**
     * 创建节点为身体的组信息
     * @param result
     * @param nodes
     * @param groupId
     * @param postIds
     */
    private static void createBodyGroup(Map<String, String> result, Map<String, DevDataflowNodeVo> nodes, String groupId, List<String> postIds) {
        if (CollectionUtils.isEmpty(postIds)){return;}
        for (String postId : postIds) {
            if (result.containsKey(postId)) {
                continue;
            }
            result.put(postId, groupId);
            DevDataflowNodeVo findResult = nodes.get(postId);
            createBodyGroup(result, nodes, groupId, findResult.getPostIds());
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSchedulerType() {
        return schedulerType;
    }

    public void setSchedulerType(String schedulerType) {
        this.schedulerType = schedulerType;
    }

    public String getSchedulerOptions() {
        return schedulerOptions;
    }

    public void setSchedulerOptions(String schedulerOptions) {
        this.schedulerOptions = schedulerOptions;
    }

    public int getExecutorFailRetryCount() {
        return executorFailRetryCount;
    }

    public void setExecutorFailRetryCount(int executorFailRetryCount) {
        this.executorFailRetryCount = executorFailRetryCount;
    }

    public String getExecuteParams() {
        return executeParams;
    }

    public void setExecuteParams(String executeParams) {
        this.executeParams = executeParams;
    }

    public String getPreTaskIds() {
        return preTaskIds;
    }

    public void setPreTaskIds(String preTaskIds) {
        this.preTaskIds = preTaskIds;
    }

    public String getPostTaskIds() {
        return postTaskIds;
    }

    public void setPostTaskIds(String postTaskIds) {
        this.postTaskIds = postTaskIds;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}
