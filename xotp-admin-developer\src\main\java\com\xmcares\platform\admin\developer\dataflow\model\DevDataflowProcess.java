package com.xmcares.platform.admin.developer.dataflow.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.common.validation.Update;
import com.xmcares.platform.admin.developer.common.enums.ProcessRunStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel(value = DevDataflowProcess.TABLE, description = "数据开发工作流实例信息")
public class DevDataflowProcess implements Serializable {

    public static final String TABLE = "bdp_dev_dataflow_process";

    /** ID */
    @NotNull(message = "ID不允许为空", groups = Update.class)
    @ApiModelProperty(value = "ID")
    private String id;

    /** 创建时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 维护人ID */
    @ApiModelProperty(value = "维护人ID")
    private String createUser;

    /** 工作流ID */
    @ApiModelProperty(value = "工作流ID")
    private String dataflowId;

    /** 实例名称 */
    @ApiModelProperty(value = "实例名称")
    private String name;

    /** 文件路径 */
    @ApiModelProperty(value = "文件路径")
    private String path;

    /** 运行状态 */
    @ApiModelProperty(value = "运行状态", notes = "0: 未运行 1: 运行中 2: 已暂停")
    private String status;

    /** 界面配置信息 */
    @ApiModelProperty(value = "界面配置信息")
    private String dataflowGraphOptions;

    /** 备注信息 */
    @ApiModelProperty(value = "备注信息")
    private String remark;

    public static DevDataflowProcess createBy(DevDataflow dataflow, DevDataflowProcess process) {
        DevDataflowProcess result = new DevDataflowProcess();
        result.setId(SnowflakeGenerator.getNextId() + "");
        result.setCreateTime(new Date());
        result.setUpdateTime(new Date());
        result.setCreateUser(UserContextHolder.getUserContext().getUsername());
        result.setDataflowId(dataflow.getId());
        result.setName(process.getName());
        result.setPath(null);
        result.setStatus(ProcessRunStatus.UN.getCode());
        result.setDataflowGraphOptions(dataflow.getGraphOptions());
        result.setRemark(process.getRemark());
        return result;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getDataflowId() {
        return dataflowId;
    }

    public void setDataflowId(String dataflowId) {
        this.dataflowId = dataflowId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDataflowGraphOptions() {
        return dataflowGraphOptions;
    }

    public void setDataflowGraphOptions(String dataflowGraphOptions) {
        this.dataflowGraphOptions = dataflowGraphOptions;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
