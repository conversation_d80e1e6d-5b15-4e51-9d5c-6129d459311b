package com.xmcares.platform.admin.metadata.common.resource;


import com.xmcares.platform.admin.common.datasource.DataSourceOptions;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

public interface IResourceManager<P> {

    interface IDoChange {

        void doRemove(String type);

        void doAdd(String type);

        void doChange(String type);
    }

    /**
     * 资源管理初始化
     * @param holder
     * @param param
     */
    void init(IResourceHolder holder, P param);

    /**
     * 添加资源
     * @param type 资源类型
     * @param stream 资源输入流
     * @return 返回路径
     */
    String addResource(String type, InputStream stream);

    /**
     * 移除资源
     * @param type 资源类型
     */
    void removeResource(String type);

    /**
     * 下载资源
     * @param type 资源类型
     * @param stream
     */
    void downloadResource(String type, OutputStream stream);

    /**
     * 获取资源类型列表
     * @return
     */
    List<String> types();

    /**
     * 根据资源类型获取资源路径
     * @param type
     * @return
     */
    String findPathByType(String type);

    /**
     * 获取改变了值的数据源
     * @param oldDataSourceOptions
     * @param doChange
     */
    void doChanger(DataSourceOptions oldDataSourceOptions, IDoChange doChange);

}
