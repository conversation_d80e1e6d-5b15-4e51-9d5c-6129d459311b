package com.xmcares.platform.admin.quality.web;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.platform.admin.quality.model.vo.QualityRuleMetricsListQueryVO;
import com.xmcares.platform.admin.quality.service.QualityRuleMetricsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/25 09:10
 */
@Api(value = "质量规则执行记录服务")
@Validated
@RestController
@RequestMapping("/quality/rule-metrics")
public class QualityRuleMetricsController {

    @Autowired
    QualityRuleMetricsService qualityRuleMetricsService;

    @ApiOperation("查询规则执行结果记录")
    @PostMapping("/list-query")
    @ResponseBody
    public Page<QualityRuleMetricsListQueryVO> listQuery(
            String datawareId,
            String datatableName,
            String ruleName,
            String[] ruleSchedulerTaskIds,
            @RequestParam(name = "pageNo") Integer pageNo,
            @RequestParam(name = "pageSize") Integer pageSize
    ) {
        return qualityRuleMetricsService.listQuery(datawareId, datatableName, ruleName, ruleSchedulerTaskIds, pageNo, pageSize);
    }

}
