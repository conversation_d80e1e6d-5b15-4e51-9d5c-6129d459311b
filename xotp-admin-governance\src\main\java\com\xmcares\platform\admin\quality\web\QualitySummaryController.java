package com.xmcares.platform.admin.quality.web;

import com.xmcares.platform.admin.config.MetricsTags;
import com.xmcares.platform.admin.config.RuleLevel;
import com.xmcares.platform.admin.quality.model.vo.QualitySummaryDatatableScoreTopsQueryVO;
import com.xmcares.platform.admin.quality.model.vo.QualitySummaryDatawareScoreQueryVO;
import com.xmcares.platform.admin.quality.model.vo.QualitySummaryRuleSchedulerCountVO;
import com.xmcares.platform.admin.quality.model.vo.QualitySummaryRuleStateCountVO;
import com.xmcares.platform.admin.quality.service.QualitySummaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/25 09:10
 */
@Api(value = "质量总览服务")
@Validated
@RestController
@RequestMapping("/quality/summary")
public class QualitySummaryController {

    @Autowired
    QualitySummaryService qualitySummaryService;

    @ApiOperation("从库的角度汇总当日维度分以及所有库的总分")
    @PostMapping("/dataware-score/query")
    @ResponseBody
    public QualitySummaryDatawareScoreQueryVO datawareScoreQuery(
            String[] datawareIds,
            String beginDate,
            String endDate
    ) {
        return qualitySummaryService.datawareScoreQuery(datawareIds, new String[]{MetricsTags.DATAWARE_DIM_SCORE, MetricsTags.DATAWARE_ALL_SCORE}, RuleLevel.DATAWARE_LEVEL, beginDate, endDate);
    }

    @ApiOperation("从库的维度计算表质量分top-n")
    @PostMapping("/datatable-score/tops-query")
    @ResponseBody
    public ArrayList<QualitySummaryDatatableScoreTopsQueryVO> datatableScoreTopsQuery(
            String[] datawareIds,
            Integer topN
    ) {
        if (topN == null) {
            topN = 10;
        }
        return qualitySummaryService.datatableScoreTopsQuery(datawareIds, MetricsTags.DATATABLE_ALL_SCORE, RuleLevel.TABLE_LEVEL, topN, "DESC");
    }

    @ApiOperation("从库的角度计算规则的运行情况")
    @PostMapping("/rule-state/count")
    @ResponseBody
    public QualitySummaryRuleStateCountVO ruleStateCount(
            String[] datawareIds,
            String beginDate,
            String endDate
    ) {
        //TODO 需要日志结果获取完成可做
        return qualitySummaryService.ruleStateCount(datawareIds, beginDate, endDate);
    }

    @ApiOperation("计算库表的调度次数")
    @PostMapping("/rule-schedule/count")
    @ResponseBody
    public QualitySummaryRuleSchedulerCountVO ruleScheduleCount(
            String[] datawareIds,
            @RequestParam(name = "beginDate") String beginDate,
            @RequestParam(name = "endDate") String endDate
    ) {
        return qualitySummaryService.ruleScheduleCount(datawareIds, beginDate.concat(" 00:00:00"), endDate.concat(" 00:00:00"));
    }
}
