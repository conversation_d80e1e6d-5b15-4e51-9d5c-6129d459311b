<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xmcares.platform</groupId>
        <artifactId>xotp-admin</artifactId>
        <version>1.0.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>xotp-admin-dataservice</artifactId>

    <properties>

    </properties>


    <dependencies>

        <!-- :::: XOTP其他模块依赖 :::: 开始！-->
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>xotp-admin-common</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>xotp-admin-metadata</artifactId>
        </dependency>
        <!-- :::: XOTP其他模块依赖 :::: 结束！-->

        <!-- :::: XCNF & Spring Boot & Spring Cloud :::: 开始！-->
        <dependency>
            <groupId>com.xmcares.framework</groupId>
            <artifactId>spring-boot-starter-xcnf-sharing-service</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- :::: XCNF & Spring Boot & Spring Cloud :::: 结束！-->
    </dependencies>
</project>