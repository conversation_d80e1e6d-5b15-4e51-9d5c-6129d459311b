/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/1/26
 */
package com.xmcares.platform.admin.metadata.database.web;

import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.platform.admin.common.datasource.DataSourceGroup;
import com.xmcares.platform.admin.common.validation.Insert;
import com.xmcares.platform.admin.common.validation.Update;
import com.xmcares.platform.admin.common.datasource.DataSourceTypeValue;
import com.xmcares.platform.admin.metadata.database.model.DatasourceModel;
import com.xmcares.platform.admin.metadata.database.service.DatasourceModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 元模型之元数据源模型（描述元数据的数据称为元元数据，简称元模型）控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/datasource-model")
public class DatasourceModelController {

    @Autowired
    DatasourceModelService dsModelService;

    @GetMapping("/supportTypes")
    @ResponseBody
    public List<DataSourceTypeValue> modelTypes() {
        List<DataSourceTypeValue> allTypes = DataSourceGroup.typesOfAllGroups();
        List<String> supports = dsModelService.findSupportTypes();
        if (allTypes != null && supports != null && !supports.isEmpty()) {
            allTypes.removeIf(type -> supports.contains(type.getType()));
        }
        return allTypes;
    }


    @GetMapping("/allTypes")
    @ResponseBody
    public List<DataSourceTypeValue> allTypes() {
        return DataSourceGroup.typesOfAllGroups();
    }

    @GetMapping("/list-query")
    @ResponseBody
    public List<DatasourceModel> listDatasourceModel(DatasourceModel query) {
        return dsModelService.listDataModel(query);
    }

    @GetMapping("/category/list-query")
    @ResponseBody
    public Map<String, List<DatasourceModel>> categoryListQuery(DatasourceModel query) {
        return dsModelService.categoryList(query);
    }

    @PostMapping("/add")
    @ResponseBody
    public Boolean add(@RequestBody @Validated({Insert.class}) DatasourceModel datasourceModel) {
        datasourceModel.setCreateUser(UserContextHolder.getUserContext().getUsername());
        return dsModelService.add(datasourceModel);
    }

    @PostMapping("/update")
    @ResponseBody
    public Boolean update(@RequestBody @Validated({Update.class}) DatasourceModel datasourceModel) {
        return dsModelService.update(datasourceModel);
    }

    @GetMapping("/delete")
    @ResponseBody
    public Boolean delete(@NotBlank(message = "id不能为空") String id) {
        int delete = dsModelService.delete(id);
        if (delete >= 1) {
            return true;
        } else {
            return false;
        }

    }
}
