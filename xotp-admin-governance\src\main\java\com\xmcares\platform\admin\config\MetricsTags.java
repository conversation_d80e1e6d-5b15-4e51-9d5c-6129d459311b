package com.xmcares.platform.admin.config;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/27 11:11
 */
public interface MetricsTags {

    //数据资产指标标签
    public static final String DATABASE_SIZE_BYTES = "database_size_bytes";
    public static final String DATABASE_SIZE_BYTES_TOTAL = "database_size_bytes_total";
    public static final String TABLES_SIZE_BYTES = "tables_size_bytes";
    public static final String DATABASE_TABLES_TOTAL = "database_tables_total";
    public static final String TABLE_SIZE_BYTES = "table_size_bytes";
    public static final String TABLE_RECORDS_TOTAL = "table_records_total";
    public static final String DATAAPI_REQUESTS_TOTAL = "dataapi_requests_total";
    public static final String STATUS_TOTAL = "status_total";
    //public static final String DATAWARE_DIM_SCORE = "dataware_dim_socre";


    //数据质量规则标签

    public static final String DATAWARE_DIM_SCORE = "0";
    //public static final String DATAWARE_ALL_SCORE = "dataware_all_socre";
    public static final String DATAWARE_ALL_SCORE = "1";
    //public static final String DATATABLE_ALL_SCORE = "datatable_all_socre";
    public static final String DATATABLE_ALL_SCORE = "2";

    public static final String DATATABLE_DIM_SCORE = "3";
}
