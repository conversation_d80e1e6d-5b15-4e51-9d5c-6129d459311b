package com.xmcares.platform.admin.metadata.database.vo;

import com.xmcares.platform.admin.metadata.database.model.DatasourceColumn;
import com.xmcares.platform.admin.metadata.database.model.DatasourceTable;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/17 09:37
 */
public class DatasourceTableDetailVo {
    public DatasourceTable datasourceTable;
    public List<DatasourceColumn> datasourceColumns;


    public DatasourceTableDetailVo(DatasourceTable datasourceTable, List<DatasourceColumn> datasourceColumns) {
        this.datasourceTable = datasourceTable;
        this.datasourceColumns = datasourceColumns;
    }

    public DatasourceTable getDatasourceTable() {
        return datasourceTable;
    }

    public void setDatasourceTable(DatasourceTable datasourceTable) {
        this.datasourceTable = datasourceTable;
    }

    public List<DatasourceColumn> getDatasourceColumns() {
        return datasourceColumns;
    }

    public void setDatasourceColumns(List<DatasourceColumn> datasourceColumns) {
        this.datasourceColumns = datasourceColumns;
    }
}
