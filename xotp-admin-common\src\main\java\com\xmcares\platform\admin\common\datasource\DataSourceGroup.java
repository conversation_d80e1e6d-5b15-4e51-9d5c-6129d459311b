package com.xmcares.platform.admin.common.datasource;

import com.xmcares.platform.admin.common.datasource.jdbc.JdbcType;
import com.xmcares.platform.admin.common.datasource.mq.MqType;
import com.xmcares.platform.admin.common.datasource.nosql.NoSqlType;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 改进：使用枚举类实现，增加对数据源组下各数据源类型的管理（eliming）
 * <AUTHOR>
 * <AUTHOR> chenYG
 * @date : 2022/4/21 9:28
 */
@SuppressWarnings({"unchecked", "rawtypes"})
public enum DataSourceGroup {
    JDBC {
        @Override
        public JdbcType itsTypeOf(String typeName) {
            return JdbcType.fromTypeName(typeName);
        }

        @Override
        public JdbcType[] itsTypes() {
            return JdbcType.values();
        }

    } ,
    NOSQL {
        @Override
        public NoSqlType itsTypeOf(String typeName) {
            return NoSqlType.fromTypeName(typeName);
        }

        @Override
        public NoSqlType[] itsTypes() {
            return NoSqlType.values();
        }
    },
    MQ {
        @Override
        public MqType itsTypeOf(String typeName) {
            return MqType.fromTypeName(typeName);
        }

        @Override
        public MqType[] itsTypes() {
            return MqType.values();
        }
    }
    ;

    public abstract <T extends DataSourceType> T itsTypeOf(String typeName);

    public abstract <T extends DataSourceType> T[] itsTypes();

    public static DataSourceGroup groupOf(String groupName) {
        for (DataSourceGroup group : DataSourceGroup.values()) {
            if (group.equalsIgnoreCase(groupName)) {
                return group;
            }
        }
        return null;
    }

    public boolean equalsIgnoreCase(String groupName) {
        return this.name().equals(groupName);
    }


    public List<DataSourceTypeValue> typesOfGroup() {
        List<DataSourceTypeValue> resultList = new ArrayList<>();
        for (DataSourceType type : itsTypes()) {
            DataSourceTypeValue result = new DataSourceTypeValue(type.getGroup().name(), type.getTypeName());
            resultList.add(result);
        }
        return resultList;
    }

    public static List<String> groups() {
        return  Arrays.stream(DataSourceGroup.values())
                .map(Enum::name).collect(Collectors.toList());
    }


    public static List<DataSourceTypeValue> typesOfAllGroups() {
        List<DataSourceTypeValue> result = new ArrayList<>();
        for (DataSourceGroup group : DataSourceGroup.values()) {
            result.addAll(group.typesOfGroup());
        }
        return result;
    }
}
