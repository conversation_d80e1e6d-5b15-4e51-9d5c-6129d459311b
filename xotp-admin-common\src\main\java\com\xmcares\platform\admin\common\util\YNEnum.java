package com.xmcares.platform.admin.common.util;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/17 17:01
 */
public enum YNEnum {

    YES("1", 1,"Y"),
    NO("0", 0, "N");

    private String intCharCode;
    private int intCode;
    private String charCode;

    YNEnum(String intCharCode, int intCode, String charCode) {
        this.intCharCode = intCharCode;
        this.intCode = intCode;
        this.charCode = charCode;
    }

    public String getIntCharCode() {
        return intCharCode;
    }

    public int getIntCode() {
        return intCode;
    }

    public String getCharCode() {
        return charCode;
    }

    public static YNEnum such(String code) {
        if (StringUtils.isEmpty(code)) { throw new IllegalArgumentException("code 不能为空"); }
        String suchCode = code.toUpperCase();
        for (YNEnum value : YNEnum.values()) {
            if (value.name().equals(suchCode)) {
                return value;
            }
            if (value.charCode.equals(suchCode)) {
                return value;
            }
            if (value.intCharCode.equals(suchCode)) {
                return value;
            }
        }
        throw new IllegalArgumentException("未找到匹配的代码");
    }
}
