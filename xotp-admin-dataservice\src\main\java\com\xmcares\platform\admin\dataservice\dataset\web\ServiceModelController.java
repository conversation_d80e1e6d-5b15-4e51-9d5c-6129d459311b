package com.xmcares.platform.admin.dataservice.dataset.web;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.platform.admin.dataservice.dataset.vo.*;
import com.xmcares.platform.admin.dataservice.dataset.service.ServiceModelService;
import com.xmcares.platform.admin.metadata.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/26 16:18
 */
@Api(value = "服务模型控制器")
@Validated
@RestController
@RequestMapping(value = "${xbdp.api.dataservice:/dataservice}/dataset", produces = "application/json")
public class ServiceModelController {

    @Autowired
    ServiceModelService serviceModelService;

    @ApiOperation("新建数据模型")
    @PostMapping("/save")
    @ResponseBody
    public Result<CreateOrCopyDataModelReturnVO> createDataModel(
            String datasetId,
            @RequestParam(name = "datasourceId") String datasourceId,
            @RequestParam(name = "datasetName") String datasetName,
            @RequestParam(name = "datasetCode") String datasetCode,
            String datasetSql,
            String datasetParameter,
            @RequestParam(name = "datasetMode", defaultValue = "0") String datasetMode
    ) {
        return serviceModelService.createDataModel(datasetId, datasourceId, datasetName, datasetCode, datasetSql, datasetParameter, datasetMode);
    }

    @ApiOperation("数据模型分页展示")
    @GetMapping("/page-query")
    @ResponseBody
    public Result<Page<DataModelPageQueryVO>> dataModelPageQuery(
            @RequestParam(name = "pageSize", defaultValue = "0") Integer pageSize,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            String datasetName,
            String datasourceId,
            String datasetPublished
    ) {

        return serviceModelService.dataModelPageQuery(pageSize, pageNo, datasetName, datasourceId, datasetPublished);
    }

    @ApiOperation("数据模型批量删除")
    @GetMapping("/delete")
    @ResponseBody
    public Result<List<DeleteDatasetVO>> deleteDatasetModels(
            @RequestParam(name = "datasetIds") List<String> datasetIds
    ) {

        return serviceModelService.deleteDatasetModels(datasetIds);
    }

    @ApiOperation("数据模型复制")
    @PostMapping("/copy")
    @ResponseBody
    public Result<CreateOrCopyDataModelReturnVO> copyDataset(
            @RequestParam(name = "datasetId") String datasetId,
            @RequestParam(name = "datasetCode") String datasetCode,
            @RequestParam(name = "datasetName") String datasetName
    ) {
        return serviceModelService.copy(datasetId, datasetCode, datasetName);
    }


    @ApiOperation("通过模型号获取模型对象")
    @GetMapping("/id-get")
    @ResponseBody
    public Result<MetaDatasetVO> getDatasetById(
            @RequestParam(name = "datasetId") String datasetId
    ) {

        return serviceModelService.getDatasetById(datasetId);
    }

    @ApiOperation("通过模型Code获取模型对象")
    @GetMapping("/datasetCode-get")
    @ResponseBody
    public Result<MetaDatasetVO> getDatasetByDatasetCode(
            @RequestParam(name = "datasetCode") String datasetCode
    ) {

        return serviceModelService.getDatasetByDatasetCode(datasetCode);
    }

    @ApiOperation("模型服务名唯一性效验")
    @GetMapping("/unique-check")
    @ResponseBody
    public Result<Boolean> nameUniqueCheck(
            @RequestParam(name = "datasetCode") String datasetCode
    ) {

        return serviceModelService.nameUniqueCheck(datasetCode);
    }

    @ApiOperation("模型服务名写入系统服务权限表")
    @GetMapping("/is-publish")
    @ResponseBody
    public Result<PublishDatasetModelVO> isPublishDatasetModel(
            @RequestParam(name = "datasetId") String datasetId,
            @RequestParam(name = "isPublish", defaultValue = "0") String isPublish,
            String serviceCategory,
            String serviceName
    ) {

        return serviceModelService.isPublishDatasetModel(datasetId, isPublish, serviceCategory, serviceName);
    }

    @ApiOperation("拉取数据源表及视图")
    @GetMapping("/datasource-view-get")
    @ResponseBody
    public Result<List<DatatableVO>> getDatatableById(
            @RequestParam(name = "datasourceId") String datasourceId
    ) {

        return serviceModelService.getDatatableById(datasourceId);
    }

    @ApiOperation("通过选中的表/视图获取字段")
    @GetMapping("/datasource-view-field")
    @ResponseBody
    public Result<List<DatatableFieldVO>> getDatatableFieldById(
            @RequestParam(name = "datasourceId") String datasourceId,
            @RequestParam(name = "datatableId") String datatableId
    ) {

        return serviceModelService.getDatatableFieldById(datasourceId, datatableId);
    }

    @ApiOperation("通过选中的表/视图显示数据")
    @GetMapping("/datasource-view-data")
    @ResponseBody
    public Result<List<Map<String, Object>>> showTableData(
            @RequestParam(name = "datasourceId") String datasourceId,
            @RequestParam(name = "datatableId") String datatableId
    ) {
        return serviceModelService.showTableData(datasourceId, datatableId);
    }

    @ApiOperation("获取权限表的url权限数据")
    @GetMapping("/service-get")
    @ResponseBody
    public Result<com.xmcares.framework.sharing.domain.model.Service> getServiceById(
            @RequestParam(name = "serviceId") String serviceId
    ) {
        return serviceModelService.getServiceById(serviceId);
    }


    @ApiOperation("服务api")
    @PostMapping("/datasource-sql-execute")
    @ResponseBody
    public Result<List<Map<String, Object>>> executeSql(
            @RequestParam(name = "datasourceId") String datasourceId,
            @RequestParam(name = "sql") String sql,
            @RequestParam(required = false, name = "sqlParams") String sqlParams
    ) {
        return serviceModelService.executeSql(datasourceId, sql.replace("\r", "").replace("\n", "").replace("\r\n", ""), sqlParams);
    }
}
