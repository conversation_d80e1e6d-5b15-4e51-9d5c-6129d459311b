package com.xmcares.platform.admin.metadata.database.repository;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.platform.admin.metadata.database.model.DatasourceResource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/9/22 10:08
 */
@Repository
public class DatasourceResourceRepository {
    private static final Logger LOG = LoggerFactory.getLogger(DatasourceResourceRepository.class);
    public static final String TABLE_BDP_META_DATASOURCE_RESOURCE = "bdp_meta_datasource_resource";

    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    public List<DatasourceResource> queryPage(DatasourceResource datasource, Page<DatasourceResource> page) {
        Map<String, Object> conditions = buildCondition(datasource);
        Map<String, Object> map = DBUtils.queryList(TABLE_BDP_META_DATASOURCE_RESOURCE, conditions);
        String sql = DBUtils.getSql(map) + " ORDER BY id ASC ";
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntities(sql, args, page, DatasourceResource.class);
    }

    public int count(DatasourceResource datasource) {
        Map<String, Object> conditions = buildCondition(datasource);
        Map<String, Object> map = DBUtils.queryCount(TABLE_BDP_META_DATASOURCE_RESOURCE, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForObject(sql, Integer.class, args);
    }


    public Integer uniqueResourceName(String name) {
        return xcfJdbcTemplate.<Integer>queryForObject(
                "SELECT COUNT(*) FROM " + TABLE_BDP_META_DATASOURCE_RESOURCE + " where `name` = ?",
                Integer.class,
                name
        );
    }

    public Integer createResource(DatasourceResource datasourceResource) {
        return xcfJdbcTemplate.update(
                "INSERT INTO " + TABLE_BDP_META_DATASOURCE_RESOURCE + "(id, `name`, update_time, path, upload_user, remark, use_count) VALUES (?,?,?,?,?,?,?)",
                datasourceResource.getId(),
                datasourceResource.getName(),
                datasourceResource.getUpdateTime(),
                datasourceResource.getPath(),
                datasourceResource.getUploadUser(),
                datasourceResource.getRemark(),
                datasourceResource.getUseCount()
        );
    }

    public Integer deleteResource(String resourceId) {
        return xcfJdbcTemplate.update("DELETE FROM " + TABLE_BDP_META_DATASOURCE_RESOURCE + " WHERE id = ?", resourceId);
    }

    public Integer updateResource(DatasourceResource datasourceResource) {
        try {
            return xcfJdbcTemplate.update(
                    "UPDATE " + TABLE_BDP_META_DATASOURCE_RESOURCE + " SET `name` = ?, remark = ? WHERE id = ? ",
                    datasourceResource.getName(), datasourceResource.getRemark(), datasourceResource.getId()
            );
        } catch (Exception e) {
            LOG.warn(e.toString());
            return 0;
        }
    }

    public DatasourceResource getResourceById(String resourceId) {
        return xcfJdbcTemplate.queryForEntity(
                "SELECT * FROM " + TABLE_BDP_META_DATASOURCE_RESOURCE + " where id = ?",
                new Object[]{resourceId},
                DatasourceResource.class
        );
    }

    public DatasourceResource getResourceByName(String name) {
        return xcfJdbcTemplate.queryForEntity(
                "SELECT * FROM " + TABLE_BDP_META_DATASOURCE_RESOURCE + " where name = ?",
                new Object[]{name},
                DatasourceResource.class
        );
    }

    private Map<String, Object> buildCondition(DatasourceResource datasourceResource) {
        Map<String, Object> conditions = new HashMap<>();
        if (datasourceResource != null) {
            if (datasourceResource.getName() != null && !datasourceResource.getName().equals("")) {
                conditions.put("name", "%" + datasourceResource.getName() + "%");
            }
        }
        return conditions;
    }

}
