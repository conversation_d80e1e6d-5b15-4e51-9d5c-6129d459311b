package com.xmcares.platform.admin.developer.dataflow.web;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.developer.dataflow.model.DevDataflow;
import com.xmcares.platform.admin.developer.dataflow.model.DevDataflowProcess;
import com.xmcares.platform.admin.developer.dataflow.model.DevDataflowProcessResource;
import com.xmcares.platform.admin.developer.dataflow.model.DevDataflowResource;
import com.xmcares.platform.admin.developer.dataflow.service.DevDataflowDefinitionService;
import com.xmcares.platform.admin.developer.dataflow.service.DevDataflowInstanceService;
import com.xmcares.platform.admin.developer.dataflow.service.DevDataflowResourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Api(value = "数据开发访问控制器")
@Validated
@RestController
@RequestMapping("/developer/dataflow")
public class DevDataflowController {


    private Logger logger= LoggerFactory.getLogger(DevDataflowController.class);
    @Autowired
    private DevDataflowDefinitionService definitionService;
    @Autowired
    private DevDataflowInstanceService instanceService;
    @Autowired
    private DevDataflowResourceService resourceService;

    //=============================   主页列表功能  ============================//
    /** 主页分页列表 */
    @ApiOperation("定义分页列表")
    @GetMapping("/page-query")
    @ResponseBody
    public Page<DevDataflow> page(@RequestParam(name = "definitionName",required = false) String definitionName,
                                  @RequestParam(name = "page",defaultValue = "1") Integer page,
                                  @RequestParam(name = "rows",defaultValue = "10") Integer row) {
        Pagination pagination = new Pagination();
        pagination.setPageNo(page);
        pagination.setPageSize(row);
        DevDataflow devDataflow=new DevDataflow();
        devDataflow.setName(definitionName);
        return definitionService.pageDevDataflow(devDataflow, pagination);
    }

    /** 主页分页列表 点击编辑后 获取详情信息 */
    @ApiOperation("详情（包含图信息）")
    @GetMapping("/detail")
    @ResponseBody
    public DevDataflow detail(@RequestParam("id") String id) {
        return definitionService.detail(id);
    }

    /** 初始化定义， 提供给新增使用 */
    @ApiOperation("初始化定义")
    @PostMapping("/init")
    @ResponseBody
    public DevDataflow init(@RequestBody DevDataflow devDataflow) {
        try {
            return definitionService.init(devDataflow);
        }catch (Exception e){
            logger.error("初始化定义失败",e);
            throw new BusinessException(e.getMessage());
        }
    }

    /** 用户保存定义信息， 提供给实时上传、保存调用 */
    @ApiOperation("保存定义")
    @PostMapping("/save")
    @ResponseBody
    public Boolean save(@RequestBody DevDataflow devDataflow) {
        try {
            boolean flag= definitionService.save(devDataflow);
            return flag;
        }catch (Exception e){
            logger.error("保存定义失败",e);
            throw new BusinessException(e.getMessage());
        }
    }

    /** 主页分页列表 点击上架按钮 */
    /*@ApiOperation("上架")
    @GetMapping("/shelve")
    @ResponseBody
    public Boolean shelve(@RequestParam("id") String id) {
        try {
            boolean flag= definitionService.shelve(id);
            return flag;
        }catch (Exception e){
            logger.error("上架失败",e);
            throw new BusinessException(e.getMessage());
        }
    }*/

    /** 主页分页列表 点击下架按钮 */
    /*@ApiOperation("下架")
    @GetMapping("/unShelve")
    @ResponseBody
    public Boolean unShelve(@RequestParam("id") String id) {
        return definitionService.unShelve(id);
    }*/

    /** 主页分页列表 点击发布按钮 */
    @ApiOperation("发布")
    @PostMapping("/publish")
    @ResponseBody
    public Boolean publish(@RequestBody DevDataflowProcess process) {
        return definitionService.publish(process);
    }

    /** 主页分页列表 点击删除按钮 */
    @ApiOperation("删除定义")
    @GetMapping("/delete")
    @ResponseBody
    public Boolean delete(@RequestParam("id") String id) {
        return definitionService.delete(id);
    }

    //=============================   实例列表功能  ============================//

    /** 实例分页列表， 主页点击某一项后输出对应的实例列表 */
    @ApiOperation("实例分页列表")
    @GetMapping("/instance/page-query")
    @ResponseBody
    public Page<DevDataflowProcess> instancePage(@RequestParam(name = "workflowId") String workflowId,
                                                   @RequestParam(name = "page",defaultValue = "1") Integer page,
                                                   @RequestParam(name = "rows",defaultValue = "10") Integer row) {
        Pagination pagination = new Pagination();
        pagination.setPageNo(page);
        pagination.setPageSize(row);
        DevDataflowProcess devDataflowProcess=new DevDataflowProcess();
        devDataflowProcess.setDataflowId(workflowId);
        return instanceService.pageDevDataflowProcess(devDataflowProcess, pagination);
    }

    /** 主页分页列表 点击编辑后 获取详情信息 */
    @ApiOperation("详情（包含图信息）")
    @GetMapping("/instance/detail")
    @ResponseBody
    public DevDataflowProcess instanceDetail(@RequestParam("id") String id) {
        DevDataflowProcess result = instanceService.detail(id);
        return result;
    }

    /** 实例分页列表 点击开始运行按钮 */
    @ApiOperation("开始运行")
    @GetMapping("/instance/begin")
    @ResponseBody
    public Boolean begin(@RequestParam("id") String id) {
        instanceService.begin(id);
        return true;
    }

    /** 实例分页列表 点击停止运行按钮 */
    @ApiOperation("停止运行")
    @GetMapping("/instance/stop")
    @ResponseBody
    public Boolean stop(@RequestParam("id") String id) {
        instanceService.stop(id);
        return true;
    }

    /** 实例分页列表 点击暂停运行按钮 */
    @ApiOperation("暂停运行")
    @GetMapping("/instance/pause")
    @ResponseBody
    public Boolean pause(@RequestParam("id") String id) {
        instanceService.pause(id);
        return true;
    }

    /** 实例分页列表 点击继续运行按钮 */
    @ApiOperation("继续运行")
    @GetMapping("/instance/go")
    @ResponseBody
    public Boolean go(@RequestParam("id") String id) {
        instanceService.go(id);
        return true;
    }

    /** 实例分页列表 点击删除按钮 */
    @ApiOperation("删除实例")
    @GetMapping("/instance/delete")
    @ResponseBody
    public Boolean instanceDelete(@RequestParam("id") String id) {
        try {
            boolean flag = instanceService.instanceDelete(id);
            return flag;
        } catch (Exception e) {
            this.logger.error("删除实例失败，失败原因", e);
        }
        return false;
    }

    /** 资源分页列表， 主页点击某一项后输出对于的资源列表 */
    @ApiOperation("实例资源分页列表")
    @GetMapping("/instance/resource/page-query")
    @ResponseBody
    public Page<DevDataflowProcessResource> instanceResourcePage(@RequestParam(name = "workflowId") String workflowId,
                                                  @RequestParam(name = "page",defaultValue = "1") Integer page,
                                                  @RequestParam(name = "rows",defaultValue = "10") Integer row) {
        if (StringUtils.isEmpty(workflowId)) {
            throw new BusinessException("实例ID不允许为空！");
        }
        Pagination pagination = new Pagination();
        pagination.setPageNo(page);
        pagination.setPageSize(row);
        DevDataflowProcessResource devDataflowResource=new DevDataflowProcessResource();
        devDataflowResource.setProcessId(workflowId);
        return instanceService.pageDevDataflowProcessResource(devDataflowResource, pagination);
    }

    //=============================   资源列表功能  ============================//

    /** 资源分页列表， 主页点击某一项后输出对于的资源列表 */
    @ApiOperation("资源分页列表")
    @GetMapping("/resource/page-query")
    @ResponseBody
    public Page<DevDataflowResource> resourcePage(@RequestParam(name = "workflowId") String workflowId,
                                                  @RequestParam(name = "name", required = false) String name,
                                                  @RequestParam(name = "page",defaultValue = "1") Integer page,
                                                  @RequestParam(name = "rows",defaultValue = "10") Integer row) {
        Pagination pagination = new Pagination();
        pagination.setPageNo(page);
        pagination.setPageSize(row);
        DevDataflowResource devDataflowResource=new DevDataflowResource();
        devDataflowResource.setName(name);
        devDataflowResource.setDataflowId(workflowId);
        return resourceService.pageDevDataflowResource(devDataflowResource, pagination);
    }

    @ApiOperation("上传资源")
    @ResponseBody
    @PostMapping(value = "/resource/upload", produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean upload(DevDataflowResource resource,
                          @NotNull(message = "file不能为空") @RequestParam("file") MultipartFile file) throws Exception {
        resourceService.upload(resource, file);
        return true;
    }

    /** 实例分页列表 点击删除按钮 */
    @ApiOperation("删除资源")
    @GetMapping("/resource/delete")
    @ResponseBody
    public Boolean resourceDelete(@RequestParam("id") String id) {
        return resourceService.resourceDelete(id);
    }
}
