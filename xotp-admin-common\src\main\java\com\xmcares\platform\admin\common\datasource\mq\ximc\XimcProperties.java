/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/30
 */
package com.xmcares.platform.admin.common.datasource.mq.ximc;

import com.xmcares.imcc.XMPPConfig;

/**
 * XIMC 数据源配置
 * <AUTHOR>
 * @since 1.0.0
 */
public class XimcProperties extends XMPPConfig {

    private String username;

    private String password;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public void setUrl(String url) {
        if (url != null) {
            String[] parts = url.split(":");
            this.setServerHost(parts[0]);
            if (parts.length > 1) {
                this.setServerPort(Integer.parseInt(parts[1]));
            }
        }
    }

}
