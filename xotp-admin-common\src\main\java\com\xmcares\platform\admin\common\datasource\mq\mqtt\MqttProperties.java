/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/23
 */
package com.xmcares.platform.admin.common.datasource.mq.mqtt;

/**
 * MQTT 配置属性
 * <AUTHOR>
 * @since 1.0.0
 */
public class MqttProperties {
    /**
     * MQTT 服务端地址，默认：tcp://127.0.0.1:1883
     */
    private String brokerUrl = "tcp://127.0.0.1:1883";

    /**
     * 客户端 ID，建议唯一
     */
    private String clientId;

    /**
     * 用户名（可选）
     */
    private String username;

    /**
     * 密码（可选）
     */
    private String password;

    /**
     * 默认 QoS（0, 1, 2）
     */
    private int qos = 1;

    // Getter / Setter
    public String getBrokerUrl() {
        return brokerUrl;
    }

    public void setBrokerUrl(String brokerUrl) {
        this.brokerUrl = brokerUrl;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public int getQos() {
        return qos;
    }

    public void setQos(int qos) {
        this.qos = qos;
    }
}
