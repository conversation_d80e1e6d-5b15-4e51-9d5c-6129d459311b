{
                  "name": "mongodbreader",
                  "parameter": {
                      "address": [
<#list orginDatasource.addresses as address>
    ${address}<#if address_has_next>,</#if>
</#list>

                        ],
                      "userName": ${orginDatasource.userName},
                      "userPassword": ${orginDatasource.userPassword},
                      "dbName": ${orginDatasource.dbName},
                      "collectionName": ${orginDatasource.collectionName},
                      "column": [
<#list orginDatasource.columns as column>
    {
        ${column.name!},
        ${column.type!},
        ${column.spliter}
    }
    ${column}<#if column_has_next>,</#if>
</#list>
                      ]
                  }
              }