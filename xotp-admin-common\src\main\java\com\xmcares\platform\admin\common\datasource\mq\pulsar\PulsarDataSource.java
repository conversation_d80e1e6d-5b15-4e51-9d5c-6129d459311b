/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/27
 */
package com.xmcares.platform.admin.common.datasource.mq.pulsar;

import com.xmcares.platform.admin.common.datasource.mq.MessageHeaders;
import com.xmcares.platform.admin.common.datasource.mq.MqDataSource;
import com.xmcares.platform.admin.common.datasource.mq.pulsar.PulsarProperties.AuthType;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.client.api.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Pulsar 数据源实现，支持的Pulsar版本：4.0.x 版本
 * <AUTHOR>
 * @since 1.0.0
 */
public class PulsarDataSource implements MqDataSource {
    private static final Logger logger = LoggerFactory.getLogger(PulsarDataSource.class);

    private final String name;
    private final PulsarProperties props;
    private PulsarClient pulsarClient;

    // 缓存 producer / consumer / listener 线程
    private final Map<String, Producer<byte[]>> producerCache = new ConcurrentHashMap<>();
    private final Map<String, Consumer<byte[]>> consumerCache = new ConcurrentHashMap<>();
    private final Map<String, ExecutorService> listenerExecutors = new ConcurrentHashMap<>();
    private final Map<String, AtomicBoolean> listenerRunning = new ConcurrentHashMap<>();

    public PulsarDataSource(String name, PulsarProperties props) {
        this.name = name;
        this.props = props;
        initialize();
    }

    private void initialize() {
        try {
            ClientBuilder builder = PulsarClient.builder()
                    .serviceUrl(props.getServiceUrl());

            if (AuthType.TOKEN.equalsIgnoreCase(props.getAuthType())) {
                builder.authentication(AuthenticationFactory.token(props.getToken()));
            } else if (AuthType.TLS.equalsIgnoreCase(props.getAuthType())) {
                builder.authentication(AuthenticationFactory.TLS(props.getCertPath(), props.getKeyPath()));
            } else if (AuthType.BASIC.equalsIgnoreCase(props.getAuthType())){
                Map<String, String> params = new HashMap<>();
                params.put("userId", props.getUsername());
                params.put("password", props.getPassword());
                builder.authentication(AuthenticationFactory
                        .create("org.apache.pulsar.client.impl.auth.AuthenticationBasic", params));
            }

            pulsarClient = builder
                    .keepAliveInterval(props.getKeepAliveIntervalSeconds(), TimeUnit.MILLISECONDS)
                    .connectionTimeout(props.getConnectionTimeoutSeconds(), TimeUnit.SECONDS)
                    .operationTimeout(props.getOperationTimeoutSeconds(), TimeUnit.SECONDS)
                    .build();
        } catch (Exception e) {
            logger.error("Pulsar client initialization failed", e);
            throw new RuntimeException("Failed to initialize Pulsar client", e);
        }
    }

    @Override
    public void close() {
        try {
            for (Producer<byte[]> producer : producerCache.values()) {
                producer.close();
            }
            for (Consumer<byte[]> consumer : consumerCache.values()) {
                consumer.close();
            }
            for (ExecutorService executor : listenerExecutors.values()) {
                executor.shutdownNow();
            }
            if (pulsarClient != null) {
                pulsarClient.close();
            }
        } catch (Exception e) {
            logger.warn("Exception while closing Pulsar resources", e);
        }
    }

    @Override
    public AvailableStatus testAvailable() {
        try {
            // 尝试创建并关闭一个 producer
            String testTopic = "test-availability-" + UUID.randomUUID();
            Producer<byte[]> producer = pulsarClient.newProducer()
                    .topic(testTopic)
                    .enableBatching(false)
                    .create();
            producer.close();
            return new AvailableStatus( true, null);
        } catch (Exception e) {
            logger.warn("Pulsar testAvailable failed", e);
            return new AvailableStatus(false, e.getMessage());
        }
    }

    @Override
    public String getName() {
        return this.name;
    }

    public PulsarProperties getProperties() {
        return props;
    }

    @Override
    public void sendMessage(String topic, MessageHeaders headers, byte[] body) {
        try {
            Producer<byte[]> producer = producerCache.computeIfAbsent(topic, t -> {
                try {
                    return pulsarClient.newProducer()
                            .topic(t)
                            .enableBatching(true)
                            .blockIfQueueFull(true)
                            .create();
                } catch (PulsarClientException e) {
                    throw new RuntimeException("Create producer failed for topic: " + t, e);
                }
            });

            TypedMessageBuilder<byte[]> msgBuilder = producer.newMessage();
            if (StringUtils.isNotEmpty(headers.getOrderKey())) {
                msgBuilder.key(headers.getOrderKey());
            }
            msgBuilder.value(body).send();
        } catch (Exception e) {
            logger.error("Send message failed for topic: {}", topic, e);
            throw new RuntimeException("Failed to send message", e);
        }
    }

    @Override
    public void pullMessage(String topic, String group, MessageHeaders headers, Callback callback) {
        String key = topic + "::" + group;
        try {
            Consumer<byte[]> consumer = consumerCache.computeIfAbsent(key, k -> {
                try {
                    return pulsarClient.newConsumer()
                            .topic(topic)
                            .subscriptionName(group)
                            .subscriptionType(SubscriptionType.Shared)
                            .subscribe();
                } catch (PulsarClientException e) {
                    throw new RuntimeException("Create consumer failed for topic: " + topic, e);
                }
            });

            Messages<byte[]> messages = consumer.batchReceive();
            List<byte[]> values = new ArrayList<>(messages.size());
            for (Message<byte[]> msg : messages) {
                values.add(msg.getValue());
            }
            callback.invoke(values);
            consumer.acknowledge(messages);
        } catch (Exception e) {
            logger.error("Pull message failed for topic: {}", topic, e);
            throw new RuntimeException("Failed to receive message", e);
        }
    }

    @Override
    public void addMessageListener(String topic, String group, MqMessageListener listener) {
        String key = topic + "::" + group;
        if (listenerRunning.putIfAbsent(key, new AtomicBoolean(true)) != null) {
            logger.warn("Listener already running for topic: {}, group: {}", topic, group);
            return;
        }

        ExecutorService executor = Executors.newSingleThreadExecutor();
        listenerExecutors.put(key, executor);

        executor.submit(() -> {
            try {
                Consumer<byte[]> consumer = pulsarClient.newConsumer()
                        .topic(topic)
                        .subscriptionName(group)
                        .subscriptionType(SubscriptionType.Shared)
                        .subscribe();

                consumerCache.put(key, consumer);
                while (listenerRunning.get(key).get()) {
                    Message<byte[]> msg = consumer.receive();
                    try {
                        // 1. 获取消息属性并转换为 MessageHeaders
                        Map<String, String> props = msg.getProperties();
                        Map<String, Object> headers = new HashMap<>(props);
                        MessageHeaders header = new MessageHeaders(headers);
                        // 2. 可选：加入 messageId、publishTime 等扩展字段
                        headers.put("messageId", msg.getMessageId().toString());
                        headers.put("publishTime", msg.getPublishTime());
                        listener.onMessage(header, msg.getValue());
                        consumer.acknowledge(msg);
                    } catch (Exception ex) {
                        consumer.negativeAcknowledge(msg);
                        logger.warn("Listener message handling failed, message negatively acknowledged", ex);
                    }
                }
            } catch (Exception e) {
                logger.error("Listener thread crashed for topic: {}", topic, e);
            }
        });
    }

    @Override
    public void removeMessageListener(String topic, String group, MqMessageListener listener) {
        String key = topic + "::" + group;
        AtomicBoolean flag = listenerRunning.get(key);
        if (flag != null) {
            flag.set(false);
        }
        ExecutorService executor = listenerExecutors.remove(key);
        if (executor != null) {
            executor.shutdownNow();
        }

        Consumer<byte[]> consumer = consumerCache.remove(key);
        if (consumer != null) {
            try {
                consumer.close();
            } catch (PulsarClientException e) {
                logger.warn("Failed to close consumer for topic: {}, group: {}", topic, group, e);
            }
        }
    }
}

