<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xmcares.platform</groupId>
        <artifactId>xotp-admin</artifactId>
        <version>1.0.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>xotp-admin-integrator</artifactId>

    <properties>

    </properties>


    <dependencies>
        <!-- :::: XOTP 其他模块依赖 :::: 开始！-->
        <dependency>
            <groupId>com.xmcares.platform</groupId>
            <artifactId>xotp-admin-metadata</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- :::: XOTP 其他模块依赖 :::: 结束！-->


        <!-- :::: XCNF & Spring Boot & Spring Cloud :::: 开始！-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
        </dependency>
        <!-- :::: XCNF & Spring Boot & Spring Cloud :::: 结束！-->

        <!-- :::: 第三方开源库依赖 :::: 开始！ -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.28</version>
        </dependency>

        <!-- :::: 第三方开源库依赖 :::: 结束！ -->


    </dependencies>

</project>
