package com.xmcares.platform.admin.integrator.common.error;

import com.xmcares.framework.commons.error.BaseException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/03/29 09:48:18
 * @version 1.0.0
 */
@ResponseStatus(value = HttpStatus.BAD_REQUEST)
public class IntegratorException extends BaseException {

    public IntegratorException(String message){
        super(message);
    }
}
