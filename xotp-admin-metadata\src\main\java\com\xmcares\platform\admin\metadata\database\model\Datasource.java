package com.xmcares.platform.admin.metadata.database.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xmcares.platform.admin.common.datasource.DataSourceOptions;
import com.xmcares.platform.admin.common.util.CommonConstants;
import com.xmcares.platform.admin.common.validation.Insert;
import com.xmcares.platform.admin.common.validation.Update;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/2/11 09:34
 */
public class Datasource implements Serializable {

    private static final List<String> FILTER = Arrays.asList(
            CommonConstants.DATASOURCE_COMMON_PARAM_USERNAME,
            CommonConstants.DATASOURCE_COMMON_PARAM_PASSWORD,
            CommonConstants.DATASOURCE_COMMON_PARAM_URL,
            CommonConstants.DATASOURCE_COMMON_PARAM_DRIVER,
            CommonConstants.DATASOURCE_COMMON_PARAM_ZKADDRESS,
            CommonConstants.DATASOURCE_COMMON_PARAM_DATABASENAME
    );

    @NotNull(message = "id不允许为空", groups = Update.class)
    private String id;
    @NotEmpty(message = "名称不允许为空", groups = Insert.class)
    @NotEmpty(message = "名称不允许为空", groups = Update.class)
    private String name;
    private String remark;
    private String modelId;
    @NotEmpty(message = "类型不允许为空", groups = Insert.class)
    @NotEmpty(message = "类型不允许为空", groups = Update.class)
    private String type;
    @NotEmpty(message = "种类不允许为空", groups = Insert.class)
    @NotEmpty(message = "种类不允许为空", groups = Update.class)
    private String category;
    @NotEmpty(message = "配置不允许为空", groups = Insert.class)
    @NotEmpty(message = "配置不允许为空", groups = Update.class)
    private String options;

    /** 创建时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 维护人ID */
    private String createUser;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getOptions() {
        return options;
    }

    public void setOptions(String options) {
        this.options = options;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }
    /*public JobDatasource toJobDatasource() {
        JSONObject object = JSON.parseObject(this.getOptions());
        JobDatasource result = new JobDatasource();
        result.setId(Long.parseLong(this.getId()));
        result.setDatasourceName(this.getName());
        result.setDatasource(this.getType());
        result.setDatasourceGroup(this.getCategory());

        result.setJdbcUsername(object.getString(CommonConstants.DATASOURCE_COMMON_PARAM_USERNAME));
        result.setJdbcPassword(object.getString(CommonConstants.DATASOURCE_COMMON_PARAM_PASSWORD));
        result.setJdbcUrl(object.getString(CommonConstants.DATASOURCE_COMMON_PARAM_URL));
        result.setJdbcDriverClass(object.getString(CommonConstants.DATASOURCE_COMMON_PARAM_DRIVER));
        result.setComments(this.getRemark());
        result.setZkAdress(object.getString(CommonConstants.DATASOURCE_COMMON_PARAM_ZKADDRESS));
        result.setDatabaseName(object.getString(CommonConstants.DATASOURCE_COMMON_PARAM_DATABASENAME));
        Map<String, String> additionalParameters = new HashMap<>();
        for (String key : object.keySet()) {
            if (object.get(key) != null && !FILTER.contains(key)) {
                additionalParameters.put(key, object.getString(key));
            }
        }
        result.setAdditionalParameters(additionalParameters);
        return result;
    }*/

    public DataSourceOptions toDataSourceOptions() {
        JSONObject params = JSON.parseObject(this.getOptions());
        DataSourceOptions result = new DataSourceOptions(params);
        result.put(DataSourceOptions.KEY_NAME, this.getName());
        result.put(DataSourceOptions.KEY_GROUP, this.getCategory());
        result.put(DataSourceOptions.KEY_REMARK, this.getRemark());
        result.put(DataSourceOptions.KEY_TYPE, this.getType());
        return result;
    }
}
