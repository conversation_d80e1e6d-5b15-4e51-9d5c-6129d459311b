package com.xmcares.platform.admin.asset.web;

import com.xmcares.platform.admin.asset.model.vo.*;
import com.xmcares.platform.admin.config.MetricsTags;
import com.xmcares.platform.admin.asset.repository.AssetSummaryRepository;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/25 09:10
 */
@Api(value = "资产总览服务")
@Validated
@RestController
@RequestMapping("/asset/summary")
public class AssetSummaryController {

    @Autowired
    AssetSummaryRepository assetSummaryRepository;

    @ApiOperation("查询仓库及其统计信息")
    @GetMapping("/dataware/list-query")
    @ResponseBody
    public List<DatawareListQueryVO> datawareListQuery() {
        //TODO datawareListQuery 参数需要默认指定需要获取的指标...
        return assetSummaryRepository.datawareListQuery(new String[]{}, new String[]{MetricsTags.DATABASE_TABLES_TOTAL, MetricsTags.DATABASE_SIZE_BYTES_TOTAL});
    }

    @ApiOperation("查询仓库大小趋势")
    @PostMapping("/dataware-size/query")
    @ResponseBody
    public List<DatawareSizeQueryVO> datawareSizeQuery(
            @RequestParam(name = "filterDatawares") List<String> datawareIds,
            @RequestParam(name = "beginDate") String beginDate,
            @RequestParam(name = "endDate") String endDate
    ) {
        return assetSummaryRepository.datawareSizeQuery(datawareIds, beginDate, endDate, MetricsTags.DATABASE_SIZE_BYTES, "DESC");
    }

    @ApiOperation("查询状态数量")
    @GetMapping("/status/query")
    @ResponseBody
    public List<AssetSummaryStatusQueryVO> statusQuery() {
        return assetSummaryRepository.statusQuery(MetricsTags.STATUS_TOTAL);
    }

    @ApiOperation("查询表大小排行")
    @GetMapping("/datatable-size/tops-query")
    @ResponseBody
    public List<DatatableSizeTopsQueryVO> datatableSizeTopsQuery(
            String order,
            Integer limit
    ) {
        if (order == null){
            order = "DESC";
        }
        if (limit == null){
            limit = 20;
        }
        return assetSummaryRepository.datatableSizeTopsQuery(new String[]{}, MetricsTags.TABLES_SIZE_BYTES, order, limit);
    }

    @ApiOperation("查询服务热度排行")
    @GetMapping("/service-hot/tops-query")
    @ResponseBody
    public List<ServiceHotTopsQueryVO> serviceHotTopsQuery(String order) {
        if (order == null){
            order = "DESC";
        }
        return assetSummaryRepository.serviceHotTopsQuery(new String[]{}, MetricsTags.DATAAPI_REQUESTS_TOTAL, order);
    }


}
