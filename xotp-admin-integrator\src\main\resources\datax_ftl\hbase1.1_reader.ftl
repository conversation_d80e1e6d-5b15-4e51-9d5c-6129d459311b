{
                    "name": "hbase11xreader",
                    "parameter": {
                        "hbaseConfig": {
                            "hbase.zookeeper.quorum": "${orginDatasource.zkAddress}"
                            <#if orgin.configs?? && (orgin.size > 0)>,
                                <#list orgin.configs as config>
                                    "${config.key}":"${config.value}"<#if config_has_next>,</#if>
                                </#list>
                            </#if>
                        },
                        "table": "${orgin.table}",
                        "encoding": "${orgin.encoding}",
                        "mode": "${orgin.mode}",
                        "column": [
                            <#list orginColumns as column>
                             {
                                "name": "${column.name}",
                                "type": "${column.type}"
                                <#if column.format??>,"format":"${column.format}"</#if>
                             }<#if column_has_next>,</#if>
                            </#list>
                        ],
                        "range": {
                            "isBinaryRowkey": <#if orgin.isBinaryRowkey??>${orgin.isBinaryRowkey?c}<#else>false</#if>
                            <#if orgin.startRowkey??>,"startRowkey": "${orgin.startRowkey}"</#if>
                            <#if orgin.endRowkey??>,"endRowkey": ${orgin.endRowkey}</#if>
                        },
                        "scanCacheSize": "${orgin.scanCacheSize}",
                        "scanBatchSize": "${orgin.scanBatchSize}"
                    }
                }
