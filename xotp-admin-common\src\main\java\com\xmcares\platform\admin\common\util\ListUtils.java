package com.xmcares.platform.admin.common.util;

import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/18 13:49
 */
public class ListUtils {

    public static class Different {
        private final List<String> removes;
        private final List<String> adds;
        public Different() {
            this.removes = new ArrayList<>();
            this.adds = new ArrayList<>();
        }
        public Different(List<String> removes, List<String> adds) {
            this.removes = removes;
            this.adds = adds;
        }
        public List<String> getAdds() {
            return adds;
        }
        public List<String> getRemoves() {
            return removes;
        }

        @Override
        public String toString() {
            return "Different{" +
                    "removes=" + removes +
                    ", adds=" + adds +
                    '}';
        }
    }


    /**
     * a.g :
     *  collA: String[] collA = new String[] { "A", "C", "D"};
     *  collB: String[] collB = new String[] { "A", "B", "E", "F"};
     *  result: Different{removes=[C, D], adds=[B, E, F]}
     * 找两个集合的不同之处
     * @param collA 已有的集合
     * @param collB 对照的集合
     * @return 两个结合的不同之处
     */
    public static Different findDifferent(List<String> collA, List<String> collB) {
        if (CollectionUtils.isEmpty(collA) && CollectionUtils.isEmpty(collB)) {
            return new Different();
        }
        // 1. 全是新增
        if (CollectionUtils.isEmpty(collA)) {
            return new Different(new ArrayList<>(), collB);
        }
        // 2. 全是删除
        if (CollectionUtils.isEmpty(collB)) {
            return new Different(collA, new ArrayList<>());
        }
        // 3. 即有新增又有删除
        List<String> removes = new ArrayList<>(collA);
        List<String> adds = new ArrayList<>(collB);

        removes.removeAll(adds);  // 移除后 removes里面剩下的就是需要移除的了
        adds.removeAll(collA); // 移除后 adds里面就是需要新增的了

        return new Different(removes, adds);

    }


}
