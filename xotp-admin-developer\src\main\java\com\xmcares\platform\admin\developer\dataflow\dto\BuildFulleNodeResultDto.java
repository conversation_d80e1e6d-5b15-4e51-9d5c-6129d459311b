package com.xmcares.platform.admin.developer.dataflow.dto;

import com.xmcares.platform.admin.developer.dataflow.model.DevDataflowResource;
import com.xmcares.platform.admin.developer.dataflow.vo.DevDataflowNodeVo;

import java.io.Serializable;
import java.util.List;

public class BuildFulleNodeResultDto implements Serializable {
    private List<DevDataflowNodeVo> nodes;
    private List<DevDataflowResource> useResources;

    public BuildFulleNodeResultDto(List<DevDataflowNodeVo> nodes, List<DevDataflowResource> useResources) {
        this.nodes = nodes;
        this.useResources = useResources;
    }

    public List<DevDataflowNodeVo> getNodes() {
        return nodes;
    }

    public List<DevDataflowResource> getUseResources() {
        return useResources;
    }
}
