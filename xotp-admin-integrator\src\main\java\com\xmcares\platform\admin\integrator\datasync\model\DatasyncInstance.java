package com.xmcares.platform.admin.integrator.datasync.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.integrator.datasync.task.IRemoveData;
import com.xmcares.platform.admin.integrator.datasync.vo.DataxTempVo;
import org.apache.commons.codec.digest.PureJavaCrc32;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/16 17:02
 */
public class DatasyncInstance implements IRemoveData, Serializable {

    public static final String ENTITY_NAME = "bdp_intg_datasync_instance";

    /** ID */
    private String id;
    /** 数据同步ID */
    private String datasyncId;
    /** 发布时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date publishTime;
    /** 更新时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /** 创建人 */
    private String publishUser;
    /** 同步任务名称 */
    private String instanceName;
    /** 实例编码 */
    private String instanceCode;
    /** 模板实际内容 */
    private String templateContext;
    /** 模板校验码 */
    private String templateCode;
    /** 数据来源集成方式 0:内置 1:插件 */
    private String orginType;
    /** 数据来源数据源名称 */
    private String orginDatasourceName;
    /** 数据来源插件路径 */
    private String orginPluginPath;
    /** 数据去向集成方式 0:内置 1：插件 */
    private String destType;
    /** 数据去向数据源名称 */
    private String destDatasourceName;
    /** 数据去向插件路径 */
    private String destPluginPath;
    /** 调度任务ID */
    private String dispatchId;

    private int jobGroup;

    private int triggerStatus;

    private String scheduleType;

    /**
     * 调度配置（cron）
     */
    private String scheduleConf;
    /**
     * 路由策略
     */
    private String executorRouteStrategy;
    /**
     * 阻塞策略
     */
    private String executorBlockStrategy;
    /**
     * 超时时间
     */
    private int executorTimeout;
    /**
     * 失败重试次数
     */
    private int executorFailRetryCount;

    /**
     * 调度过期策略
     */
    private String misfireStrategy;

    /**
     * 调度执行器
     */
    private String executorHandler;

    /**
     * 调度执行参数
     */
    private String executorParam;

    /**
     * 子任务JobId
     */
    private String childJobid;

    /** 文件路径 */
    private String filePath;
    /** 是否被删除 0：否 1：是 */
    private String hasDelete;

    public static DatasyncInstance newDatasyncInstance(String instanceName, String instanceCode, DataxTempVo templateContext, String path, Datasync datasync) {
        DatasyncInstance result = new DatasyncInstance();
        result.setDatasyncId(datasync.getId());
        result.setInstanceName(instanceName);
        result.setInstanceCode(instanceCode);
        result.setTemplateContext(templateContext.getContext());
        PureJavaCrc32 crc32 = new PureJavaCrc32();
        byte[] bytes;
        try {
            bytes = StringUtils.getBytes(templateContext.getContext(), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new IllegalArgumentException(String.format("获取[%s]字节数组异常", templateContext.getContext()), e);
        }
        crc32.update(bytes, 0, bytes.length);
        result.setTemplateCode(crc32.getValue()+"");
        result.setOrginType(datasync.getOrginType());
        result.setOrginDatasourceName(datasync.getOrginDatasourceName());
        result.setOrginPluginPath(datasync.getOrginPluginPath());
        result.setDestType(datasync.getDestType());
        result.setDestDatasourceName(datasync.getDestDatasourceName());
        result.setDestPluginPath(datasync.getDestPluginPath());
        result.setFilePath(path);
        result.setHasDelete(YNEnum.NO.getIntCharCode());
        return result;
    }

    @Override
    public String findId() {
        return id;
    }

    @Override
    public String findName() {
        return instanceName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDatasyncId() {
        return datasyncId;
    }

    public void setDatasyncId(String datasyncId) {
        this.datasyncId = datasyncId;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getPublishUser() {
        return publishUser;
    }

    public void setPublishUser(String publishUser) {
        this.publishUser = publishUser;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public String getInstanceCode() {
        return instanceCode;
    }

    public void setInstanceCode(String instanceCode) {
        this.instanceCode = instanceCode;
    }

    public String getTemplateContext() {
        return templateContext;
    }

    public void setTemplateContext(String templateContext) {
        this.templateContext = templateContext;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getOrginType() {
        return orginType;
    }

    public void setOrginType(String orginType) {
        this.orginType = orginType;
    }

    public String getOrginDatasourceName() {
        return orginDatasourceName;
    }

    public void setOrginDatasourceName(String orginDatasourceName) {
        this.orginDatasourceName = orginDatasourceName;
    }

    public String getOrginPluginPath() {
        return orginPluginPath;
    }

    public void setOrginPluginPath(String orginPluginPath) {
        this.orginPluginPath = orginPluginPath;
    }

    public String getDestType() {
        return destType;
    }

    public void setDestType(String destType) {
        this.destType = destType;
    }

    public String getDestDatasourceName() {
        return destDatasourceName;
    }

    public void setDestDatasourceName(String destDatasourceName) {
        this.destDatasourceName = destDatasourceName;
    }

    public String getDestPluginPath() {
        return destPluginPath;
    }

    public void setDestPluginPath(String destPluginPath) {
        this.destPluginPath = destPluginPath;
    }

    public String getDispatchId() {
        return dispatchId;
    }

    public void setDispatchId(String dispatchId) {
        this.dispatchId = dispatchId;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getHasDelete() {
        return hasDelete;
    }

    public void setHasDelete(String hasDelete) {
        this.hasDelete = hasDelete;
    }

    public String getScheduleConf() {
        return scheduleConf;
    }

    public void setScheduleConf(String scheduleConf) {
        this.scheduleConf = scheduleConf;
    }

    public String getExecutorRouteStrategy() {
        return executorRouteStrategy;
    }

    public void setExecutorRouteStrategy(String executorRouteStrategy) {
        this.executorRouteStrategy = executorRouteStrategy;
    }

    public String getExecutorBlockStrategy() {
        return executorBlockStrategy;
    }

    public void setExecutorBlockStrategy(String executorBlockStrategy) {
        this.executorBlockStrategy = executorBlockStrategy;
    }

    public int getExecutorTimeout() {
        return executorTimeout;
    }

    public void setExecutorTimeout(int executorTimeout) {
        this.executorTimeout = executorTimeout;
    }

    public int getExecutorFailRetryCount() {
        return executorFailRetryCount;
    }

    public void setExecutorFailRetryCount(int executorFailRetryCount) {
        this.executorFailRetryCount = executorFailRetryCount;
    }

    public int getJobGroup() {
        return jobGroup;
    }

    public void setJobGroup(int jobGroup) {
        this.jobGroup = jobGroup;
    }

    public int getTriggerStatus() {
        return triggerStatus;
    }

    public void setTriggerStatus(int triggerStatus) {
        this.triggerStatus = triggerStatus;
    }

    public String getScheduleType() {
        return scheduleType;
    }

    public void setScheduleType(String scheduleType) {
        this.scheduleType = scheduleType;
    }

    public String getMisfireStrategy() {
        return misfireStrategy;
    }

    public void setMisfireStrategy(String misfireStrategy) {
        this.misfireStrategy = misfireStrategy;
    }

    public String getExecutorHandler() {
        return executorHandler;
    }

    public void setExecutorHandler(String executorHandler) {
        this.executorHandler = executorHandler;
    }

    public String getExecutorParam() {
        return executorParam;
    }

    public void setExecutorParam(String executorParam) {
        this.executorParam = executorParam;
    }

    public String getChildJobid() {
        return childJobid;
    }

    public void setChildJobid(String childJobid) {
        this.childJobid = childJobid;
    }
}
