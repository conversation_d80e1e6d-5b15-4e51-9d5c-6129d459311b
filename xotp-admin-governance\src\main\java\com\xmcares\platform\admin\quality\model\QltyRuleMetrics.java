package com.xmcares.platform.admin.quality.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/11 16:11
 */
@ApiModel(value = QltyRuleMetrics.TABLE, description = "数据质量规则指标记录")
public class QltyRuleMetrics implements Serializable {
    public static final String TABLE = "bdp_qlty_rule_metrics";

    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "质量规则ID")
    private String rule_id;

    @ApiModelProperty(value = "质量维度编码")
    private String dim_code;

    @ApiModelProperty(value = "质量规则调度ID")
    private String rule_scheduler_id;

    @ApiModelProperty(value = "仓库ID")
    private String dataware_id;

    @ApiModelProperty(value = "仓库名称")
    private String dataware_name;

    @ApiModelProperty(value = "表ID")
    private String datatable_id;

    @ApiModelProperty(value = "表名称")
    private String datatable_name;

    @ApiModelProperty(value = "规则级别")
    private String rule_level;

    @ApiModelProperty(value = "规则对象(冗余)")
    private String rule_targets;

    @ApiModelProperty(value = "质量指标日期")
    private String metric_date;

    @ApiModelProperty(value = "质量指标值")
    private String metric_value;


    @ApiModelProperty(value = "规则唯一标识")
    private String rule_hashcode;


    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "规则运行时间")
    private String check_time;

    @ApiModelProperty(value = "规则运行状态（0,失败；1，成功）")
    private String check_status;


    @ApiModelProperty(value = "规则运行报告")
    private String check_report;

    @ApiModelProperty(value = "告警(0:无，1：告警)")
    private String warn_status;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "create_time")
    private String create_time;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "update_time")
    private String update_time;


    public static String getTABLE() {
        return TABLE;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRule_id() {
        return rule_id;
    }

    public void setRule_id(String rule_id) {
        this.rule_id = rule_id;
    }

    public String getDim_code() {
        return dim_code;
    }

    public void setDim_code(String dim_code) {
        this.dim_code = dim_code;
    }

    public String getRule_scheduler_id() {
        return rule_scheduler_id;
    }

    public void setRule_scheduler_id(String rule_scheduler_id) {
        this.rule_scheduler_id = rule_scheduler_id;
    }

    public String getDataware_id() {
        return dataware_id;
    }

    public void setDataware_id(String dataware_id) {
        this.dataware_id = dataware_id;
    }

    public String getDataware_name() {
        return dataware_name;
    }

    public void setDataware_name(String dataware_name) {
        this.dataware_name = dataware_name;
    }

    public String getDatatable_id() {
        return datatable_id;
    }

    public void setDatatable_id(String datatable_id) {
        this.datatable_id = datatable_id;
    }

    public String getDatatable_name() {
        return datatable_name;
    }

    public void setDatatable_name(String datatable_name) {
        this.datatable_name = datatable_name;
    }

    public String getRule_level() {
        return rule_level;
    }

    public void setRule_level(String rule_level) {
        this.rule_level = rule_level;
    }

    public String getRule_targets() {
        return rule_targets;
    }

    public void setRule_targets(String rule_targets) {
        this.rule_targets = rule_targets;
    }

    public String getMetric_date() {
        return metric_date;
    }

    public void setMetric_date(String metric_date) {
        this.metric_date = metric_date;
    }

    public String getMetric_value() {
        return metric_value;
    }

    public void setMetric_value(String metric_value) {
        this.metric_value = metric_value;
    }

    public String getCheck_time() {
        return check_time;
    }

    public void setCheck_time(String check_time) {
        this.check_time = check_time;
    }

    public String getCheck_status() {
        return check_status;
    }

    public void setCheck_status(String check_status) {
        this.check_status = check_status;
    }

    public String getCheck_report() {
        return check_report;
    }

    public void setCheck_report(String check_report) {
        this.check_report = check_report;
    }

    public String getWarn_status() {
        return warn_status;
    }

    public void setWarn_status(String warn_status) {
        this.warn_status = warn_status;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public String getRule_hashcode() {
        return rule_hashcode;
    }

    public void setRule_hashcode(String rule_hashcode) {
        this.rule_hashcode = rule_hashcode;
    }
}
