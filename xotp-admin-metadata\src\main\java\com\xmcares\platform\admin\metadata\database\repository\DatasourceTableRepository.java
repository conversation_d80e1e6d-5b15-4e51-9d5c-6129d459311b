package com.xmcares.platform.admin.metadata.database.repository;

import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.platform.admin.metadata.database.model.DatasourceTable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/16 16:32
 */
@Repository
public class DatasourceTableRepository {

    String DATASROUCE_TABLE = "bdp_meta_datatable";

    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    public List<DatasourceTable> listDatasourceTable(String datasourceId) {

        return xcfJdbcTemplate.queryForEntities(
                "SELECT * FROM " + DATASROUCE_TABLE + " WHERE  datasource_id = ?",
                new Object[]{datasourceId},
                DatasourceTable.class);
    }

    public DatasourceTable getDatasourceTable(String datasourceTableId) {
        DatasourceTable datasourceTable = xcfJdbcTemplate.queryForEntity(
                "SELECT * FROM " + DATASROUCE_TABLE + " WHERE id = ?",
                new Object[]{datasourceTableId},
                DatasourceTable.class);

        return datasourceTable;
    }

    public DatasourceTable getDatasourceTableByName(String datasourceTableName, String dataSourceId) {
        DatasourceTable datasourceTable = xcfJdbcTemplate.queryForEntity(
                "SELECT * FROM " + DATASROUCE_TABLE + " WHERE name = ? AND datasource_id = ?",
                new Object[]{datasourceTableName, dataSourceId},
                DatasourceTable.class
        );

        return datasourceTable;
    }


    public int deleteDatasourceTable(String datasourceTableId) {
        int deleteTable = xcfJdbcTemplate.update(
                "DELETE  FROM " + DATASROUCE_TABLE + " WHERE id = ? ",
                datasourceTableId);

        return deleteTable ;
    }

    public boolean updateTable(DatasourceTable datasourceTable) {
        int updateTable = xcfJdbcTemplate.update(
                "UPDATE " + DATASROUCE_TABLE + " SET  datasource_id = ? , remark = ? , alias = ? , `name` = ? WHERE  id = ? ",
                datasourceTable.getDatasourceId(),
                datasourceTable.getRemark(),
                datasourceTable.getAlias(),
                datasourceTable.getName(),
                datasourceTable.getId()
        );

        return updateTable > 0;
    }

    public DatasourceTable getDatatable(String datasourceTableId) {
        return xcfJdbcTemplate.queryForEntity(
                "SELECT * FROM " + DATASROUCE_TABLE + " WHERE id = ? ",
                new Object[]{datasourceTableId},
                DatasourceTable.class);
    }



    public void batchSave(List<DatasourceTable> datasourceTables) {
        List<Object[]> params = new ArrayList<>();
        String sql = null;
        for (DatasourceTable datasourceTable : datasourceTables) {
            Map<String, Object> map = DBUtils.insertSqlAndObjects(datasourceTable, DatasourceTable.class, DATASROUCE_TABLE);
            params.add(DBUtils.getObjects(map));
            if (sql == null) {
                sql = DBUtils.getSql(map);
            }
        }
        assert sql != null;
        xcfJdbcTemplate.batchUpdate(sql, params);
    }

    public void removeAll(List<String> tableIds) {
        StringBuilder sqlBuild = new StringBuilder("DELETE FROM ");
        sqlBuild.append(DATASROUCE_TABLE).append(" WHERE ").append(" id ").append(" IN ").append("(").append("?");
        if (tableIds.size() > 1) {
            for (int i = 1; i < tableIds.size(); i++) {
                sqlBuild.append(",").append("?");
            }
        }
        sqlBuild.append(")");
        xcfJdbcTemplate.update(sqlBuild.toString(), tableIds.toArray(new Object[] {}));
    }
}
