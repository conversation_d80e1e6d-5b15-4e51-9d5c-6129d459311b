package com.xmcares.platform.admin.quality.model.vo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/17 13:48
 */
public class QualityRuleMetricsListQueryVO implements Serializable {

    private String id;
    private String ruleId;
    private String ruleName;
    private String ruleSchedulerTaskName;
    private String checkStatus;
    private String checkTime;
    private String metricDate;
    private String metricValue;
    private String warmStatus;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getRuleSchedulerTaskName() {
        return ruleSchedulerTaskName;
    }

    public void setRuleSchedulerTaskName(String ruleSchedulerTaskName) {
        this.ruleSchedulerTaskName = ruleSchedulerTaskName;
    }

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(String checkTime) {
        this.checkTime = checkTime;
    }

    public String getMetricDate() {
        return metricDate;
    }

    public void setMetricDate(String metricDate) {
        this.metricDate = metricDate;
    }

    public String getMetricValue() {
        return metricValue;
    }

    public void setMetricValue(String metricValue) {
        this.metricValue = metricValue;
    }

    public String getWarmStatus() {
        return warmStatus;
    }

    public void setWarmStatus(String warmStatus) {
        this.warmStatus = warmStatus;
    }
}
