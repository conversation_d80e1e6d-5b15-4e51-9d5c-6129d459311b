package com.xmcares.platform.admin.metadata.database.vo;

import com.xmcares.platform.admin.common.validation.Insert;
import com.xmcares.platform.admin.metadata.database.model.DatasourceColumn;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/20 16:42
 */
public class DatacolumnSaveVo implements Serializable {

    @NotEmpty(message = "表ID不允许为空", groups = {Insert.class})
    private String tableId;
    private List<DatasourceColumn> columns;

    public String getTableId() {
        return tableId;
    }

    public void setTableId(String tableId) {
        this.tableId = tableId;
    }

    public List<DatasourceColumn> getColumns() {
        return columns;
    }

    public void setColumns(List<DatasourceColumn> columns) {
        this.columns = columns;
    }
}
