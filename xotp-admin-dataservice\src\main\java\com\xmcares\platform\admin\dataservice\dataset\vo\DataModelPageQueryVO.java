package com.xmcares.platform.admin.dataservice.dataset.vo;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/9/16 08:28
 */
public class DataModelPageQueryVO implements Serializable {
    private String datasetId;
    private String datasetCode;
    private String datasetName;
    private String datasourceId;
    private String datasourceName;
    private Date createTime;
    private String datasetPublished;
    private String datasetSql;
//    private String options;
    private String datasourceType;
    private Long timeConsuming;
    private String serviceApi;
    private String datasetParameter;
    private String datasetMode;

    public String getDatasetId() {
        return datasetId;
    }

    public void setDatasetId(String datasetId) {
        this.datasetId = datasetId;
    }

    public String getDatasetCode() {
        return datasetCode;
    }

    public void setDatasetCode(String datasetCode) {
        this.datasetCode = datasetCode;
    }

    public String getDatasetName() {
        return datasetName;
    }

    public void setDatasetName(String datasetName) {
        this.datasetName = datasetName;
    }

    public String getDatasourceId() {
        return datasourceId;
    }

    public void setDatasourceId(String datasourceId) {
        this.datasourceId = datasourceId;
    }

    public String getDatasourceName() {
        return datasourceName;
    }

    public void setDatasourceName(String datasourceName) {
        this.datasourceName = datasourceName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getDatasetPublished() {
        return datasetPublished;
    }

    public void setDatasetPublished(String datasetPublished) {
        this.datasetPublished = datasetPublished;
    }

/*    public String getOptions() {
        return options;
    }

    public void setOptions(String options) {
        this.options = options;
    }*/

    public String getDatasourceType() {
        return datasourceType;
    }

    public void setDatasourceType(String datasourceType) {
        this.datasourceType = datasourceType;
    }

    public Long getTimeConsuming() {
        return timeConsuming;
    }

    public void setTimeConsuming(Long timeConsuming) {
        this.timeConsuming = timeConsuming;
    }

    public String getServiceApi() {
        return serviceApi;
    }

    public void setServiceApi(String serviceApi) {
        this.serviceApi = serviceApi;
    }

    public String getDatasetParameter() {
        return datasetParameter;
    }

    public void setDatasetParameter(String datasetParameter) {
        this.datasetParameter = datasetParameter;
    }

    public String getDatasetSql() {
        return datasetSql;
    }

    public void setDatasetSql(String datasetSql) {
        this.datasetSql = datasetSql;
    }

    public String getDatasetMode() {
        return datasetMode;
    }

    public void setDatasetMode(String datasetMode) {
        this.datasetMode = datasetMode;
    }
}
