{
  "plugin_name": "Plugin",
  "plugin_output": "${source.plugin_output!'default_source_output'}",

  "reader_class_name": "${source.reader_class_name}",

  <#-- 可选参数：只有在提供了 jar_path 时才生成此字段 -->
  <#if source.reader_jar_path?? && source.reader_jar_path?has_content>
      "reader_jar_path": "${source.reader_jar_path}",
  </#if>

  <#--
    可选参数：自定义的 reader_options
    这里假设 'source.reader_options_json' 是一个已经序列化好的 JSON 字符串。
    这样处理可以灵活地传递任意复杂的嵌套对象给自定义 Reader。
  -->
  <#if source.reader_options_json?? && source.reader_options_json?has_content>
      "reader_options": ${source.reader_options_json}
  <#else>
      "reader_options": {}
  </#if>
}
