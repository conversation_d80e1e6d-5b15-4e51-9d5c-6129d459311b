package com.xmcares.platform.admin.dataservice.dataset.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.framework.sharing.repository.ServiceRepository;
import com.xmcares.framework.sharing.repository.jdbc.mybatis.AppMapper;
import com.xmcares.platform.admin.common.datasource.DataSourceOptions;
import com.xmcares.platform.admin.common.datasource.jdbc.JdbcDataSource;
import com.xmcares.platform.admin.common.datasource.jdbc.JdbcDataSourceManager;
import com.xmcares.platform.admin.common.jdbc.JdbcQuery;
import com.xmcares.platform.admin.dataservice.dataset.repository.ServiceModelRepository;
import com.xmcares.platform.admin.dataservice.dataset.vo.*;
import com.xmcares.platform.admin.metadata.common.model.Result;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import com.xmcares.platform.admin.metadata.database.repository.GetDatasourceDataRepository;
import org.apache.ibatis.builder.xml.XMLMapperEntityResolver;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.parsing.XNode;
import org.apache.ibatis.parsing.XPathParser;
import org.apache.ibatis.scripting.xmltags.XMLScriptBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/30 10:15
 */
@Service
public class ServiceModelService {
    Logger logger = LoggerFactory.getLogger(ServiceModelService.class);

    @Autowired
    ServiceModelRepository serviceModelRepository;

    @Autowired
    ServiceRepository serviceRepository;

    @Autowired
    private AppMapper appMapper;

    @Autowired
    JdbcDataSourceManager dataSourceManager;

    GetDatasourceDataRepository getDataRepository = new GetDatasourceDataRepository();

    /**
     * 创建或更新Dataset
     *
     * @param datasetId
     * @param datasourceId
     * @param datasetName
     * @param datasetCode
     * @return
     */
    public Result<CreateOrCopyDataModelReturnVO> createDataModel(String datasetId, String datasourceId,
            String datasetName, String datasetCode, String datasetSql, String datasetParameter, String datasetMode) {
        CreateOrCopyDataModelReturnVO dataModelLessInfo = serviceModelRepository.createDataModelLessInfo(datasetId,
                datasourceId, datasetName, datasetCode, datasetSql, datasetParameter, datasetMode);
        boolean success = true;
        String message = "";
        if (dataModelLessInfo == null) {
            success = false;
            message = "存储失败";
        }
        return new Result<CreateOrCopyDataModelReturnVO>(success, message, dataModelLessInfo);
    }

    /**
     * 分页条件查询dataset和datasource
     *
     * @param pageSize
     * @param pageNo
     * @param datasetName
     * @param datasourceId
     * @param datasetPublished
     * @return
     */
    public Result<Page<DataModelPageQueryVO>> dataModelPageQuery(Integer pageSize, Integer pageNo, String datasetName,
            String datasourceId, String datasetPublished) {
        boolean success = true;
        String message = "";
        Page<DataModelPageQueryVO> page = new Page<DataModelPageQueryVO>();
        try {
            if (isEmptyOrNull(datasetName)) {
                datasetName = "%";
            }
            if (isEmptyOrNull(datasetPublished)) {
                datasetPublished = "%";
            }

            // 去掉options type 根据options type 连接数据源并测试延迟
            List<DataModelPageQueryVO> datasetsInfo = serviceModelRepository.dataModelPageQuery(pageSize, pageNo,
                    datasetName, datasourceId, datasetPublished);
            if (datasetsInfo != null) {
                for (DataModelPageQueryVO info : datasetsInfo) {
                    // String dsType = String.valueOf(info.get("datasourceType"));
                    // String dsOptions = String.valueOf(info.get("options"));
                    // Map<String, Object> testInfo = DSConnector.testDataSource(dsType, dsOptions);
                    // info.put("timeConsuming",
                    // Long.valueOf(String.valueOf(testInfo.get("timeConsuming"))));
                    info.setTimeConsuming(null);
                    // TODO 此处固定写法之后根据需求可能要修改表获得根据不同服务得到的完整url
                    info.setServiceApi("/admin-service/developer/api-safe/" + info.getDatasetCode());
                }
                Integer total = serviceModelRepository.dataModelTotalCount(datasetName, datasourceId, datasetPublished);
                page.setData(datasetsInfo);
                page.setPageNo(pageNo);
                page.setPageSize(pageSize);
                page.setTotal(total);
            }
        } catch (Exception e) {
            success = false;
            message = e.toString();
        }

        return new Result<Page<DataModelPageQueryVO>>(success, message, page);
    }

    /**
     * 删除dataset
     *
     * @param datasetIds
     * @return
     */
    public Result<List<DeleteDatasetVO>> deleteDatasetModels(List<String> datasetIds) {
        boolean success = true;
        String message = "";
        ArrayList<DeleteDatasetVO> deleteDatasetVOS = new ArrayList<>();
        try {
            for (String datasetId : datasetIds) {
                int isDeleted = serviceModelRepository.deleteDatasetModel(datasetId);
                DeleteDatasetVO deleteDatasetVO = new DeleteDatasetVO();
                deleteDatasetVO.setDatasetId(datasetId);
                deleteDatasetVO.setDeleted(isDeleted == 1);
                deleteDatasetVOS.add(deleteDatasetVO);
            }
        } catch (Exception e) {
            success = false;
            message = e.toString();
        }
        return new Result<List<DeleteDatasetVO>>(success, message, deleteDatasetVOS);
    }

    /**
     * 复制dataset
     *
     * @param datasetId
     * @param datasetCode
     * @param datasetName
     * @return
     */
    public Result<CreateOrCopyDataModelReturnVO> copy(String datasetId, String datasetCode, String datasetName) {
        boolean success = true;
        String message = "";
        CreateOrCopyDataModelReturnVO data = new CreateOrCopyDataModelReturnVO();
        try {
            if (!nameUniqueCheck(datasetCode).getData()) {
                throw new Exception("datasetCode 重复");
            }
            MetaDatasetVO metaDataset = serviceModelRepository.getDatasetById(datasetId);
            if (metaDataset != null) {
                String id = SnowflakeGenerator.getNextId().toString();
                metaDataset.setId(id);
                int dataModel = serviceModelRepository.createDataModel(metaDataset, datasetCode, datasetName);
                if (dataModel == 1) {
                    data.setDatasetId(id);
                }
            } else {
                throw new Exception("查询dataset错误");
            }
        } catch (Exception e) {
            success = false;
            message = e.toString();
        }
        return new Result<CreateOrCopyDataModelReturnVO>(success, message, data);
    }

    /**
     * 按照datasetId查询dataset
     *
     * @param datasetId
     * @return
     */
    public Result<MetaDatasetVO> getDatasetById(String datasetId) {
        MetaDatasetVO datasetById = serviceModelRepository.getDatasetById(datasetId);
        String message = "";
        boolean success = true;
        if (datasetById == null) {
            success = false;
            message = "查询失败";
        }
        return new Result<MetaDatasetVO>(success, message, datasetById);
    }

    /**
     * 判断datasetCode名称合法和唯一性
     *
     * @param datasetCode
     * @return
     */
    public Result<Boolean> nameUniqueCheck(String datasetCode) {
        boolean success = true;
        String message = "";
        boolean data = false;
        String regex = "^[a-zA-Z0-9_]+$";
        try {
            data = datasetCode.matches(regex) && (serviceModelRepository.nameUniqueCheck(datasetCode) == 0);
        } catch (Exception e) {
            success = false;
            message = e.toString();
        }
        return new Result<Boolean>(success, message, data);
    }

    /**
     * 发布或取消发布dataset的sql url
     *
     * @param datasetId
     * @param isPublish
     * @return
     */
    public Result<PublishDatasetModelVO> isPublishDatasetModel(String datasetId, String isPublish,
            String serviceCategory, String serviceName) {
        boolean success = true;
        String message = "";
        PublishDatasetModelVO data = null;
        try {
            if (!("0".equals(isPublish) || "1".equals(isPublish))) {
                success = false;
                data = null;
                message = "isPublish 请输入 0/1";
            } else {
                String serviceId = null;
                if ("1".equals(isPublish)) {
                    serviceId = SnowflakeGenerator.getNextId().toString();
                }
                data = serviceModelRepository.isPublishDatasetModel(datasetId, isPublish, serviceId);
                if (data == null) {
                    success = false;
                    message = "发布过程失败";
                } else {
                    if ("1".equals(isPublish)) {
                        com.xmcares.framework.sharing.domain.model.Service oldService = serviceRepository
                                .getService(data.getServiceId());
                        if (oldService != null) {
                            serviceRepository.deleteService(data.getServiceId());
                        }
                        com.xmcares.framework.sharing.domain.model.Service service = new com.xmcares.framework.sharing.domain.model.Service();
                        service.setId(data.getServiceId());
                        service.setCreateTime(new Date());
                        service.setUpdateTime(new Date());
                        service.setCategory(
                                serviceCategory == null || "".equals(serviceCategory) ? "数据开发平台" : serviceCategory);
                        service.setName(
                                serviceName == null || "".equals(serviceName) ? data.getDatasetName() : serviceName);
                        service.setUri(data.getServiceApi());
                        service.setStatus("1");
                        serviceRepository.createService(service);
                        ArrayList<String> serviceIds = new ArrayList<>();
                        serviceIds.add(data.getServiceId());
                        appMapper.saveAppServices("3E3F2AD531D7447DB5CC19645ADFF1FC", serviceIds);
                    } else if ("0".equals(isPublish)) {
                        serviceRepository.deleteService(data.getServiceId());
                    } else {
                        throw new Exception("isPublish 请输入 0/1");
                    }
                }

            }
        } catch (Exception e) {
            success = false;
            message = e.toString();
        }
        return new Result<PublishDatasetModelVO>(success, message, data);
    }

    /**
     * 通过数据源id获取表信息
     *
     * @param datasourceId
     * @return
     */
    public Result<List<DatatableVO>> getDatatableById(String datasourceId) {
        List<DatatableVO> datatableById = serviceModelRepository.getDatatableById(datasourceId);
        String message = "";
        boolean success = true;
        if (datatableById == null) {
            success = false;
            message = "查询失败";
        }
        return new Result<List<DatatableVO>>(success, message, datatableById);
    }

    /**
     * 根据数据源id和表id获取字段信息
     *
     * @param datasourceId
     * @param datatableId
     * @return
     */
    public Result<List<DatatableFieldVO>> getDatatableFieldById(String datasourceId, String datatableId) {
        List<DatatableFieldVO> datatableFieldById = serviceModelRepository.getDatatableFieldById(datasourceId,
                datatableId);
        String message = "";
        boolean success = true;
        if (datatableFieldById == null) {
            success = false;
            message = "查询失败";
        }
        return new Result<List<DatatableFieldVO>>(success, message, datatableFieldById);
    }

    /**
     * 通过数据源id和表id获取表里的数据预览
     *
     * @param datasourceId
     * @param datatableId
     * @return
     */
    public Result<List<Map<String, Object>>> showTableData(String datasourceId, String datatableId) {
        boolean success = true;
        String message = "";
        List<Map<String, Object>> data = null;
        try {
            List<TableInfoVO> tableInfo = serviceModelRepository.getTableInfo(datasourceId, datatableId);
            if (tableInfo != null && tableInfo.size() == 1) {
                TableInfoVO tableInfoVO = tableInfo.get(0);
                String datasourceType = String.valueOf(tableInfoVO.getDatasourceType());
                String datasourceOptions = String.valueOf(tableInfoVO.getDatasourceOptions());
                String tableName = String.valueOf(tableInfoVO.getTableName());
                data = getDataRepository.getData(datasourceType, datasourceOptions, tableName,
                        "SELECT * FROM " + tableName + " limit 100");
            }
        } catch (Exception e) {
            success = false;
            message = e.toString();
        }
        return new Result<List<Map<String, Object>>>(success, message, data);
    }

    /**
     * 通过权限表服务id获取服务信息
     *
     * @param serviceId
     * @return
     */
    public Result<com.xmcares.framework.sharing.domain.model.Service> getServiceById(String serviceId) {
        boolean success = true;
        String message = "";
        com.xmcares.framework.sharing.domain.model.Service service = null;
        try {
            service = serviceRepository.getService(serviceId);
            if (service == null) {
                success = false;
                message = "获取失败";
            }
        } catch (Exception e) {
            message = e.toString();
        }
        return new Result<com.xmcares.framework.sharing.domain.model.Service>(success, message, service);
    }

    public Result<List<Map<String, Object>>> executeSql(String datasourceId, String sql, String sqlParams) {
        boolean success = true;
        String message = "";
        List<Map<String, Object>> data = null;
        JdbcDataSource dataSource = null;
        JSONObject jsonObject = null;
        try {
            if (sql == null) {
                throw new Exception("不存在sql语句");
            }
            if (sqlParams != null) {
                jsonObject = JSON.parseObject(sqlParams);
            }
            if (sql.contains("#(")) {
                for (String k : jsonObject.keySet()) {
                    sql = sql.replace("#(" + k + ")", jsonObject.getString(k));
                }
            }
            Datasource datasourceMeta = serviceModelRepository.getDatasourceById(datasourceId);
            if (datasourceMeta != null) {
                DataSourceOptions options = datasourceMeta.toDataSourceOptions();
                dataSource = dataSourceManager.getOrCreateDataSource(options);

            }
            Datasource datasourceById = serviceModelRepository.getDatasourceById(datasourceId);
            if (sql.contains("<if") || sql.contains("<choose") || sql.contains("<![CDATA") || sql.contains("<select")) {
                // 追加select的xml标签
                if (!sql.contains("<select")) {
                    sql = "<select>" + sql + "</select>";
                }
                // 实例化解析XML对象
                XPathParser parser = new XPathParser(sql, false, null, new XMLMapperEntityResolver());
                XNode context = parser.evalNode("/select");
                org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
                configuration.setDatabaseId("");
                XMLScriptBuilder xmlScriptBuilder = new XMLScriptBuilder(configuration, context);
                SqlSource sqlSource = xmlScriptBuilder.parseScriptNode();
                // 获取转换xml标签后的sql对象
                BoundSql bs = sqlSource.getBoundSql(jsonObject);
                // 获取查询sql
                sql = bs.getSql();
            }

            data = JdbcQuery.getJdbcData(dataSource.getConnection(), sql);

            // data = getDataRepository.getData(datasourceById.getType(),
            // datasourceById.getOptions(), null, sql);
            // return new Result<List<Map<String, Object>>>(success, message, data);
        } catch (Exception e) {
            success = false;
            data = null;
            message = "执行失败";
            logger.error("执行失败", e);
        } finally {
            // 关闭数据源连接
            try {
                if (dataSource != null) {
                    Connection connection = dataSource.getConnection();
                    if (connection != null && !connection.isClosed()) {
                        connection.close();
                    }
                }
            } catch (Exception e) {
                // ignore
                logger.error("close失败", e);
            }
        }
        return new Result<List<Map<String, Object>>>(success, message, data);
    }

    private boolean isEmptyOrNull(String str) {
        return str == null || "".equals(str);
    }

    public Result<MetaDatasetVO> getDatasetByDatasetCode(String datasetCode) {
        MetaDatasetVO datasetByDatasetCode = serviceModelRepository.getDatasetByDatasetCode(datasetCode);
        String message = "";
        boolean success = true;
        if (datasetByDatasetCode == null) {
            success = false;
            message = "查询失败";
        }
        return new Result<MetaDatasetVO>(success, message, datasetByDatasetCode);
    }
}
