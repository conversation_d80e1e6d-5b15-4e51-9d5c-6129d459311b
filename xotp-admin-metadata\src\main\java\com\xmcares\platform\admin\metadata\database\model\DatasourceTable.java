package com.xmcares.platform.admin.metadata.database.model;

import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.common.database.metainfo.TableInfo;
import com.xmcares.platform.admin.common.validation.Update;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/16 16:22
 */
public class DatasourceTable implements Serializable {
    @NotNull(message = "id不允许为空", groups = Update.class)
    private String id;
    private String datasourceId;
    private String name;
    private String remark;
    private String alias;

    public static DatasourceTable createBy(String datasourceId, TableInfo tableInfo) {
        DatasourceTable result = new DatasourceTable();
        result.setId(SnowflakeGenerator.getNextId() + "");
        result.setDatasourceId(datasourceId);
        result.setName(tableInfo.getName());
        result.setRemark(tableInfo.getComment());
        result.setAlias(null);
        return result;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDatasourceId() {
        return datasourceId;
    }

    public void setDatasourceId(String datasourceId) {
        this.datasourceId = datasourceId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }
}
