package com.xmcares.platform.admin.integrator.plugin.web;

/**
 * PluginResourceController
 *
 * <AUTHOR>
 * @Descriptions PluginResourceController
 * @Date 2025/8/6 15:15
 */

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmcares.platform.admin.integrator.common.error.IntegratorException;
import com.xmcares.platform.admin.integrator.plugin.model.PluginResource;
import com.xmcares.platform.admin.integrator.plugin.model.PluginResourceQueryDTO;
import com.xmcares.platform.admin.integrator.plugin.model.PluginResourceVO;
import com.xmcares.platform.admin.integrator.plugin.service.PluginResourceService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PluginResourceController
 *
 * <AUTHOR>
 * @Descriptions PluginResourceController
 * @Date 2025/8/6 14:11
 */
@RestController
@RequestMapping("/plugin-resource")
public class PluginResourceController {

    @Resource
    private PluginResourceService pluginResourceService;

    /**
     * 1. 分页查询插件资源列表
     * 示例: GET /plugin-resource/page-list?pageNum=1&pageSize=10&name=my-plugin
     *
     * @param queryDTO 包含分页和过滤参数的对象
     * @return 分页结果对象
     */
    @GetMapping("/list-query")
    public Page<PluginResourceVO> pagePluginResources(PluginResourceQueryDTO queryDTO) {
        return pluginResourceService.pagePluginResources(queryDTO);
    }

    /**
     * 查询所有插件资源列表
     * 示例: GET /plugin-resource/list
     *
     * @return 结果对象
     */
    @GetMapping("/list")
    public List<PluginResource> listPluginResources() {
        return pluginResourceService.listPluginResources();
    }


    /**
     * 2. 上传并新增一个插件
     * 示例: POST /plugin-resource/upload
     * Body: form-data, 包含 file, remark, createUser 三个字段
     *
     * @param file   上传的 jar 文件 (必需)
     * @param name   插件名称
     * @param remark 插件备注
     * @return 创建成功后的插件信息
     */
    @PostMapping(value = "/upload", produces = MediaType.APPLICATION_JSON_VALUE)
    public PluginResource addPlugin(
            @RequestParam("file") MultipartFile file,
            @RequestParam("name") String name,
            @RequestParam("remark") String remark
    ) {
        String originalFilename = file.getOriginalFilename();

        // 在处理前，先校验文件类型
        if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".jar")) {
            throw new IllegalArgumentException("文件类型错误，请上传.jar文件。");
        }

        if (file.isEmpty()) {
            throw new IllegalArgumentException("上传的文件不能为空");
        }

        // 最大 512MB
        long maxSizeInBytes = 512L * 1024 * 1024;
        if (file.getSize() > maxSizeInBytes) {
            throw new IllegalArgumentException("文件大小超过限制，最大允许 512 MB。");
        }

        try {
            return pluginResourceService.addPlugin(file, name, remark);
        } catch (IOException e) {
            throw new RuntimeException("文件处理失败，请检查文件格式或联系管理员", e);
        }
    }

    /**
     * 3. 根据ID修改插件信息 (目前仅支持修改备注)
     * 示例: POST /plugin-resource/update/{id}
     * Body: JSON {"remark": "新的备注", "updateUser": "admin"}
     *
     * @param id      插件 ID (通过路径传入)
     * @param payload 包含 remark 和 updateUser 的 Map
     * @return 是否修改成功
     */
    @PostMapping(value = "/update/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean updatePluginRemark(
            @PathVariable String id,
            @RequestBody Map<String, String> payload) {
        String remark = payload.get("remark");
        return pluginResourceService.updatePluginRemark(id, remark);
    }

    /**
     * 4. 根据ID删除插件
     * 示例: POST /plugin-resource/delete/12345
     *
     * @param id 插件 ID (通过路径传入)
     * @return 是否删除成功
     */
    @PostMapping(value = "/delete/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean deletePlugin(@PathVariable String id) {
        return pluginResourceService.deletePlugin(id);
    }

    /**
     * 4. 根据ID下载插件
     * 示例: POST /plugin-resource/download/12345
     *
     * @param id 插件 ID (通过路径传入)
     * @param response HttpServletResponse 对象，用于写入文件流
     */
    @GetMapping(value = "/download/{id}")
    public void downloadPlugin(@PathVariable String id, HttpServletResponse response) throws IOException {
        pluginResourceService.downloadPlugin(id, response);
    }
    /**
     * 5. 根据ID获取插件详情
     * 示例: GET /plugin-resource/get/12345
     *
     * @param id 插件 ID (通过路径传入)
     * @return 插件详细信息。如果未找到，将返回 null。
     */
    @GetMapping("/get/{id}")
    public PluginResource getPluginById(@PathVariable String id) {
        return pluginResourceService.getPluginById(id);
    }

    /**
     * 6. 用于测试获取文件系统配置的接口
     * 示例: GET /plugin-resource/test
     *
     * @return 文件服务相关的配置信息(JSON字符串)
     */
    @GetMapping("/test")
    public String pagePluginResourceList() {
        Map<String, Object> activeFilesSystemConfig = pluginResourceService.getActiveFilesSystemConfig();
        return JSON.toJSONString(activeFilesSystemConfig);
    }

    /**
     * 这个方法专门用来处理本 Controller 内部抛出的 IntegratorException
     * Spring MVC在处理 multipart/form-data 类型的请求（即文件上传）时，
     * 其异常处理的路径和时机与处理普通 application/json 或 application/x-www-form-urlencoded 请求时有所不同。
     */
    @ExceptionHandler(IntegratorException.class)
    public ResponseEntity<Map<String, Object>> handleIntegratorException(IntegratorException ex, HttpServletRequest request) {
        Map<String, Object> errorBody = new HashMap<>();
        errorBody.put("error", HttpStatus.BAD_REQUEST.getReasonPhrase());
        errorBody.put("status", HttpStatus.BAD_REQUEST.value());
        errorBody.put("message", ex.getMessage());
        errorBody.put("timestamp", DateUtil.date().toString());
        errorBody.put("path", request.getRequestURI());

        // 创建HttpHeaders对象并设置Content-Type
        final HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 将响应体、头信息和HTTP状态码一起封装到ResponseEntity中
        return new ResponseEntity<>(errorBody, headers, HttpStatus.BAD_REQUEST);
    }
}
