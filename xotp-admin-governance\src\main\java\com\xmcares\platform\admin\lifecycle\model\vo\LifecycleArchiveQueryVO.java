package com.xmcares.platform.admin.lifecycle.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value = "LifecycleArchiveQueryVO", description = "查询归档列表")
public class LifecycleArchiveQueryVO implements Serializable {

    @ApiModelProperty(value = "调度名称")
    private String schedulerName;

    @ApiModelProperty(value = "调度ID")
    private String schedulerId;

    @ApiModelProperty(value = "启动|停止")
    private String started;
    @ApiModelProperty(value = "调度任务表达式")
    private String schedulerCronExpr;
    @ApiModelProperty(value = "调度路由策略")
    private String schedulerRouteStrategy;
    @ApiModelProperty(value = "调度阻塞策略")
    private String schedulerBlockStrategy;
    @ApiModelProperty(value = "调度超时时间")
    private String schedulerExecutorTimeout;
    @ApiModelProperty(value = "调度执行重试次数")
    private String schedulerExecutorRetryCount;
    @ApiModelProperty(value = "updateTime")
    private String updateTime;
    @ApiModelProperty(value = "表描述信息")
    private String remark;
    @ApiModelProperty(value = "归档信息ID")
    private String id;
    @ApiModelProperty(value = "需要归档的表的ID")
    private String datatableId;
    @ApiModelProperty(value = "仓库ID")
    private String datawareId;
    @ApiModelProperty(value = "表名称")
    private String datatableName;
    @ApiModelProperty(value = "是否删除原有表数据")
    private String datatableReduced;
    @ApiModelProperty(value = "启用近线归档，0：否1：是")
    private String nearlined;
    @ApiModelProperty(value = "近线归档数据源ID")
    private String nearlineDatasourceId;
    @ApiModelProperty(value = "近线归档数据源名称")
    private String nearlineDatasourceName;
    @ApiModelProperty(value = "近线归档数据表名称")
    private String nearlineDatatableName;
    @ApiModelProperty(value = "启用离线归档，0：否1：是")
    private String offlined;
    @ApiModelProperty(value = "离线归档文件名")
    private String offlineFileName;
    @ApiModelProperty(value = "离线保留天数")
    private String offlineFileLifedays;

    @ApiModelProperty(value = "archiveRangeExpr")
    private String archiveRangeExpr;

    @ApiModelProperty(value = "归档类型")
    private String archiveType;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDatatableId() {
        return datatableId;
    }

    public void setDatatableId(String datatableId) {
        this.datatableId = datatableId;
    }

    public String getDatawareId() {
        return datawareId;
    }

    public void setDatawareId(String datawareId) {
        this.datawareId = datawareId;
    }

    public String getDatatableName() {
        return datatableName;
    }

    public void setDatatableName(String datatableName) {
        this.datatableName = datatableName;
    }

    public String getDatatableReduced() {
        return datatableReduced;
    }

    public void setDatatableReduced(String datatableReduced) {
        this.datatableReduced = datatableReduced;
    }

    public String getNearlined() {
        return nearlined;
    }

    public void setNearlined(String nearlined) {
        this.nearlined = nearlined;
    }

    public String getNearlineDatasourceId() {
        return nearlineDatasourceId;
    }

    public void setNearlineDatasourceId(String nearlineDatasourceId) {
        this.nearlineDatasourceId = nearlineDatasourceId;
    }

    public String getNearlineDatasourceName() {
        return nearlineDatasourceName;
    }

    public void setNearlineDatasourceName(String nearlineDatasourceName) {
        this.nearlineDatasourceName = nearlineDatasourceName;
    }

    public String getNearlineDatatableName() {
        return nearlineDatatableName;
    }

    public void setNearlineDatatableName(String nearlineDatatableName) {
        this.nearlineDatatableName = nearlineDatatableName;
    }

    public String getOfflined() {
        return offlined;
    }

    public void setOfflined(String offlined) {
        this.offlined = offlined;
    }

    public String getOfflineFileName() {
        return offlineFileName;
    }

    public void setOfflineFileName(String offlineFileName) {
        this.offlineFileName = offlineFileName;
    }

    public String getOfflineFileLifedays() {
        return offlineFileLifedays;
    }

    public void setOfflineFileLifedays(String offlineFileLifedays) {
        this.offlineFileLifedays = offlineFileLifedays;
    }

    public String getSchedulerName() {
        return schedulerName;
    }

    public void setSchedulerName(String schedulerName) {
        this.schedulerName = schedulerName;
    }

    public String getSchedulerId() {
        return schedulerId;
    }

    public void setSchedulerId(String schedulerId) {
        this.schedulerId = schedulerId;
    }

    public String getSchedulerCronExpr() {
        return schedulerCronExpr;
    }

    public void setSchedulerCronExpr(String schedulerCronExpr) {
        this.schedulerCronExpr = schedulerCronExpr;
    }

    public String getSchedulerRouteStrategy() {
        return schedulerRouteStrategy;
    }

    public void setSchedulerRouteStrategy(String schedulerRouteStrategy) {
        this.schedulerRouteStrategy = schedulerRouteStrategy;
    }

    public String getSchedulerBlockStrategy() {
        return schedulerBlockStrategy;
    }

    public void setSchedulerBlockStrategy(String schedulerBlockStrategy) {
        this.schedulerBlockStrategy = schedulerBlockStrategy;
    }

    public String getSchedulerExecutorTimeout() {
        return schedulerExecutorTimeout;
    }

    public void setSchedulerExecutorTimeout(String schedulerExecutorTimeout) {
        this.schedulerExecutorTimeout = schedulerExecutorTimeout;
    }

    public String getSchedulerExecutorRetryCount() {
        return schedulerExecutorRetryCount;
    }

    public void setSchedulerExecutorRetryCount(String schedulerExecutorRetryCount) {
        this.schedulerExecutorRetryCount = schedulerExecutorRetryCount;
    }

    public String getStarted() {
        return started;
    }

    public void setStarted(String started) {
        this.started = started;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getArchiveRangeExpr() {
        return archiveRangeExpr;
    }

    public void setArchiveRangeExpr(String archiveRangeExpr) {
        this.archiveRangeExpr = archiveRangeExpr;
    }

    public String getArchiveType() {
        return archiveType;
    }

    public void setArchiveType(String archiveType) {
        this.archiveType = archiveType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
