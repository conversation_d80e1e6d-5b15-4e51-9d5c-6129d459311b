package com.xmcares.platform.admin.scheduler.xxljob.model.dto;

/**
 * JobLogPageListDto
 *
 * <AUTHOR>
 * @Descriptions JobLogPageListDto
 * @Date 2025/5/20 15:11
 */
public class JobLogPageListDto {
    private Integer page;

    private Integer rows;

    private Integer jobGroup;

    private Integer jobId;

    private Integer logStatus;

    private String filterTime;

    private String jobName;

    public JobLogPageListDto() {
    }

    public JobLogPageListDto(Integer page, Integer rows, Integer jobGroup, Integer jobId, Integer logStatus, String filterTime, String jobName) {
        this.page = (page == null || page < 1) ? 1 : page;
        this.rows = (rows == null || rows < 1) ? 10 : rows;
        this.jobGroup = jobGroup;
        this.jobId = jobId;
        this.logStatus = logStatus;
        this.filterTime = filterTime;
        this.jobName = jobName;
    }


    public Integer getPage() {
        return (page == null || page < 1) ? 1 : page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return (rows == null || rows < 1) ? 10 : rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getJobGroup() {
        return jobGroup;
    }

    public void setJobGroup(Integer jobGroup) {
        this.jobGroup = jobGroup;
    }

    public Integer getJobId() {
        return jobId;
    }

    public void setJobId(Integer jobId) {
        this.jobId = jobId;
    }

    public Integer getLogStatus() {
        return logStatus;
    }

    public void setLogStatus(Integer logStatus) {
        this.logStatus = logStatus;
    }

    public String getFilterTime() {
        return filterTime;
    }

    public void setFilterTime(String filterTime) {
        this.filterTime = filterTime;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    @Override
    public String toString() {
        return "JobLogPageListRequest{" +
                "page=" + page +
                ", rows=" + rows +
                ", jobGroup=" + jobGroup +
                ", jobId=" + jobId +
                ", logStatus=" + logStatus +
                ", filterTime='" + filterTime + '\'' +
                ", jobName='" + jobName + '\'' +
                '}';
    }
}
