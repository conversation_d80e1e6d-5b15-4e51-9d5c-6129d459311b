{
"name": "oraclereader",
"parameter": {
"username": "${orginDatasource.username}",
"password": "${orginDatasource.password}",
"column": [
<#list orginFieldNames! as field>
    "${field}"<#if field_has_next>,</#if>
</#list>
],
<#if orgin.querySql??>
<#else>
    <#if orgin.where??>"where":"${orgin.where}",</#if>
</#if>
<#if orgin.splitPk??>"splitPk":"${orgin.splitPk}",</#if>
<#if orgin.fetchSize??>"fetchSize":${orgin.fetchSize},</#if>
<#if orgin.session??>
    "session": [
    <#list orgin.session?split("#") as se>
        "${se}"<#if se_has_next>,</#if>
    </#list>
    ],
</#if>
"connection": [
{
<#if orgin.querySql??>
    "querySql": [
    <#list orgin.querySql?split("#") as query>
        "${query}"<#if query_has_next>,</#if>
    </#list>
    ],
</#if>
<#if orgin.querySql??>
<#else>
    <#if orgin.table??>"table":["${orgin.table}"],</#if>
</#if>
"jdbcUrl": [
"${orginDatasource.url}"
]
}
]
}
}