/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/24
 */
package com.xmcares.platform.admin.scheduler.xxljob.error;

import com.xxl.job.admin.controller.resolver.WebExceptionResolver;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Xxljob 直接作为模块整合进项目时，xxljob的{@link WebExceptionResolver}对项目的异常处理机制产生影响
 * <AUTHOR>
 * @since 1.0.0
 */
public class XxlJobWebExceptionResolver extends WebExceptionResolver {

    @Override
    public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 非HandlerMethod直接通过
        if (!(handler instanceof HandlerMethod)) {
            return null; // proceed with the next interceptor
        }

        HandlerMethod method = (HandlerMethod) handler;
        String packageName = method.getBeanType().getPackage().getName();
        // 仅对xxljob添加权限拦截解除
        if (!packageName.startsWith("com.xxl.job.admin")) {
            // 不是xxljob的请求放行
            return null;
        }
        return super.resolveException(request, response, handler, ex);
    }
}
