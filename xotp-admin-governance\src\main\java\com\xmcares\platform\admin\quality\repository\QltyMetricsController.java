package com.xmcares.platform.admin.quality.repository;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/16 09:19
 */
@FeignClient(name = "${xbdp.feign.hookbox-service.name:hookbox-service}",
        url = "${xbdp.feign.hookbox-service.url:}"
)
public interface QltyMetricsController {
    @PostMapping("/quality/metrics/flink/local-run")
    @ResponseBody
    public void metricsFlinkLocalRun(@RequestParam(name = "args") String[] args) throws Exception;

    @PostMapping("/quality/metrics/flink/kill")
    @ResponseBody
    public void metricsFlinkKill(@RequestParam(name = "jobId") String jobId,@RequestParam(name = "jarName") String jarName) throws Exception;
}
