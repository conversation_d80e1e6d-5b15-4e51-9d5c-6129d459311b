/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/30
 */
package com.xmcares.platform.admin.common.datasource.mq.ximc;

import com.xmcares.imcc.IMCCManager;
import com.xmcares.imcc.listener.IListener;
import com.xmcares.imcc.listener.TopicMessageListener;
import com.xmcares.platform.admin.common.datasource.mq.MessageHeaders;
import com.xmcares.platform.admin.common.datasource.mq.MqDataSource;
import com.xmcares.platform.admin.common.datasource.mq.TopicInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public class XimcDataSource implements MqDataSource {
    protected static final Logger logger = LoggerFactory.getLogger(XimcDataSource.class);

    public static final String HEADER_KEY_TOPIC = "topic";
    public static final String HEADER_KEY_SENDER = "sender";
    public static final String HEADER_KEY_SIGN = "sign";
    public static final String HEADER_KEY_SEND_TIME = "sendTime";


    private final String name;
    private final XimcProperties properties;
    private final List<TopicMessageListener> listeners = new CopyOnWriteArrayList<>();

    public XimcDataSource(String name, XimcProperties properties) {
        if (name == null) {
            throw new IllegalArgumentException("uniqueName must not be null");
        }
        this.name = name;
        this.properties = properties;
    }

    @Override
    public AvailableStatus testAvailable() {
        try {
            IMCCManager manager = XimcClientManager.borrowImcManager(this.name, this.properties);
            loginIfNecessary(manager);
            XimcClientManager.releaseImcManager(this.name, this.properties);
        } catch (Exception e) {
            return new AvailableStatus(false, e.getMessage());
        }
        return new AvailableStatus(true, null);
    }

    @Override
    public List<TopicInfo> getTopicInfos() {
        IMCCManager manager = XimcClientManager.borrowImcManager(this.name, this.properties);
        loginIfNecessary(manager);
        Collection<com.xmcares.imcc.model.TopicInfo> topics = manager.getPublishedTopics();
        return topics.stream().map(topic -> {
            TopicInfo result = new TopicInfo(topic.getNode());
            result.setComment(topic.getTitle());
            return result;
        }).collect(Collectors.toList());
    }

    @Override
    public void sendMessage(String topic, MessageHeaders headers, byte[] body) {
        IMCCManager manager = XimcClientManager.borrowImcManager(this.name, this.properties);
        loginIfNecessary(manager);
        manager.sendTopicMessage(topic, new String(body));
    }

    @Override
    public void pullMessage(String topic, String group, MessageHeaders headers, Callback callback) {
        throw new UnsupportedOperationException("XIMC不支持消息拉取模式");
    }

    /**
     * 此处添加监听器时，如果topicName字符串值和listener对象相同，则不会重复添加监听器。
     * @see IMCCManager#addListener(IListener)
     * @see TopicMessageListenerDelegate#hashCode()
     * @param topic 主题名称
     * @param listener 监听器
     */
    @Override
    public void addMessageListener(String topic, String group, MqMessageListener listener) {
        IMCCManager manager = XimcClientManager.borrowImcManager(this.name, this.properties);
        loginIfNecessary(manager);
        //此处多次调用只要 topicName值和listener对象相同 就不会重复添加监听器。IMCCManager#addListener实现中已经检查去重。
        TopicMessageListenerDelegate lsnDelegate = new TopicMessageListenerDelegate(this.name, topic, listener);
        manager.addListener(lsnDelegate);
        listeners.add(lsnDelegate);
    }

    @Override
    public void removeMessageListener(String topic, String group, MqMessageListener listener) {
        IMCCManager manager = XimcClientManager.borrowImcManager(this.name, this.properties);
        //同一个this.name， topic， listener对象相同，则不会重复添加监听器。IMCCManager#addListener实现中已经检查去重。
        TopicMessageListenerDelegate lsnDelegate = new TopicMessageListenerDelegate(this.name, topic, listener);
        manager.removeListener(lsnDelegate);
        listeners.remove(lsnDelegate);
    }

    @Override
    public void close() throws Exception {
        IMCCManager manager = XimcClientManager.borrowImcManager(this.name, this.properties);
        listeners.forEach(manager::removeListener);
        listeners.clear();
        XimcClientManager.releaseImcManager(this.name, this.properties);
    }


    @Override
    public String getName() {
        return this.name;
    }


    private void loginIfNecessary(IMCCManager manager) {
        if (!manager.hasLogined()) {
            manager.login(this.properties.getUsername(), this.properties.getPassword());
        }
    }

    /**
     * 监听器代理类
     * 每个数据源中可以添加多个不同topic的监听器，同一个topic不能让重复添加
     * 各个不同数据源（依据{@link #name}区别不同数据源）之间无关，虽然不同数据源可能使用同一个消息中心{@link IMCCManager}。
     */
    static class TopicMessageListenerDelegate implements TopicMessageListener {

        private final String sourceName;

        private final String topicName;

        private final MqMessageListener listener;

        public TopicMessageListenerDelegate(String sourceName, String topicName, MqMessageListener listener) {
            this.sourceName = sourceName;
            this.topicName = topicName;
            this.listener = listener;
        }

        @Override
        public void published(String id, String nodeId, String sender, boolean sign, String content, Date date)  {
            Map<String, Object> infos = new HashMap<>();
            infos.put(HEADER_KEY_TOPIC, nodeId);
            infos.put(HEADER_KEY_SENDER, sender);
            infos.put(HEADER_KEY_SIGN, sign);
            infos.put(HEADER_KEY_SEND_TIME, date);
            MessageHeaders headers = new MessageHeaders(infos, id);
            listener.onMessage(headers, content.getBytes(StandardCharsets.UTF_8));
        }

        @Override
        public String getTopic() {
            return this.topicName;
        }

        /**
         * 该方法覆盖 非常重要，避免{@link IMCCManager#addListener(IListener)}时重复添加监听器
         * @return boolean
         */
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            TopicMessageListenerDelegate other = (TopicMessageListenerDelegate) obj;
            return Objects.equals(sourceName, other.sourceName) &&
                    Objects.equals(topicName, other.topicName) &&
                    Objects.equals(listener.hashCode(), other.listener.hashCode());
        }

        /**
         * 该方法覆盖 非常重要，避免{@link IMCCManager#addListener(IListener)}时重复添加监听器
         * @return 监听器的hashCode
         */
        @Override
        public int hashCode() {
            return Objects.hash(sourceName, topicName, listener.hashCode());
        }
    }
}
