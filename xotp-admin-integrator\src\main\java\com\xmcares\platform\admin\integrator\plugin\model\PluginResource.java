package com.xmcares.platform.admin.integrator.plugin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

@ApiModel(description="xotp.bdp_meta_plugin_resource")
@TableName(value = "xotp.bdp_meta_plugin_resource")
public class PluginResource implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value="唯一标识")
    private String id;

    /**
     * 插件名称
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="插件名称")
    private String name;

    /**
     * 上传人
     */
    @TableField(value = "create_user")
    @ApiModelProperty(value="上传人")
    private String createUser;

    /**
     * 上传时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="上传时间")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_user")
    @ApiModelProperty(value="更新人")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
     * 资源上传后的路径
     */
    @TableField(value = "`path`")
    @ApiModelProperty(value="资源上传后的路径")
    private String path;

    /**
     * 资源描述
     */
    @TableField(value = "remark")
    @ApiModelProperty(value="资源描述")
    private String remark;

    @TableField(value = "properties")
    @ApiModelProperty(value="")
    private String properties;

    @TableField(value = "bytes")
    @ApiModelProperty(value="")
    private Integer bytes;

    /**
     * 获取唯一标识
     *
     * @return id - 唯一标识
     */
    public String getId() {
        return id;
    }

    /**
     * 设置唯一标识
     *
     * @param id 唯一标识
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取插件名称
     *
     * @return name - 插件名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置插件名称
     *
     * @param name 插件名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取上传人
     *
     * @return create_user - 上传人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置上传人
     *
     * @param createUser 上传人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取上传时间
     *
     * @return create_time - 上传时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置上传时间
     *
     * @param createTime 上传时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新人
     *
     * @return update_user - 更新人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新人
     *
     * @param updateUser 更新人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取资源上传后的路径
     *
     * @return path - 资源上传后的路径
     */
    public String getPath() {
        return path;
    }

    /**
     * 设置资源上传后的路径
     *
     * @param path 资源上传后的路径
     */
    public void setPath(String path) {
        this.path = path;
    }

    /**
     * 获取资源描述
     *
     * @return remark - 资源描述
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置资源描述
     *
     * @param remark 资源描述
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * @return properties
     */
    public String getProperties() {
        return properties;
    }

    /**
     * @param properties
     */
    public void setProperties(String properties) {
        this.properties = properties;
    }

    /**
     * @return bytes
     */
    public Integer getBytes() {
        return bytes;
    }

    /**
     * @param bytes
     */
    public void setBytes(Integer bytes) {
        this.bytes = bytes;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", path=").append(path);
        sb.append(", remark=").append(remark);
        sb.append(", properties=").append(properties);
        sb.append(", bytes=").append(bytes);
        sb.append("]");
        return sb.toString();
    }
}
