/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/23
 */
package com.xmcares.platform.admin.common.datasource.mq.rabbitmq;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public class RabbitMqProperties {
    /**
     * RabbitMQ 服务地址，例如：127.0.0.1
     */
    private String host = "127.0.0.1";

    /**
     * RabbitMQ 服务端口，默认 5672
     */
    private int port = 5672;

    /**
     * RabbitMQ 虚拟主机，默认 /
     */
    private String virtualHost = "/";

    /**
     * 登录用户名
     */
    private String username;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 交换机名称（默认为默认交换机 ""）
     */
    private String exchange = "";

    /**
     * 路由键（可选，默认使用 topic 作为 routingKey）
     */
    private String routingKey = "";


    private AdminProperties admin = new AdminProperties();

    public AdminProperties getAdmin() {
        return admin;
    }

    public void setAdmin(AdminProperties admin) {
        this.admin = admin;
    }
// getter & setter

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getVirtualHost() {
        return virtualHost;
    }

    public void setVirtualHost(String virtualHost) {
        this.virtualHost = virtualHost;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getExchange() {
        return exchange;
    }

    public void setExchange(String exchange) {
        this.exchange = exchange;
    }

    public String getRoutingKey() {
        return routingKey;
    }

    public void setRoutingKey(String routingKey) {
        this.routingKey = routingKey;
    }


    public static class AdminProperties {
        /**
         * RabbitMQ 管理端口，默认 http://127.0.0.1:15672
         */
        private String url = "http://127.0.0.1:15672";

        /**
         * 用户名
         */
        private String username;
        /**
         * 密码
         */
        private String password;

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }
}