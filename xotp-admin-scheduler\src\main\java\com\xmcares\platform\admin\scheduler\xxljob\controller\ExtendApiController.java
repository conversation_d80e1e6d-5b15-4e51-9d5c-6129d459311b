package com.xmcares.platform.admin.scheduler.xxljob.controller;

import com.alibaba.fastjson.JSONObject;
import com.xmcares.platform.admin.scheduler.xxljob.model.dto.JobLogPageListDto;
import com.xxl.job.admin.controller.annotation.PermissionLimit;
import com.xxl.job.admin.core.conf.XxlJobAdminConfig;
import com.xxl.job.admin.core.exception.XxlJobException;
import com.xxl.job.admin.core.model.XxlJobGroup;
import com.xxl.job.admin.core.model.XxlJobInfo;
import com.xxl.job.admin.core.model.XxlJobLog;
import com.xxl.job.admin.core.model.XxlJobUser;
import com.xxl.job.admin.core.thread.JobScheduleHelper;
import com.xxl.job.admin.core.thread.JobTriggerPoolHelper;
import com.xxl.job.admin.core.trigger.TriggerTypeEnum;
import com.xxl.job.admin.core.util.I18nUtil;
import com.xxl.job.admin.dao.XxlJobGroupDao;
import com.xxl.job.admin.dao.XxlJobInfoDao;
import com.xxl.job.admin.dao.XxlJobLogDao;
import com.xxl.job.admin.service.XxlJobService;
import com.xxl.job.core.biz.AdminBiz;
import com.xxl.job.core.biz.model.HandleCallbackParam;
import com.xxl.job.core.biz.model.RegistryParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.util.DateUtil;
import com.xxl.job.core.util.GsonTool;
import com.xxl.job.core.util.XxlJobRemotingUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * ExtendApiController
 *
 * <AUTHOR>
 * @Descriptions ExtendApiController
 * @Date 2025/5/19 10:39
 */
@Controller
@RequestMapping("/extend-api")
public class ExtendApiController {
    private static final Logger log = LoggerFactory.getLogger(ExtendApiController.class);
    @Resource
    private XxlJobService xxlJobService;

    @Resource
    private AdminBiz adminBiz;

    @Resource
    private XxlJobInfoDao xxlJobInfoDao;

    @Resource
    private XxlJobGroupDao xxlJobGroupDao;

    @Resource
    XxlJobLogDao xxlJobLogDao;

    @Resource
    private RestTemplate restTemplate;

    @Value("${xbdp.feign.hookbox-service.url:http://127.0.0.1:8082}")
    private String hookboxBaseUrl;

    /**
     * api
     *
     * @param uri
     * @param data
     * @return
     */
    @RequestMapping("/{uri}")
    @ResponseBody
    @PermissionLimit(limit = false)
    public ReturnT<String> api(HttpServletRequest request, @PathVariable("uri") String uri, @RequestBody(required = false) String data) {

        // valid
        if (!"POST".equalsIgnoreCase(request.getMethod())) {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "invalid request, HttpMethod not support.");
        }
        if (uri == null || uri.trim().length() == 0) {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "invalid request, uri-mapping empty.");
        }
        if (XxlJobAdminConfig.getAdminConfig().getAccessToken() != null
                && XxlJobAdminConfig.getAdminConfig().getAccessToken().trim().length() > 0
                && !XxlJobAdminConfig.getAdminConfig().getAccessToken().equals(request.getHeader(XxlJobRemotingUtil.XXL_JOB_ACCESS_TOKEN))) {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "The access token is wrong.");
        }

        // services mapping
        if ("callback".equals(uri)) {
            List<HandleCallbackParam> callbackParamList = GsonTool.fromJson(data, List.class, HandleCallbackParam.class);
            return adminBiz.callback(callbackParamList);
        } else if ("registry".equals(uri)) {
            RegistryParam registryParam = GsonTool.fromJson(data, RegistryParam.class);
            return adminBiz.registry(registryParam);
        } else if ("registryRemove".equals(uri)) {
            RegistryParam registryParam = GsonTool.fromJson(data, RegistryParam.class);
            return adminBiz.registryRemove(registryParam);
        }
        // 在现有的JobApiController中添加新方法
        // ========== job info start ==========
        else if ("loadJobInfo".equals(uri)) {
            int jobId = GsonTool.fromJson(data, Integer.class);
            XxlJobInfo jobInfo = xxlJobInfoDao.loadById(jobId);
            return new ReturnT<>(GsonTool.toJson(jobInfo));
        } else if ("loadJobGroupInfo".equals(uri)) {
            // id = 2，默认为 xotp-hookbox-integrator 钩盒-数据集成服务
            // id = 3，默认为 xotp-admin-integrator 管理-数据集成服务
            int jobId = GsonTool.fromJson(data, Integer.class);
            XxlJobGroup jobGroup = xxlJobGroupDao.load(jobId);
            return jobGroup != null ? new ReturnT<>(GsonTool.toJson(jobGroup)) : new ReturnT<>(ReturnT.FAIL_CODE, "get job group info fail, jobGroupId:" + jobId);
        } else if ("addJobInfo".equals(uri)) {
            XxlJobInfo xxlJobInfo = GsonTool.fromJson(data, XxlJobInfo.class);
            // 默认为超级管理员
            XxlJobUser loginUser = new XxlJobUser();
            loginUser.setId(1);
            loginUser.setUsername("admin");
            loginUser.setRole(1);
            // 这里会返回新增成功后的job id
            return xxlJobService.add(xxlJobInfo, loginUser);
        } else if ("triggerJob".equals(uri)) {
            XxlJobInfo xxlJobInfo = GsonTool.fromJson(data, XxlJobInfo.class);
            String executorParam = xxlJobInfo.getExecutorParam();
            // force cover job param
            if (executorParam == null) {
                executorParam = "";
            }
            JobTriggerPoolHelper.trigger(xxlJobInfo.getId(), TriggerTypeEnum.MANUAL, -1, null, executorParam, null);
            return ReturnT.SUCCESS;
        } else if ("startJob".equals(uri)) {
            int jobId = GsonTool.fromJson(data, Integer.class);
            return xxlJobService.start(jobId);
        } else if ("endJob".equals(uri)) {
            int jobId = GsonTool.fromJson(data, Integer.class);
            return xxlJobService.stop(jobId);
        } else if ("updateJob".equals(uri)) {
            XxlJobInfo xxlJobInfo = GsonTool.fromJson(data, XxlJobInfo.class);
            // 默认为超级管理员
            XxlJobUser loginUser = new XxlJobUser();
            loginUser.setId(1);
            loginUser.setUsername("admin");
            loginUser.setRole(1);
            return xxlJobService.update(xxlJobInfo, loginUser);
        } else if ("nextTriggerTime".equals(uri)) {
            String jsonResult = "";
            XxlJobInfo xxlJobInfo = GsonTool.fromJson(data, XxlJobInfo.class);
            List<String> result = new ArrayList<>();
            try {
                Date lastTime = new Date();
                for (int i = 0; i < 5; i++) {
                    lastTime = JobScheduleHelper.generateNextValidTime(xxlJobInfo, lastTime);
                    if (lastTime != null) {
                        result.add(DateUtil.formatDateTime(lastTime));
                    } else {
                        break;
                    }
                }
                jsonResult = GsonTool.toJson(result);
            } catch (Exception e) {
                return new ReturnT<>(ReturnT.FAIL_CODE, (I18nUtil.getString("schedule_type") + I18nUtil.getString("system_unvalid")) + e.getMessage());
            }
            return new ReturnT<>(jsonResult);

        } else if ("removeJob".equals(uri)) {
            int jobId = GsonTool.fromJson(data, Integer.class);
            return xxlJobService.remove(jobId);
        }
        // ========== job info end ==========

        // ========== job log start ==========
        else if ("jobLogPageList".equals(uri)) {
            JobLogPageListDto params = GsonTool.fromJson(data, JobLogPageListDto.class);
            Integer page = params.getPage();
            Integer rows = params.getRows();

            int start = (page - 1) * rows;
            int length = rows;

            Integer jobGroup = params.getJobGroup();
            int jobId = null != params.getJobId() ? params.getJobId() : 0;
            Integer logStatus = params.getLogStatus();
            String filterTime = params.getFilterTime();
            // String jobName = params.getJobName();

            Date triggerTimeStart = null;
            Date triggerTimeEnd = null;
            if (filterTime != null && !filterTime.trim().isEmpty()) {
                String[] temp = filterTime.split(" - ");
                if (temp.length == 2) {
                    triggerTimeStart = DateUtil.parseDateTime(temp[0]);
                    triggerTimeEnd = DateUtil.parseDateTime(temp[1]);
                }
            }

            // page query
            List<XxlJobLog> list = xxlJobLogDao.pageList(start, length, jobGroup, jobId, triggerTimeStart, triggerTimeEnd, logStatus);
            int listCount = xxlJobLogDao.pageListCount(start, length, jobGroup, jobId, triggerTimeStart, triggerTimeEnd, logStatus);

            // package result
            Map<String, Object> maps = new HashMap<String, Object>();
            // 总记录数
            maps.put("recordsTotal", listCount);
            // 过滤后的总记录数
            maps.put("recordsFiltered", listCount);
            // 分页列表
            maps.put("data", list);
            return new ReturnT<>(GsonTool.toJson(maps));
        } else if ("findAllJobGroup".equals(uri)) {
            // 获取执行器列表
            List<XxlJobGroup> jobGroupList = xxlJobGroupDao.findAll();
            if (jobGroupList == null || jobGroupList.isEmpty()) {
                throw new XxlJobException(I18nUtil.getString("jobgroup_empty"));
            }
            return new ReturnT<>(GsonTool.toJson(jobGroupList));
        } else if ("datasyncLog".equals(uri)) {
            String jobInstanceId = GsonTool.fromJson(data, String.class);
            if (StringUtils.isEmpty(jobInstanceId)) {
                throw new XxlJobException("传入 jobInstanceId 为空");
            }

            String url = hookboxBaseUrl + "/hookbox/datasync/log/get-logs/" + jobInstanceId;
            log.info("Forwarding request to data sync log service: {}", url);

            try {
                ResponseEntity<JSONObject> responseEntity = restTemplate.exchange(url, HttpMethod.POST, null, JSONObject.class);
                if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
                    JSONObject responseBody = responseEntity.getBody();
                    return new ReturnT<>(responseBody.toJSONString());
                } else {
                    // Handle non-successful responses from the service
                    log.error("Failed to get data sync log for jobInstanceId: {}. Status: {}, Body: {}",
                            jobInstanceId, responseEntity.getStatusCode(), responseEntity.getBody());
                    return new ReturnT<>(ReturnT.FAIL_CODE, "Failed to retrieve logs. Service responded with status: " + responseEntity.getStatusCode());
                }
            } catch (Exception e) {
                log.error("Error calling data sync log service for jobInstanceId: {}", jobInstanceId, e);
                throw new XxlJobException("Error occurred while fetching data sync log: " + e.getMessage());
            }
        }
        else if("pageDatasyncLog".equals(uri)) {
            try {
                // 1. 使用GsonTool解析传入的JSON字符串数据
                Map<String, Object> params = GsonTool.fromJson(data, Map.class);

                // 2. 提取分页参数，并设置默认值
                int current = params.get("current") != null ? ((Number) params.get("current")).intValue() : 1;
                int size = params.get("size") != null ? ((Number) params.get("size")).intValue() : 10;

                // 3. 提取查询DTO对象。它此时是一个Map，需要转回JSON字符串以便转发
                Object queryDTOObject = params.get("queryDTO");
                String queryDTOJson = (queryDTOObject != null) ? GsonTool.toJson(queryDTOObject) : "{}";

                // 4. 构建目标服务的URL，将分页参数作为URL的Query Parameters
                String url = hookboxBaseUrl + "/hookbox/datasync/log/page-query?current=" + current + "&size=" + size;
                log.info("Forwarding paginated log request to: {}", url);

                // 5. 准备转发请求的HttpEntity，设置Header和Body
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                HttpEntity<String> requestEntity = new HttpEntity<>(queryDTOJson, headers);

                // 6. 使用RestTemplate发起POST请求
                ResponseEntity<JSONObject> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, JSONObject.class);

                // 7. 处理来自目标服务的响应
                if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
                    return new ReturnT<>(JSONObject.toJSONString(responseEntity.getBody()));
                } else {
                    log.error("Failed to get paginated data sync log. Status: {}, Body: {}",
                            responseEntity.getStatusCode(), responseEntity.getBody());
                    return new ReturnT<>(ReturnT.FAIL_CODE, "Failed to retrieve paginated logs. Service responded with status: " + responseEntity.getStatusCode());
                }
            } catch (Exception e) {
                log.error("Error calling data sync log page query service", e);
                throw new XxlJobException("Error occurred while fetching paginated data sync log: " + e.getMessage());
            }
        }
        // ========== job log end ==========
        else {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "invalid request, uri-mapping(" + uri + ") not found.");
        }

    }

}
