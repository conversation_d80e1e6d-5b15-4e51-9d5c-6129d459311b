/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： zhanlh
 * Date：2022/6/20
 */
package com.xmcares.platform.admin.developer.dataflow.task;

import com.xmcares.platform.admin.developer.dataflow.model.DevDataflow;
import com.xmcares.platform.admin.developer.dataflow.model.DevDataflowProcess;
import com.xmcares.platform.admin.developer.dataflow.service.DevDataflowDefinitionService;
import com.xmcares.platform.admin.developer.dataflow.service.DevDataflowInstanceService;
import com.xmcares.platform.admin.developer.dataflow.service.DevDataflowResourceService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 3.0.3
 */
@Component
public class RemoveFlowDatasyncCronTask {
    private static final Logger LOG = LoggerFactory.getLogger("DEV_CRON_CLEAR");
    @Autowired
    private DevDataflowDefinitionService devDataflowDefinitionService;
    @Autowired
    private DevDataflowInstanceService devDataflowInstanceService;
    @Autowired
    private DevDataflowResourceService devDataflowResourceService;
    /**
     * 自动删除数据同步定义信息
     * 每隔1个小时执行一次
     * 1. 因为 数据定义表是一个小表， 数据量不大（撑死1000个！）， 所以采用全表分页查的方式获取需要删除的数据
     * 2. 在删除数据的时候，  是以遍历的方式去删除定义下的实例的， 能删一个是一个，  所以不用为整个定义打开事务， 但需要为实例打开事务
     */
    @Scheduled(cron = "${xbdp.task.developer.clear.cron}")
    public void removeDatasync() {
        LOG.info("任务开始执行...");
        List<DevDataflow> removes = devDataflowDefinitionService.queryRemoves();
        if (CollectionUtils.isNotEmpty(removes)) {
            this.execute(removes);
        }
        LOG.info("本次任务执行完成...");
    }

    private void execute(List<DevDataflow> removes) {
        // 2. 遍历需要删除的数据
        for (DevDataflow devDataflow : removes) {

            LOG.info("正在清理已删除的任务【{}-{}】", devDataflow.getId(), devDataflow.getName());
            try {
                // 2.0. 休眠20毫秒
                Thread.sleep(20);
                // 2.1. 删除定义所属的资源
                this.devDataflowResourceService.removeByDataflowId(devDataflow.getId());
            } catch (Exception e) {
                LOG.error("删除定义【{}】的资源，异常", devDataflow.getName(), e);
                continue;
            }
            // 2.2. 删除实例
            boolean processRemoveSuccess = true;
            List<DevDataflowProcess> dataflowProcessList = this.devDataflowInstanceService.queryByDataflowId(devDataflow.getId());
            if (CollectionUtils.isNotEmpty(dataflowProcessList)) {
                // 2.2.1. 遍历需要删除的实例
                for (DevDataflowProcess devDataflowProcess : dataflowProcessList) {
                    LOG.info("正在清理任务【{}】下的实例【{}-{}】", devDataflow.getName(), devDataflowProcess.getId(), devDataflow.getName());
                    try {
                        // 2.0. 休眠10毫秒
                        Thread.sleep(10);
                        this.devDataflowInstanceService.instanceDelete(devDataflow.getId());
                        LOG.info("清理任务【{}】下的实例【{}-{}】完成...", devDataflow.getName(), devDataflowProcess.getId(), devDataflow.getName());
                    } catch (Exception e) {
                        LOG.error("清理任务【{}】下的实例【{}-{}】异常...", devDataflow.getName(), devDataflowProcess.getId(), devDataflow.getName());
                        LOG.error("清理实例【{}】异常", devDataflowProcess.getId(), e);
                        processRemoveSuccess = false;
                    }
                }
            }
            // 2.3. 删除定义
            if (processRemoveSuccess) {
                try {
                    this.devDataflowDefinitionService.realDelete(devDataflow.getId());
                    LOG.info("清理已删除的任务【{}-{}】完成", devDataflow.getId(), devDataflow.getName());
                } catch (Exception e) {
                    LOG.error("清理已删除的任务【{}-{}】异常", devDataflow.getId(), devDataflow.getName(), e);
                }
            } else {
                LOG.warn("由于在清理实例时存在异常，所以未能删除【{}-{}】，等待下一次执行", devDataflow.getId(), devDataflow.getName());
            }
        }
    }

}
