package com.xmcares.platform.admin.metadata;

import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.fsclient.FSTemplate;
import com.xmcares.platform.admin.common.database.DatabaseMetaQueryFactory;
import com.xmcares.platform.admin.common.datasource.jdbc.JdbcDataSourceManager;
import com.xmcares.platform.admin.common.datasource.mq.MqDataSourceManager;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

import javax.sql.DataSource;

/**
 * 平台管理中心 > 元数据（管理）模块的配置类
 * <AUTHOR>
 * @since 1.0.0
 */
@EnableConfigurationProperties({MetadataProperties.class})
@ComponentScan(basePackages ={"com.xmcares.platform.admin.metadata"})
public class AdminMetadataConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public XcfJdbcTemplate xcfJdbcTemplate(DataSource dataSource) {
        return new XcfJdbcTemplate(dataSource);
    }

    @Bean(name = "jdbcDataSourceManager")
    @ConditionalOnMissingBean
    public JdbcDataSourceManager jdbcDataSourceManager(FSTemplate fsTemplate) {
        return new JdbcDataSourceManager(fsTemplate);
    }

    @Bean(name = "mqDataSourceManager")
    @ConditionalOnMissingBean
    public MqDataSourceManager mqDataSourceManager() {
        return new MqDataSourceManager();
    }

    @Bean
    @ConditionalOnMissingBean
    public DatabaseMetaQueryFactory databaseMetaQueryFactory(JdbcDataSourceManager jdbcDataSourceManager) {
        return new DatabaseMetaQueryFactory(jdbcDataSourceManager);
    }

}
