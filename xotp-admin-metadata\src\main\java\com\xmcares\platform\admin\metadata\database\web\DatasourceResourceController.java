package com.xmcares.platform.admin.metadata.database.web;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.platform.admin.metadata.database.model.DatasourceResource;
import com.xmcares.platform.admin.metadata.database.service.DatasourceResourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/9/21 16:10
 */
@Api(value = "数据源资源管理控制器")
@Validated
@RestController
@RequestMapping("/datasource-resource")
public class DatasourceResourceController {
    private static final Logger LOG = LoggerFactory.getLogger(DatasourceResourceController.class);
    @Autowired
    DatasourceResourceService datasourceResourceService;

    /*@PostConstruct
    public void init(){
        LOG.warn("start initResourceSynTaskScheduler ====================================");
        datasourceResourceService.initResourceSynTaskScheduler();
        LOG.warn("end initResourceSynTaskScheduler ====================================");
    }*/

    @ApiOperation("资源分页展示")
    @GetMapping("/list-query")
    @ResponseBody
    public Page<DatasourceResource> fileList(
            String name,
            @RequestParam(name = "page", defaultValue = "1") Integer page,
            @RequestParam(name = "rows", defaultValue = "1") Integer rows
    ) {
        Pagination pagination = new Pagination();
        pagination.setPageNo(page!=null ? page : 0);
        pagination.setPageSize(rows!=null ? rows : 100);
        return datasourceResourceService.fileList(name, pagination);
    }

    @ApiOperation("上传文件资源")
    @PostMapping(value = "/upload", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public String uploadFile(
            @RequestParam(name = "name") String name,
            String remark,
            MultipartFile uploadFile
    ) throws Exception {
        return datasourceResourceService.uploadFile(name, remark, uploadFile);
    }

    @ApiOperation("删除文件资源")
    @GetMapping("/delete")
    @ResponseBody
    public Boolean deleteFile(
            @RequestParam(name = "resourceId") String resourceId
    ) throws Exception {
        return datasourceResourceService.deleteFile(resourceId);
    }

    @ApiOperation("更新文件资源信息")
    @GetMapping("/upadte")
    @ResponseBody
    public Boolean updateResource(
            DatasourceResource datasourceResource
    ) {
        return datasourceResourceService.updateResource(datasourceResource);
    }
}
