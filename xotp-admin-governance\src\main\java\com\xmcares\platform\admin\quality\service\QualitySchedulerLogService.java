package com.xmcares.platform.admin.quality.service;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.platform.admin.quality.model.QltyRuleScheduler;
import com.xmcares.platform.admin.quality.model.XxlJobLog;
import com.xmcares.platform.admin.quality.model.vo.QualitySchedulerLogListQueryVO;
import com.xmcares.platform.admin.quality.repository.QualitySchedulerLogRepostitory;
import com.xmcares.platform.admin.quality.repository.XbdpQltyController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/25 09:10
 */
@Service
public class QualitySchedulerLogService {
    private static final Logger LOG = LoggerFactory.getLogger(QualitySchedulerLogService.class);
    @Autowired
    QualitySchedulerLogRepostitory qualitySchedulerLogRepostitory;

    @Resource
    XbdpQltyController xbdpQltyController;
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");

    public Page<QualitySchedulerLogListQueryVO> listQuery(String datawareId, String datatableId, String beginTime, String endTime, Integer pageNo, Integer pageSize) {
        Page<QltyRuleScheduler> qltyRuleSchedulerPage = qualitySchedulerLogRepostitory.queryDatatableSchedulers(datawareId, datatableId, pageNo, pageSize);

        try {

            ArrayList<QualitySchedulerLogListQueryVO> qualitySchedulerLogListQueryVOS = new ArrayList<>();


            for (QltyRuleScheduler qltyRuleScheduler : qltyRuleSchedulerPage.getData()) {

                List<XxlJobLog> jobLogs = xbdpQltyController.findJobLogByJobId(String.valueOf(qltyRuleScheduler.getDispatch_id()), beginTime, endTime, 0, 1);


                QualitySchedulerLogListQueryVO qualitySchedulerLogListQueryVO = new QualitySchedulerLogListQueryVO();

                qualitySchedulerLogListQueryVO.setScheduleId(qltyRuleScheduler.getId());
                qualitySchedulerLogListQueryVO.setScheduleName(qltyRuleScheduler.getName());
                qualitySchedulerLogListQueryVO.setScheduleState(qltyRuleScheduler.getStarted());
                if (jobLogs.size() == 1) {
                    XxlJobLog xxlJobLog = jobLogs.get(0);
                    qualitySchedulerLogListQueryVO.setScheduleStartTime(xxlJobLog.getTriggerTime());
                    qualitySchedulerLogListQueryVO.setScheduleEndTime(xxlJobLog.getHandleTime());
                    qualitySchedulerLogListQueryVO.setRuleExecuteInfo(xxlJobLog.getTriggerMsg());
                    qualitySchedulerLogListQueryVO.setScheudleInfo(xxlJobLog.getTriggerMsg());
                }

                qualitySchedulerLogListQueryVOS.add(qualitySchedulerLogListQueryVO);
            }

            Page<QualitySchedulerLogListQueryVO> qualitySchedulerLogListQueryVOPage = new Page<QualitySchedulerLogListQueryVO>();
            qualitySchedulerLogListQueryVOPage.setData(qualitySchedulerLogListQueryVOS);
            qualitySchedulerLogListQueryVOPage.setPageNo(pageNo);
            qualitySchedulerLogListQueryVOPage.setPageSize(pageSize);
            qualitySchedulerLogListQueryVOPage.setTotal(qltyRuleSchedulerPage.getTotal());
            return qualitySchedulerLogListQueryVOPage;
        } catch (Exception e) {
            LOG.warn("listQuery", e);
        }
        return null;
    }

    public String get(String scheduleId, String metricId) {
        QltyRuleScheduler qltyRuleScheduler = qualitySchedulerLogRepostitory.queryDatatableScheduler(scheduleId);

        try {
            String deftStartTriggerTime = "0000-01-01 00:00:00";
            List<XxlJobLog> jobLogs = xbdpQltyController.findJobLogByJobId(String.valueOf(qltyRuleScheduler.getDispatch_id()), qltyRuleScheduler.getCreate_time() != null ? qltyRuleScheduler.getCreate_time() : deftStartTriggerTime, "9999-01-01 00:00:00", 0, 1);
            if (jobLogs != null && jobLogs.size() == 1) {
                XxlJobLog xxlJobLog = jobLogs.get(0);
                return xbdpQltyController.logDetailPage(xxlJobLog.getExecutorAddress(), xxlJobLog.getTriggerTime().getTime(), xxlJobLog.getId(), 1);
            }
        } catch (Exception e) {
            LOG.warn("get", e);
        }
        return null;
    }
}
