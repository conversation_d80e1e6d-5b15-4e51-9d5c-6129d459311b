/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/1/26
 */
package com.xmcares.platform.admin.metadata.database.web;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.platform.admin.common.validation.Insert;
import com.xmcares.platform.admin.common.validation.Update;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import com.xmcares.platform.admin.metadata.database.model.DatasourceModel;
import com.xmcares.platform.admin.metadata.database.service.DatasourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 元数据之元数据源（描述数据源的数据称元数据源）控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/datasource")
public class DatasourceController {

    @Autowired
    DatasourceService datasourceService;

    @PostMapping("/add")
    @ResponseBody
    public Boolean addDatasource(@RequestBody @Validated({Insert.class}) Datasource datasource) {
        datasourceService.add(datasource);
        return true;
    }

    @GetMapping("/delete")
    @ResponseBody
    public Boolean removeDatasource(String id) {
        datasourceService.delete(id);
        return true;
    }

    @PostMapping("/batch-delete")
    public Boolean deleteBatch(@RequestBody List<String> ids) {
        datasourceService.deleteBatch(ids);
        return true;
    }

    @PostMapping("/update")
    @ResponseBody
    public Boolean updateDatasource(@RequestBody @Validated({Update.class}) Datasource datasource) {
        datasourceService.update(datasource);
        return true;
    }

    @PostMapping("/list-query")
    @ResponseBody
    public List<Datasource> listDatasource(Datasource datasource) {
        return datasourceService.listDatasource(datasource);
    }

    @PostMapping("/ids/list")
    @ResponseBody
    public List<Datasource> listDatasource(@RequestBody List<String> ids) {
        return datasourceService.listDatasource(ids);
    }

    @GetMapping("/page-query")
    @ResponseBody
    public Page<Datasource> pageListDatasource(Datasource datasource, Integer page, Integer rows) {
        Pagination pagination = new Pagination();
        pagination.setPageNo(page!=null ? page : 1);
        pagination.setPageSize(rows!=null ? rows : 10);
        return datasourceService.pageListDatasource(datasource, pagination);
    }


    @PostMapping("/test")
    @ResponseBody
    public Boolean testDatasource(@RequestBody @Validated({Update.class}) Datasource datasource) {
        return datasourceService.testDatasource2(datasource);
    }

    @GetMapping("/get")
    public Datasource getDatasource(@NotBlank String id) {
        return datasourceService.getDatasource(id);
    }



    @GetMapping("/model/get")
    public DatasourceModel getByDatasourceId(@NotBlank String id) {
        return datasourceService.getModelById(id);
    }

    @GetMapping("/template/get")
    public String getTemplate(@NotBlank String id) {
        return datasourceService.getTemplate(id);
    }


}
