package com.xmcares.platform.admin.developer.dataflow.vo;



import com.xmcares.platform.admin.developer.dataflow.core.node.IGroupDAGNode;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class DevDataflowNodeVo implements IGroupDAGNode, Serializable {

    public static class DevDataflowNodeParam implements Serializable {

        /** 参数Key */
        private String key;
        /** 参数Value */
        private Object value;

        public static DevDataflowNodeParam createFrom(String key, Object value) {
            DevDataflowNodeParam result = new DevDataflowNodeParam();
            result.key = key;
            result.value = value;
            return result;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public Object getValue() {
            return value;
        }

        public void setValue(Object value) {
            this.value = value;
        }
    }

    public static DevDataflowNodeParam createParam(String key, Object value) {
        DevDataflowNodeParam result = new DevDataflowNodeParam();
        result.setKey(key);
        result.setValue(value);
        return result;
    }

    /** 任务ID */
    private String id;
    /** 任务名称 */
    private String label;
    /** 任务类型 */
    private String type;
    /** 节点所处的组ID */
    private String groupId;
    /** 节点的前置ID列表 */
    private List<String> preIds;
    /** 节点的后置ID列表 */
    private List<String> postIds;
    /** 节点的参数列表 */
    private List<DevDataflowNodeParam> params;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public List<String> getPreIds() {
        return preIds;
    }

    public void setPreIds(List<String> preIds) {
        this.preIds = preIds;
    }

    public List<String> getPostIds() {
        return postIds;
    }

    public void setPostIds(List<String> postIds) {
        this.postIds = postIds;
    }

    public List<DevDataflowNodeParam> getParams() {
        return params;
    }

    public void setParams(List<DevDataflowNodeParam> params) {
        this.params = params;
    }

    @Override
    public String nodeId() {
        return id;
    }
    @Override
    public Set<String> nextNodes() {
        if (postIds != null) {
            return new HashSet<>(postIds);
        } else {
            return null;
        }

    }
    @Override
    public String groupId() { return groupId; }
    @Override
    public String name() { return label; }
}
