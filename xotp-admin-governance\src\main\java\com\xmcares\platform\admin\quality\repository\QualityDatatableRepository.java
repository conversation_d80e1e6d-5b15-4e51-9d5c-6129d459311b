package com.xmcares.platform.admin.quality.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.common.vo.ReturnT;
import com.xmcares.platform.admin.asset.model.MetaDatasource;
import com.xmcares.platform.admin.asset.model.MetaDatatable;
import com.xmcares.platform.admin.asset.model.MetaDataware;
import com.xmcares.platform.admin.quality.model.*;
import com.xmcares.platform.admin.quality.model.vo.QualityDatatableListQueryVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/30 10:16
 */
@Repository
public class QualityDatatableRepository {

    private static final Logger LOG = LoggerFactory.getLogger(QualityDatatableRepository.class);
    public static final String TABLE_BDP_QLTY_RULE = "bdp_qlty_rule";
    public static final String TABLE_BDP_QLTY_RULE_SCHEDULER = "bdp_qlty_rule_scheduler";
    public static final String TABLE_BDP_META_DATATABLE = "bdp_meta_datatable";
    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    @Resource
    XbdpQltyController xbdpQltyController;

    @Value("${xbdp.qlty.flink.jar}")
    String qltyFlinkJar;

    @Resource
    QltyMetricsController qltyMetricsController;

    public Page<QualityDatatableListQueryVO> listQuery(String datawareId, String datatableId, String datatableAlias, Integer pageNo, Integer pageSize) {
        try {

            List<QualityDatatableListQueryVO> qualityDatatableListQueryVOS = xcfJdbcTemplate.<QualityDatatableListQueryVO>queryForEntities(
                    " select * from ( " +
                            " select " +
                            TABLE_BDP_QLTY_RULE_SCHEDULER + ".dataware_id datawareId, " +
                            TABLE_BDP_QLTY_RULE_SCHEDULER + ".datatable_id datatableId, " +
                            TABLE_BDP_QLTY_RULE + ".datatable_name datatableName, " +
                            TABLE_BDP_META_DATATABLE + ".alias datatableAlias, " +
                            MetaDataware.TABLE + ".name datawareName, " +
                            " count(" + TABLE_BDP_QLTY_RULE + ".id) ruleCount, " +
                            TABLE_BDP_QLTY_RULE_SCHEDULER + ".type schedulerTactics, " +
                            TABLE_BDP_QLTY_RULE_SCHEDULER + ".started schedulerState " +
                            " from " + TABLE_BDP_QLTY_RULE_SCHEDULER +
                            " left join " + TABLE_BDP_QLTY_RULE + "  on " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".dataware_id=" + TABLE_BDP_QLTY_RULE + ".dataware_id and " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".datatable_id=" + TABLE_BDP_QLTY_RULE + ".datatable_id " +
                            " left join " + MetaDataware.TABLE + "  on " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".dataware_id=" + MetaDataware.TABLE + ".id " +
                            " left join " + TABLE_BDP_META_DATATABLE + "  on " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".datatable_id=" + TABLE_BDP_META_DATATABLE + ".id " +
                            " where true and  " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".dataware_id != '-1' " +
                            ((datawareId != null && !"".equals(datawareId)) ? (" and " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".dataware_id='" + datawareId + "'  ") : "  ") +
                            ((datatableId != null && !"".equals(datatableId)) ? (" and " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".datatable_id='" + datatableId + "'  ") : "  ") +
                            ((datatableAlias != null && !"".equals(datatableAlias)) ? (" and " + TABLE_BDP_META_DATATABLE + ".name like '%" + datatableAlias + "%' ") : " ") +
                            " group by " +
                            " " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".dataware_id," +
                            " " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".datatable_id  " +
                            " ) t " +
                            " limit  " + ((pageNo - 1) * pageSize) + "," + pageSize
                    ,
                    new Object[]{},
                    QualityDatatableListQueryVO.class
            );
            Integer total = xcfJdbcTemplate.<Integer>queryForObject(
                    " select " +
                            " IFNULL(( " +
                            " select " +
                            " count(*) total " +
                            " from ( " +
                            " select " +
                            TABLE_BDP_QLTY_RULE_SCHEDULER + ".dataware_id datawareId, " +
                            TABLE_BDP_QLTY_RULE_SCHEDULER + ".datatable_id datatableId, " +
                            TABLE_BDP_QLTY_RULE + ".datatable_name datatableName, " +
                            TABLE_BDP_META_DATATABLE + ".alias datatableAlias, " +
                            MetaDataware.TABLE + ".name datawareName, " +
                            " count(" + TABLE_BDP_QLTY_RULE + ".id) ruleCount, " +
                            TABLE_BDP_QLTY_RULE_SCHEDULER + ".type schedulerTactics, " +
                            TABLE_BDP_QLTY_RULE_SCHEDULER + ".started schedulerState " +
                            " from " + TABLE_BDP_QLTY_RULE_SCHEDULER +
                            " left join " + TABLE_BDP_QLTY_RULE + "  on " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".dataware_id=" + TABLE_BDP_QLTY_RULE + ".dataware_id and " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".datatable_id=" + TABLE_BDP_QLTY_RULE + ".datatable_id " +
                            " left join " + MetaDataware.TABLE + "  on " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".dataware_id=" + MetaDataware.TABLE + ".id " +
                            " left join " + TABLE_BDP_META_DATATABLE + "  on " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".datatable_id=" + TABLE_BDP_META_DATATABLE + ".id " +
                            " where true and  " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".dataware_id != '-1' " +
                            ((datawareId != null && !"".equals(datawareId)) ? (" and " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".dataware_id='" + datawareId + "'  ") : "  ") +
                            ((datatableId != null && !"".equals(datatableId)) ? (" and " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".datatable_id='" + datatableId + "'  ") : "  ") +
                            ((datatableAlias != null && !"".equals(datatableAlias)) ? (" and " + TABLE_BDP_META_DATATABLE + ".name like '%" + datatableAlias + "%' ") : " ") +
                            " group by " +
                            " " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".dataware_id," +
                            " " + TABLE_BDP_QLTY_RULE_SCHEDULER + ".datatable_id  " +
                            " ) t " +
                            " ),0 ) total"
                    ,
                    Integer.class,
                    new Object[]{}
            );

            Page<QualityDatatableListQueryVO> qualityDatatableListQueryVOPage = new Page<QualityDatatableListQueryVO>();

            qualityDatatableListQueryVOPage.setData(qualityDatatableListQueryVOS);
            qualityDatatableListQueryVOPage.setPageNo(pageNo);
            qualityDatatableListQueryVOPage.setPageSize(pageSize);
            qualityDatatableListQueryVOPage.setTotal(total);
            return qualityDatatableListQueryVOPage;
        } catch (Exception e) {
            LOG.info("listQuery", e);
            return null;
        }
    }

    public Page<QltyRule> ruleListQuery(String datatableId, String ruleName, String ruleLevel, String dimCode, Integer pageNo, Integer pageSize) {
        try {

            List<QltyRule> qltyRules = xcfJdbcTemplate.<QltyRule>queryForEntities(
                    " select " +
                            " " + TABLE_BDP_QLTY_RULE + ".id id, " +
                            " " + TABLE_BDP_QLTY_RULE + ".dataware_id dataware_id, " +
                            " " + TABLE_BDP_QLTY_RULE + ".datatable_id datatable_id, " +
                            " " + TABLE_BDP_QLTY_RULE + ".datatable_name datatable_name, " +
                            " " + TABLE_BDP_META_DATATABLE + ".alias datatable_name_alias, " +
                            " " + TABLE_BDP_QLTY_RULE + ".rule_name rule_name, " +
                            " " + TABLE_BDP_QLTY_RULE + ".rule_type rule_type, " +
                            " " + TABLE_BDP_QLTY_RULE + ".rule_level rule_level, " +
                            " " + TABLE_BDP_QLTY_RULE + ".rule_tmpl_id rule_tmpl_id, " +
                            " " + TABLE_BDP_QLTY_RULE + ".rule_targets rule_targets, " +
                            " " + TABLE_BDP_QLTY_RULE + ".rule_options rule_options, " +
                            " " + TABLE_BDP_QLTY_RULE + ".rule_sql_expr rule_sql_expr, " +
                            " " + TABLE_BDP_QLTY_RULE + ".rule_range_expr rule_range_expr, " +
                            " " + TABLE_BDP_QLTY_RULE + ".dim_code dim_code, " +
                            " " + TABLE_BDP_QLTY_RULE + ".check_type check_type, " +
                            " " + TABLE_BDP_QLTY_RULE + ".check_options check_options, " +
                            " " + TABLE_BDP_QLTY_RULE + ".remark remark, " +
                            " " + TABLE_BDP_QLTY_RULE + ".disabled disabled, " +
                            " " + TABLE_BDP_QLTY_RULE + ".create_user create_user, " +
                            " " + TABLE_BDP_QLTY_RULE + ".create_time create_time, " +
                            " " + TABLE_BDP_QLTY_RULE + ".update_user update_user, " +
                            " " + TABLE_BDP_QLTY_RULE + ".update_time update_time " +
                            " from " + TABLE_BDP_QLTY_RULE +
                            " left join " + TABLE_BDP_META_DATATABLE + "  on " + TABLE_BDP_QLTY_RULE + ".datatable_id=" + TABLE_BDP_META_DATATABLE + ".id " +
                            " where true and " + TABLE_BDP_QLTY_RULE + ".datatable_id != '-1' " +
                            ((datatableId != null && !"".equals(datatableId)) ? " and  " + TABLE_BDP_QLTY_RULE + ".datatable_id='" + datatableId + "' " : "  ") +
                            ((ruleName != null && !"".equals(ruleName)) ? " and " + TABLE_BDP_QLTY_RULE + ".rule_name like '%" + ruleName + "%' " : "  ") +
                            ((ruleLevel != null && !"".equals(ruleLevel)) ? (" and " + TABLE_BDP_QLTY_RULE + ".rule_level ='" + ruleLevel + "' ") : "  ") +
                            ((dimCode != null && !"".equals(dimCode)) ? (" and " + TABLE_BDP_QLTY_RULE + ".dim_code ='" + dimCode + "' ") : "  ") +
                            " limit  " + ((pageNo - 1) * pageSize) + "," + pageSize,
                    new Object[]{},
                    QltyRule.class
            );
            Integer total = xcfJdbcTemplate.<Integer>queryForObject(
                    "SELECT " +
                            " IFNULL(( " +
                            " select " +
                            " count(*) all_count " +
                            " from " + TABLE_BDP_QLTY_RULE +
                            " left join " + TABLE_BDP_META_DATATABLE + "  on " + TABLE_BDP_QLTY_RULE + ".datatable_id=" + TABLE_BDP_META_DATATABLE + ".id " +
                            " where true and " + TABLE_BDP_QLTY_RULE + ".datatable_id != '-1' " +
                            ((datatableId != null && !"".equals(datatableId)) ? " and  " + TABLE_BDP_QLTY_RULE + ".datatable_id='" + datatableId + "' " : "  ") +
                            ((ruleName != null && !"".equals(ruleName)) ? " and " + TABLE_BDP_QLTY_RULE + ".rule_name like '%" + ruleName + "%' " : "  ") +
                            ((ruleLevel != null && !"".equals(ruleLevel)) ? (" and " + TABLE_BDP_QLTY_RULE + ".rule_level ='" + ruleLevel + "' ") : "  ") +
                            ((dimCode != null && !"".equals(dimCode)) ? (" and " + TABLE_BDP_QLTY_RULE + ".dim_code ='" + dimCode + "' ") : "  ") +
                            " ),0 ) all_count "
                    ,
                    Integer.class,
                    new Object[]{}
            );
            Page<QltyRule> qltyRulePage = new Page<QltyRule>();
            qltyRulePage.setData(qltyRules);
            qltyRulePage.setPageNo(pageNo);
            qltyRulePage.setPageSize(pageSize);
            qltyRulePage.setTotal(total);
            return qltyRulePage;
        } catch (Exception e) {
            LOG.info("ruleListQuery", e);
            return null;
        }
    }

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");

    public String ruleAdd(QltyRule qltyRule) {

        try {
            String idStr = SnowflakeGenerator.getNextId().toString();
            qltyRule.setRule_targets(qltyRule.getRule_targets().replace("::", ":"));
            qltyRule.setCreate_user(qltyRule.getCreate_user() == null ? "xbdp" : qltyRule.getCreate_user());
            qltyRule.setCreate_time(qltyRule.getCreate_time() == null ? sdf.format(new Date()) : qltyRule.getCreate_time());
            qltyRule.setDisabled(qltyRule.getDisabled() == null ? "0" : qltyRule.getDisabled());
            qltyRule.setRule_type(qltyRule.getRule_tmpl_id() != null && "1000".equals(qltyRule.getRule_tmpl_id()) ? "SQL" : "TMPL");
            qltyRule.setId(idStr);
            Map<String, Object> maps = DBUtils.insertSqlAndObjects(qltyRule, QltyRule.class, TABLE_BDP_QLTY_RULE);
            String sql = DBUtils.getSql(maps);
            Object[] objs = DBUtils.getObjects(maps);
            return xcfJdbcTemplate.update(sql, objs) == 1 ? "true" : "false";
        } catch (Exception e) {
            LOG.info("ruleAdd", e);
            return "false";
        }
    }

    public String schedulerSave(QltyRuleScheduler qltyRuleScheduler) {
        try {
            if ("CRON".equals(qltyRuleScheduler.getType())) {

                List<QltyRuleScheduler> qltyRuleSchedulers = xcfJdbcTemplate.<QltyRuleScheduler>queryForEntities(
                        " select * from  " +
                                TABLE_BDP_QLTY_RULE_SCHEDULER +
                                " where dataware_id ='" + qltyRuleScheduler.getDataware_id() + "' and " +
                                " datatable_id ='" + qltyRuleScheduler.getDatatable_id() + "' "
                        ,
                        new Object[]{},
                        QltyRuleScheduler.class
                );

                MetaDataware metaDataware = xcfJdbcTemplate.<MetaDataware>queryForEntity(
                        " select * from  " +
                                MetaDataware.getTABLE() +
                                " where id ='" + qltyRuleScheduler.getDataware_id() + "' "
                        ,
                        new Object[]{},
                        MetaDataware.class
                );
                JSONObject executeParamsJson = JSON.parseObject(metaDataware.getDatasource_options());
                //TODO 这里暂时定位打包的位置，如需更改需要更改POM中存储压缩包的位置
                executeParamsJson.put("resource.path", qltyFlinkJar);

                JSONObject businessJsonNode = JSON.parseObject("{}");
                businessJsonNode.put("datatableId", qltyRuleScheduler.getDatatable_id());
                businessJsonNode.put("datawareId", qltyRuleScheduler.getDataware_id());
                businessJsonNode.put("jobName", SnowflakeGenerator.getNextId());
                businessJsonNode.put("testing", false);



                int count = 0;
                if (qltyRuleScheduler.getId() == null && qltyRuleSchedulers.size() == 0) {

                    qltyRuleScheduler.setCreate_time(sdf.format(new Date()));
                    qltyRuleScheduler.setId(SnowflakeGenerator.getNextId().toString());
                    businessJsonNode.put("jobId", qltyRuleScheduler.getId());
                    executeParamsJson.put("business", businessJsonNode);
                    XxlJobInfo xxlJobInfo = new XxlJobInfo();

                    executeParamsJson.put("QltyRuleSchedulerId", qltyRuleScheduler.getId());
                    qltyRuleScheduler.setExecute_params(executeParamsJson.toJSONString());
                    qltyRuleScheduler.setCall_back_params("{}");


                    XxlJobGroup xbdp_qlty_executor = xbdpQltyController.findByAppname("hookbox-service");

                    xxlJobInfo.setJobGroup(xbdp_qlty_executor.getId());
                    xxlJobInfo.setExecutorParam(qltyRuleScheduler.getExecute_params());
                    xxlJobInfo.setJobDesc(qltyRuleScheduler.getDatatable_name() + ":" + qltyRuleScheduler.getName());
                    xxlJobInfo.setAuthor(qltyRuleScheduler.getCreate_user());
                    xxlJobInfo.setMisfireStrategy("DO_NOTHING");
                    //xxlJobInfo.setAlarmEmail("<EMAIL>");
                    xxlJobInfo.setScheduleType(qltyRuleScheduler.getType());
                    xxlJobInfo.setScheduleConf(qltyRuleScheduler.getCron_expr());
                    xxlJobInfo.setGlueType("BEAN");
                    xxlJobInfo.setExecutorHandler("XbdpQltyRuleTaskHandler");
                    String route_strategy = qltyRuleScheduler.getRoute_strategy();
                    xxlJobInfo.setExecutorRouteStrategy(route_strategy == null ? "FIRST" : route_strategy);
                    String block_strategy = qltyRuleScheduler.getBlock_strategy();
                    xxlJobInfo.setExecutorBlockStrategy(block_strategy == null ? "SERIAL_EXECUTION" : block_strategy);
                    xxlJobInfo.setExecutorTimeout(qltyRuleScheduler.getExecutor_timeout() == null ? 6000 : qltyRuleScheduler.getExecutor_timeout());
                    xxlJobInfo.setExecutorFailRetryCount(qltyRuleScheduler.getExecutor_retry_count() == null ? 1 : qltyRuleScheduler.getExecutor_retry_count());
                    xxlJobInfo.setGlueRemark("GLUE代码初始化");
                    xxlJobInfo.setAddTime(new Date());
                    xxlJobInfo.setJobTaskType("QltyRuleJob");
                    xxlJobInfo.setUpdateTime(new Date());
                    xxlJobInfo.setAuthor("qlty");

                    int jobInfoId = xbdpQltyController.saveJobInfo(xxlJobInfo);
                    qltyRuleScheduler.setDispatch_id(Long.valueOf(jobInfoId));
                    Map<String, Object> maps = DBUtils.insertSqlAndObjects(qltyRuleScheduler, QltyRuleScheduler.class, TABLE_BDP_QLTY_RULE_SCHEDULER);
                    String sql = DBUtils.getSql(maps);
                    Object[] objs = DBUtils.getObjects(maps);
                    xcfJdbcTemplate.update(sql, objs);

                    count++;
                } else if (qltyRuleScheduler.getId() != null && qltyRuleSchedulers.size() == 1) {
                    qltyRuleScheduler.setUpdate_time(sdf.format(new Date()));
                    businessJsonNode.put("jobId", qltyRuleScheduler.getId());
                    executeParamsJson.put("business", businessJsonNode);
                    executeParamsJson.put("QltyRuleSchedulerId", qltyRuleScheduler.getId());
                    if (qltyRuleScheduler.getExecute_params() == null) {
                        qltyRuleScheduler.setExecute_params(executeParamsJson.toJSONString());
                    }


                    XxlJobInfo xxlJobInfo = xbdpQltyController.loadById(qltyRuleScheduler.getDispatch_id().intValue());

                    xxlJobInfo.setExecutorParam(qltyRuleScheduler.getExecute_params());
                    xxlJobInfo.setJobDesc(qltyRuleScheduler.getDatatable_name() + ":" + qltyRuleScheduler.getName());
                    xxlJobInfo.setAuthor(qltyRuleScheduler.getCreate_user());
                    xxlJobInfo.setScheduleType(qltyRuleScheduler.getType());
                    xxlJobInfo.setScheduleConf(qltyRuleScheduler.getCron_expr());
                    xxlJobInfo.setMisfireStrategy("DO_NOTHING");
                    String route_strategy = qltyRuleScheduler.getRoute_strategy();
                    xxlJobInfo.setExecutorRouteStrategy(route_strategy == null ? "FIRST" : route_strategy);
                    String block_strategy = qltyRuleScheduler.getBlock_strategy();
                    xxlJobInfo.setExecutorBlockStrategy(block_strategy == null ? "SERIAL_EXECUTION" : block_strategy);
                    xxlJobInfo.setExecutorTimeout(qltyRuleScheduler.getExecutor_timeout() == null ? 6000 : qltyRuleScheduler.getExecutor_timeout());
                    xxlJobInfo.setExecutorFailRetryCount(qltyRuleScheduler.getExecutor_retry_count() == null ? 1 : qltyRuleScheduler.getExecutor_retry_count());
                    xxlJobInfo.setGlueRemark("GLUE代码初始化");
                    xxlJobInfo.setAddTime(new Date());
                    xxlJobInfo.setUpdateTime(new Date());
                    xxlJobInfo.setJobTaskType("QltyRuleJob");
                    xxlJobInfo.setAuthor("qlty");
                    xbdpQltyController.update(xxlJobInfo);

                    Map<String, Object> maps = DBUtils.updateSqlAndObjects("id", qltyRuleScheduler, QltyRuleScheduler.class, TABLE_BDP_QLTY_RULE_SCHEDULER);
                    String sql = DBUtils.getSql(maps);
                    Object[] objs = DBUtils.getObjects(maps);
                    xcfJdbcTemplate.update(sql, objs);

                    count++;
                } else {
                    throw new RuntimeException("Multiply Scheduler Record.");
                }
                return count == 1 ? "true" : "false";
            } else {
                //TODO task关联调度
                throw new RuntimeException("Not Support Scheduler Type:" + qltyRuleScheduler.getType());
            }
        } catch (Exception e) {
            LOG.info("schedulerSave", e);
            return "false";
        }
    }

    public String ruleUpdate(QltyRule qltyRule) {
        try {
            Map<String, Object> maps = DBUtils.updateSqlAndObjects("id", qltyRule, QltyRule.class, TABLE_BDP_QLTY_RULE);
            String sql = DBUtils.getSql(maps);
            Object[] objs = DBUtils.getObjects(maps);
            return xcfJdbcTemplate.update(sql, objs) == 1 ? "true" : "false";
        } catch (Exception e) {
            LOG.info("ruleUpdate", e);
            return "false";
        }
    }

    public String ruleDelete(String ruleId) {
        try {
            String sql = DBUtils.deleteSql(TABLE_BDP_QLTY_RULE, "id");
            return String.valueOf(xcfJdbcTemplate.update(sql, ruleId) == 1);
        } catch (Exception e) {
            LOG.info("ruleDelete", e);
            return "false";
        }
    }

    public String ruleControl(String ruleId, String disabled) {
        try {
            if ("1".equals(disabled) || "0".equals(disabled)) {
                return String.valueOf(xcfJdbcTemplate.update(
                        " UPDATE " + TABLE_BDP_QLTY_RULE +
                                " SET disabled=? " +
                                " WHERE id=? "
                        ,
                        disabled,
                        ruleId
                ) == 1);
            } else {
                throw new Exception("unknown disabled status: use 0：enabled ，1：disabled");
            }
        } catch (Exception e) {
            LOG.info("ruleControl", e);
            return "false";
        }
    }

    @Transactional
    public String schedulerRun(String datawareId, String datatableId, String started) {
        //1开启 0关闭
        try {
            if ("1".equals(started) || "0".equals(started)) {
                ReturnT<String> controlStatus = null;
                List<QltyRuleScheduler> qltyRuleSchedulers = xcfJdbcTemplate.<QltyRuleScheduler>queryForEntities(
                        " select * from  " +
                                TABLE_BDP_QLTY_RULE_SCHEDULER +
                                " where dataware_id ='" + datawareId + "' and " +
                                " datatable_id ='" + datatableId + "' "
                        ,
                        new Object[]{},
                        QltyRuleScheduler.class
                );
                for (QltyRuleScheduler qltyRuleScheduler : qltyRuleSchedulers) {
                    if ("0".equals(started)) {
                        try {
                            controlStatus = xbdpQltyController.pause(qltyRuleScheduler.getDispatch_id().intValue());
                        } catch (Exception e) {
                            LOG.info("schedulerRun", e);
                        }
                        String callBackParams = qltyRuleScheduler.getCall_back_params();
                        try {
                            //关闭最近的一次flink job
                            JSONObject callBackJson = JSON.parseObject(callBackParams);
                            String jarId = callBackJson.getString("jarId");
                            String jobId = callBackJson.getString("jobId");
                            qltyMetricsController.metricsFlinkKill(jobId, jarId);
                        } catch (Exception e) {
                            LOG.info("schedulerRun", e);
                        }
                    } else if ("1".equals(started)) {
                        try {
                            controlStatus = xbdpQltyController.start(qltyRuleScheduler.getDispatch_id().intValue());
                        } catch (Exception e) {
                            LOG.info("schedulerRun", e);
                        }
                    }
                }
                return String.valueOf(xcfJdbcTemplate.update(
                        " UPDATE " + TABLE_BDP_QLTY_RULE_SCHEDULER +
                                " SET started=? " +
                                " WHERE " +
                                " dataware_id=? and " +
                                " datatable_id=?  "
                        ,
                        started,
                        datawareId,
                        datatableId
                ) > 0);
            } else {
                throw new Exception("unknown disabled status: use 0：stop ，1：started");
            }
        } catch (Exception e) {
            LOG.info("schedulerRun", e);
            return "false";
        }
    }


    public MetaDatasource getDatasourceByRuleId(String ruleId) {
        try {
            return xcfJdbcTemplate.queryForEntity(
                    " SELECT " +
                            " bdp_meta_datasource.*  " +
                            " FROM " +
                            " bdp_qlty_rule, " +
                            " bdp_meta_datatable, " +
                            " bdp_meta_datasource  " +
                            " WHERE " +
                            " bdp_qlty_rule.datatable_id = bdp_meta_datatable.id  " +
                            " AND bdp_meta_datatable.datasource_id = bdp_meta_datasource.id " +
                            " and bdp_qlty_rule.id = '" + ruleId + "'"
                    ,
                    new Object[]{}
                    ,
                    MetaDatasource.class
            );
        } catch (Exception e) {
            LOG.info("getDatasourceByRuleId", e);
            return null;
        }
    }

    public MetaDatatable getDatatableByRuleId(String ruleId) {
        try {
            return xcfJdbcTemplate.queryForEntity(
                    " SELECT " +
                            " bdp_meta_datatable.*  " +
                            " FROM " +
                            " bdp_qlty_rule, " +
                            " bdp_meta_datatable " +
                            " WHERE " +
                            " bdp_qlty_rule.datatable_id = bdp_meta_datatable.id  " +
                            " and bdp_qlty_rule.id = '" + ruleId + "'"
                    ,
                    new Object[]{}
                    ,
                    MetaDatatable.class
            );
        } catch (Exception e) {
            LOG.info("getDatatableByRuleId", e);
            return null;
        }
    }

    public List<QltyRuleScheduler> schedulerAllQuery(String datawareId, String datatableId) {
        try {

            List<QltyRuleScheduler> qltyRuleSchedulers = xcfJdbcTemplate.<QltyRuleScheduler>queryForEntities(
                    " select " +
                            " * " +
                            " from " + QltyRuleScheduler.TABLE +
                            " where  dataware_id != '-1' and datatable_id != '-1' " +
                            " and " + ((datawareId == null) ? " true " : (" dataware_id ='" + datawareId + "' ")) +
                            " and " + ((datatableId == null) ? " true " : (" datatable_id ='" + datatableId + "' "))
                    ,
                    new Object[]{},
                    QltyRuleScheduler.class
            );
            return qltyRuleSchedulers;
        } catch (Exception e) {
            LOG.info("schedulerAllQuery", e);
            return null;
        }
    }

    public List<QltyRule> ruleAllQuery() {
        try {

            List<QltyRule> qltyRules = xcfJdbcTemplate.<QltyRule>queryForEntities(
                    " select " +
                            " * " +
                            " from " + QltyRule.TABLE +
                            " where  dataware_id != '-1' and datatable_id != '-1' "
                    ,
                    new Object[]{},
                    QltyRule.class
            );
            return qltyRules;
        } catch (Exception e) {
            LOG.info("ruleAllQuery", e);
            return null;
        }
    }

    public List<QltyDim> dimAllQuery() {
        try {

            List<QltyDim> qltyDims = xcfJdbcTemplate.<QltyDim>queryForEntities(
                    " select " +
                            " * " +
                            " from " + QltyDim.TABLE
                    ,
                    new Object[]{},
                    QltyDim.class
            );
            return qltyDims;
        } catch (Exception e) {
            LOG.info("dimAllQuery", e);
            return null;
        }
    }
}
