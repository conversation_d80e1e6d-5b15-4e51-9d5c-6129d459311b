package com.xmcares.platform.admin.quality.web;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.platform.admin.quality.model.QltyRuleTmpl;
import com.xmcares.platform.admin.quality.service.QualityRuleTmplService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/25 09:10
 */
@Api(value = "质量规则模板服务")
@Validated
@RestController
@RequestMapping("/quality/rule-tmpl")
public class QualityRuleTmplController {

    @Autowired
    QualityRuleTmplService qualityRuleTmplService;

    @ApiOperation("从数据库中查询规则模板记录")
    @GetMapping("/page-query")
    @ResponseBody
    public Page<QltyRuleTmpl> pageQuery(
           String ruleName,
            String ruleLevel,
            String dimCode,
            @RequestParam(name = "pageNo") Integer pageNo,
            @RequestParam(name = "pageSize") Integer pageSize
    ) {
        return qualityRuleTmplService.pageQuery(ruleName, ruleLevel, dimCode, pageNo, pageSize);
    }

    @ApiOperation("从数据库中查询规则模板记录")
    @GetMapping("/all-query")
    @ResponseBody
    public List<QltyRuleTmpl> allQuery(
    ) {
        return qualityRuleTmplService.allQuery();
    }

}
