package com.xmcares.platform.admin.metadata.database.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/20 15:40
 */
@ApiModel(value = "DatatableUnSyncResultVo", description = "数据源中未同步的表集合")
public class DatatableUnSyncResultVo implements Serializable {

    @ApiModelProperty(value = "数据源ID")
    private String datasourceId;
    @ApiModelProperty(value = "未同步的表")
    private List<String> unSyncTables;
    @ApiModelProperty(value = "已同步的表")
    private List<String> syncTables;

    public DatatableUnSyncResultVo() {
    }

    public DatatableUnSyncResultVo(String datasourceId, List<String> unSyncTables, List<String> syncTables) {
        this.datasourceId = datasourceId;
        this.unSyncTables = unSyncTables;
        this.syncTables = syncTables;
    }

    public String getDatasourceId() {
        return datasourceId;
    }

    public List<String> getUnSyncTables() {
        return unSyncTables;
    }

    public List<String> getSyncTables() {
        return syncTables;
    }
}
