package com.xmcares.platform.admin.lifecycle.service;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.platform.admin.lifecycle.model.vo.*;
import com.xmcares.platform.admin.lifecycle.repository.ArchiveRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ArchiveService {

    @Autowired
    ArchiveRepository archiveRepository;

    public Page<LifecycleArchiveQueryVO> listQuery(
            String datawareId,
            String datatableId,
            String datatableReduced,
            Integer pageNo,
            Integer pageSize
    ) {
        return archiveRepository.listQuery(datawareId, datatableId, datatableReduced, pageNo, pageSize);
    }


    public LifecycleArchiveGetVO get(
            String id
    ) {
        return archiveRepository.get(id);
    }


    public String schedulerAdd(
            LifecycleArchiveSchedulerAddInVO schedulerAddInVO
    ) {
        return archiveRepository.schedulerAdd(schedulerAddInVO);
    }


    public String schedulerUpdate(
            LifecycleArchiveSchedulerUpdateInVO schedulerUpdateInVO
    ) {
        return archiveRepository.schedulerUpdate(schedulerUpdateInVO);
    }


    public String schedulerDelete(
            String id
    ) {
        return archiveRepository.schedulerDelete(id);
    }


    public String schedulerClose(
            String id
    ) {
        return archiveRepository.schedulerClose(id);
    }


    public String schedulerOpen(
            String id
    ) {
        return archiveRepository.schedulerOpen(id);
    }


    public String add(
            LifecycleArchiveAddInVO lifecycleArchiveAddInVO
    ) {
        return archiveRepository.add(lifecycleArchiveAddInVO);
    }

    public String update(
            LifecycleArchiveUpdateInVO lifecycleArchiveAddInVO
    ) {
        return archiveRepository.update(lifecycleArchiveAddInVO);
    }


    public String delete(
            String id
    ) {
        return archiveRepository.delete(id);
    }


    public String start(
            String id,
            String archiveType
    ) {
        return archiveRepository.start(id, archiveType);
    }

    public String disable(
            String id,
            String archiveType
    ) {
        return archiveRepository.disable(id, archiveType);
    }


    public Page<LifecycleArchiveRecordQueryVO> recordQuery(
            String datawareId,
            String datatableId,
            String archiveId,
            String beginTimer,
            String endTimer,
            Integer pageNo,
            Integer pageSize
    ) {
        return archiveRepository.recordQuery(datawareId, datatableId, archiveId, beginTimer, endTimer, pageNo, pageSize);
    }


    public String recordDelete(
            String id
    ) {
        return archiveRepository.recordDelete(id);
    }


}
