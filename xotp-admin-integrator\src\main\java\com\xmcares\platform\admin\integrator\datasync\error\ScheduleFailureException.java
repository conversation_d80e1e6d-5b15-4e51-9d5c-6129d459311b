/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/3/2
 */
package com.xmcares.platform.admin.integrator.datasync.error;

import com.xmcares.framework.commons.error.BaseException;

/**
 * <AUTHOR>
 * @since 1.1.0
 */
public class ScheduleFailureException extends BaseException {

    public ScheduleFailureException() {
    }

    public ScheduleFailureException(String message) {
        super(message);
    }

    public ScheduleFailureException(String message, Throwable cause) {
        super(message, cause);
    }

    public ScheduleFailureException(Throwable cause) {
        super(cause);
    }

    public ScheduleFailureException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
