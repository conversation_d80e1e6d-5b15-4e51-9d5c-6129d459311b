/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/24
 */
package com.xmcares.platform.admin.common.datasource.jdbc;

import com.xmcares.framework.fsclient.FSTemplate;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.platform.admin.common.datasource.DataSource;
import com.xmcares.platform.admin.common.datasource.DataSourceManager;
import com.xmcares.platform.admin.common.datasource.DataSourceOptions;
import com.xmcares.platform.admin.common.errors.SystemException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.nio.file.Paths;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * JDBC 类型(包括SQL 、BIGDATA组的部分)的数据源管理
 * 共享各个地方的数据源使用，减少创建和销毁的消耗
 * <AUTHOR>
 * @since 1.0.0
 */
public class JdbcDataSourceManager implements DataSourceManager<JdbcDataSource> {
    private static final Logger logger = LoggerFactory.getLogger(JdbcDataSourceManager.class);

    private final Map<String, JdbcDataSource> dataSources = new ConcurrentHashMap<>();

    /**
     * 文件存储客户端，用于获取数据源连接需要的配置文件，如hive的认证文件krb5.conf文件
     */
    private final FSTemplate fsTemplate;

    private String resourceLocalDir = "tmp";

    public JdbcDataSourceManager(FSTemplate fsTemplate) {
        this.fsTemplate = fsTemplate;
    }

    @Override
    public JdbcDataSource getOrCreateDataSource(DataSourceOptions options) {
        JdbcType type = JdbcType.fromTypeName(options.getTypeName());
        if (type == null) {
            JdbcType.fromJdbcUrl(options.getUrl());
        }
        if (type == null) {
            throw new IllegalArgumentException("不支持的JDBC(SQL or BigData)类型: " + options.getTypeName());
        }
        return dataSources.computeIfAbsent(options.getName(), k -> {
            setpathKerberos(options);
            return type.createDataSource(options);
        });
    }

    @Override
    public void destroyDataSource(String name) {
        DataSource removed = dataSources.remove(name);
        if (removed != null) {
            try {
                removed.close();
            } catch (Exception e) {
                logger.warn("销毁数据源[{}]失败:{}", name, e.getMessage());
            }
        }
    }

    public String getResourceLocalDir() {
        return resourceLocalDir;
    }

    public void setResourceLocalDir(String resourceLocalDir) {
        this.resourceLocalDir = resourceLocalDir;
    }

    /**
     * 配置kerberos认证
     * @param options 数据源配置
     */
    protected void setpathKerberos(DataSourceOptions options) {
        String enableKerberos = (String) options.get("enableKerberos");
        if (!"3".equals(enableKerberos)) {
            return;
        }
        logger.info("启用并下载置换[enableKerberos={}]Kerberos认证文件路径配置值", enableKerberos);

        // 配置Krb5.conf 文件
        if (!setpathKrb5Conf(options)) return;

        if (setpathKeytabConf(options)) {
            logger.info("启用并完成[enableKerberos={}]Kerberos认证配置的下载置换", enableKerberos);
        }
    }

    /**
     * 配置kerberos krb5.conf
     * @param options 数据源配置
     * @return 配置成功返回true
     */
    private boolean setpathKrb5Conf(DataSourceOptions options) {
        String krb5Path = (String) options.get("krb5path");
        if (null == krb5Path || krb5Path.isEmpty()) {
            logger.warn("启用Krb认证[enableKerberos=3]，但未配置krb5Path(已忽略启用)");
            return false;
        }
        String krb5LocalPath = getLocalFilePath(krb5Path);
        if (logger.isDebugEnabled()) {
            logger.debug("下载kerberos认证配置文件的路径: remote:{} > local:{}", krb5Path, krb5LocalPath);
        }
        downloadResource(krb5Path, krb5LocalPath);
        logger.info("设置kerberos认证配置文件的本地路径: {}", krb5LocalPath);
        options.put("krb5path", krb5LocalPath);

        return true;
    }

    /**
     * 配置kerberos keytab
     * @param options 数据源配置
     * @return 配置是否成功
     */
    private boolean setpathKeytabConf(DataSourceOptions options) {
        String enableKerberos = (String)options.get("enableKerberos");
        String keytabPath = (String) options.get("keytabPath");
        if (StringUtils.isEmpty(keytabPath)) {
            logger.warn("启用Krb认证[enableKerberos={}]，但未配置keytabPath(已忽略启用)", enableKerberos);
            return false;
        }
        String keytabLocalPath = getLocalFilePath(keytabPath);
        if (logger.isDebugEnabled()) {
            logger.debug("下载kerberos认证keytab文件的路径: remote:{} > local:{}", keytabPath, keytabLocalPath);
        }
        downloadResource(keytabPath, keytabLocalPath);
        logger.info("设置kerberos认证keytab文件本地路径: {}", keytabLocalPath);
        options.put("keytabPath", keytabLocalPath);
        return true;
    }

    private void downloadResource(String remotePath, String localPath) {
        try (FileOutputStream output = new FileOutputStream(localPath)) {
            fsTemplate.loadFile(new FileDesc.FileDescImpl(remotePath, remotePath), output);
        } catch (Exception e) {
            throw new SystemException(
                    String.format("下载kerberos认证配置文件的路径: [%s] > local:[%s]", remotePath, localPath), e);
        }
    }

    private String getLocalFilePath(String filePath) {
        String rootPath = System.getProperty("app.home");
        if (StringUtils.isEmpty(rootPath)) {
            rootPath = Paths.get("").toAbsolutePath().toString();
        }
        return rootPath +  File.separator + this.resourceLocalDir
                +  File.separator + filePath;
    }

}
