package com.xmcares.platform.admin.integrator.plugin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

/**
 * 数据同步定义
 */
@ApiModel(description="数据同步定义")
@TableName(value = "xotp.bdp_intg_datasync")
public class Datasync {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value="ID")
    private String id;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
     * 创建人/维护人
     */
    @TableField(value = "create_user")
    @ApiModelProperty(value="创建人/维护人")
    private String createUser;

    /**
     * 同步任务名称
     */
    @TableField(value = "intg_name")
    @ApiModelProperty(value="同步任务名称")
    private String intgName;

    /**
     * 数据来源集成方式 0:内置 1:插件
     */
    @TableField(value = "orgin_type")
    @ApiModelProperty(value="数据来源集成方式 0:内置 1:插件")
    private String orginType;

    /**
     * 数据来源集成模型ID
     */
    @TableField(value = "orgin_intg_model_id")
    @ApiModelProperty(value="数据来源集成模型ID")
    private String orginIntgModelId;

    /**
     * 数据来源数据源(或插件)名称
     */
    @TableField(value = "orgin_datasource_name")
    @ApiModelProperty(value="数据来源数据源(或插件)名称")
    private String orginDatasourceName;

    /**
     * 数据来源数据源(或插件)ID
     */
    @TableField(value = "orgin_datasource_id")
    @ApiModelProperty(value="数据来源数据源(或插件)ID")
    private String orginDatasourceId;

    /**
     * 数据来源插件路径
     */
    @TableField(value = "orgin_plugin_path")
    @ApiModelProperty(value="数据来源插件路径")
    private String orginPluginPath;

    /**
     * 数据来源基础信息实际Json数据
     */
    @TableField(value = "orgin_base_json")
    @ApiModelProperty(value="数据来源基础信息实际Json数据")
    private String orginBaseJson;

    /**
     * 数据来源进阶信息实际Json数据
     */
    @TableField(value = "orgin_adv_json")
    @ApiModelProperty(value="数据来源进阶信息实际Json数据")
    private String orginAdvJson;

    /**
     * 数据来源高级信息实际Json数据
     */
    @TableField(value = "orgin_high_json")
    @ApiModelProperty(value="数据来源高级信息实际Json数据")
    private String orginHighJson;

    /**
     * 数据来源列信息实际Json数据
     */
    @TableField(value = "orgin_column_json")
    @ApiModelProperty(value="数据来源列信息实际Json数据")
    private String orginColumnJson;

    /**
     * 数据来源运行模式
     */
    @TableField(value = "orgin_run_schema")
    @ApiModelProperty(value="数据来源运行模式")
    private String orginRunSchema;

    /**
     * 数据去向集成方式 0:内置 1：插件
     */
    @TableField(value = "dest_type")
    @ApiModelProperty(value="数据去向集成方式 0:内置 1：插件")
    private String destType;

    /**
     * 数据去向集成模型ID
     */
    @TableField(value = "dest_intg_model_id")
    @ApiModelProperty(value="数据去向集成模型ID")
    private String destIntgModelId;

    /**
     * 数据去向数据源(或插件)名称
     */
    @TableField(value = "dest_datasource_name")
    @ApiModelProperty(value="数据去向数据源(或插件)名称")
    private String destDatasourceName;

    /**
     * 数据去向数据源(或插件)ID
     */
    @TableField(value = "dest_datasource_id")
    @ApiModelProperty(value="数据去向数据源(或插件)ID")
    private String destDatasourceId;

    /**
     * 数据去向插件路径
     */
    @TableField(value = "dest_plugin_path")
    @ApiModelProperty(value="数据去向插件路径")
    private String destPluginPath;

    /**
     * 数据去向基础信息实际Json数据
     */
    @TableField(value = "dest_base_json")
    @ApiModelProperty(value="数据去向基础信息实际Json数据")
    private String destBaseJson;

    /**
     * 数据去向进阶信息实际Json数据
     */
    @TableField(value = "dest_adv_json")
    @ApiModelProperty(value="数据去向进阶信息实际Json数据")
    private String destAdvJson;

    /**
     * 调度参数
     */
    @TableField(value = "scheduler_expr")
    @ApiModelProperty(value="调度参数")
    private String schedulerExpr;

    /**
     * 数据去向高级信息实际Json数据
     */
    @TableField(value = "dest_high_json")
    @ApiModelProperty(value="数据去向高级信息实际Json数据")
    private String destHighJson;

    /**
     * 数据去向列信息实际Json数据
     */
    @TableField(value = "dest_column_json")
    @ApiModelProperty(value="数据去向列信息实际Json数据")
    private String destColumnJson;

    /**
     * 路由策略
     */
    @TableField(value = "route_strategy")
    @ApiModelProperty(value="路由策略")
    private String routeStrategy;

    /**
     * 阻塞处理策略
     */
    @TableField(value = "block_strategy")
    @ApiModelProperty(value="阻塞处理策略")
    private String blockStrategy;

    /**
     * 子任务ID组
     */
    @TableField(value = "child_jobid")
    @ApiModelProperty(value="子任务ID组")
    private String childJobid;

    /**
     * 任务执行超时时间，单位秒
     */
    @TableField(value = "executor_timeout")
    @ApiModelProperty(value="任务执行超时时间，单位秒")
    private Integer executorTimeout;

    /**
     * 失败重试次数
     */
    @TableField(value = "executor_fail_retry_count")
    @ApiModelProperty(value="失败重试次数")
    private Integer executorFailRetryCount;

    /**
     * 是否删除 0：否 1：是
     */
    @TableField(value = "has_delete")
    @ApiModelProperty(value="是否删除 0：否 1：是")
    private String hasDelete;

    /**
     * 数据集成同步模式（BATCH/STREAMING）
     */
    @TableField(value = "job_mode")
    @ApiModelProperty(value="数据集成同步模式（BATCH/STREAMING）")
    private String jobMode;

    /**
     * 数据集成同步字段映射
     */
    @TableField(value = "field_mapping_json")
    @ApiModelProperty(value="数据集成同步字段映射")
    private String fieldMappingJson;

    @TableField(value = "orgin_plugin_id")
    @ApiModelProperty(value="")
    private String orginPluginId;

    @TableField(value = "dest_plugin_id")
    @ApiModelProperty(value="")
    private String destPluginId;

    /**
     * 获取ID
     *
     * @return id - ID
     */
    public String getId() {
        return id;
    }

    /**
     * 设置ID
     *
     * @param id ID
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取创建人/维护人
     *
     * @return create_user - 创建人/维护人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人/维护人
     *
     * @param createUser 创建人/维护人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取同步任务名称
     *
     * @return intg_name - 同步任务名称
     */
    public String getIntgName() {
        return intgName;
    }

    /**
     * 设置同步任务名称
     *
     * @param intgName 同步任务名称
     */
    public void setIntgName(String intgName) {
        this.intgName = intgName;
    }

    /**
     * 获取数据来源集成方式 0:内置 1:插件
     *
     * @return orgin_type - 数据来源集成方式 0:内置 1:插件
     */
    public String getOrginType() {
        return orginType;
    }

    /**
     * 设置数据来源集成方式 0:内置 1:插件
     *
     * @param orginType 数据来源集成方式 0:内置 1:插件
     */
    public void setOrginType(String orginType) {
        this.orginType = orginType;
    }

    /**
     * 获取数据来源集成模型ID
     *
     * @return orgin_intg_model_id - 数据来源集成模型ID
     */
    public String getOrginIntgModelId() {
        return orginIntgModelId;
    }

    /**
     * 设置数据来源集成模型ID
     *
     * @param orginIntgModelId 数据来源集成模型ID
     */
    public void setOrginIntgModelId(String orginIntgModelId) {
        this.orginIntgModelId = orginIntgModelId;
    }

    /**
     * 获取数据来源数据源(或插件)名称
     *
     * @return orgin_datasource_name - 数据来源数据源(或插件)名称
     */
    public String getOrginDatasourceName() {
        return orginDatasourceName;
    }

    /**
     * 设置数据来源数据源(或插件)名称
     *
     * @param orginDatasourceName 数据来源数据源(或插件)名称
     */
    public void setOrginDatasourceName(String orginDatasourceName) {
        this.orginDatasourceName = orginDatasourceName;
    }

    /**
     * 获取数据来源数据源(或插件)ID
     *
     * @return orgin_datasource_id - 数据来源数据源(或插件)ID
     */
    public String getOrginDatasourceId() {
        return orginDatasourceId;
    }

    /**
     * 设置数据来源数据源(或插件)ID
     *
     * @param orginDatasourceId 数据来源数据源(或插件)ID
     */
    public void setOrginDatasourceId(String orginDatasourceId) {
        this.orginDatasourceId = orginDatasourceId;
    }

    /**
     * 获取数据来源插件路径
     *
     * @return orgin_plugin_path - 数据来源插件路径
     */
    public String getOrginPluginPath() {
        return orginPluginPath;
    }

    /**
     * 设置数据来源插件路径
     *
     * @param orginPluginPath 数据来源插件路径
     */
    public void setOrginPluginPath(String orginPluginPath) {
        this.orginPluginPath = orginPluginPath;
    }

    /**
     * 获取数据来源基础信息实际Json数据
     *
     * @return orgin_base_json - 数据来源基础信息实际Json数据
     */
    public String getOrginBaseJson() {
        return orginBaseJson;
    }

    /**
     * 设置数据来源基础信息实际Json数据
     *
     * @param orginBaseJson 数据来源基础信息实际Json数据
     */
    public void setOrginBaseJson(String orginBaseJson) {
        this.orginBaseJson = orginBaseJson;
    }

    /**
     * 获取数据来源进阶信息实际Json数据
     *
     * @return orgin_adv_json - 数据来源进阶信息实际Json数据
     */
    public String getOrginAdvJson() {
        return orginAdvJson;
    }

    /**
     * 设置数据来源进阶信息实际Json数据
     *
     * @param orginAdvJson 数据来源进阶信息实际Json数据
     */
    public void setOrginAdvJson(String orginAdvJson) {
        this.orginAdvJson = orginAdvJson;
    }

    /**
     * 获取数据来源高级信息实际Json数据
     *
     * @return orgin_high_json - 数据来源高级信息实际Json数据
     */
    public String getOrginHighJson() {
        return orginHighJson;
    }

    /**
     * 设置数据来源高级信息实际Json数据
     *
     * @param orginHighJson 数据来源高级信息实际Json数据
     */
    public void setOrginHighJson(String orginHighJson) {
        this.orginHighJson = orginHighJson;
    }

    /**
     * 获取数据来源列信息实际Json数据
     *
     * @return orgin_column_json - 数据来源列信息实际Json数据
     */
    public String getOrginColumnJson() {
        return orginColumnJson;
    }

    /**
     * 设置数据来源列信息实际Json数据
     *
     * @param orginColumnJson 数据来源列信息实际Json数据
     */
    public void setOrginColumnJson(String orginColumnJson) {
        this.orginColumnJson = orginColumnJson;
    }

    /**
     * 获取数据来源运行模式
     *
     * @return orgin_run_schema - 数据来源运行模式
     */
    public String getOrginRunSchema() {
        return orginRunSchema;
    }

    /**
     * 设置数据来源运行模式
     *
     * @param orginRunSchema 数据来源运行模式
     */
    public void setOrginRunSchema(String orginRunSchema) {
        this.orginRunSchema = orginRunSchema;
    }

    /**
     * 获取数据去向集成方式 0:内置 1：插件
     *
     * @return dest_type - 数据去向集成方式 0:内置 1：插件
     */
    public String getDestType() {
        return destType;
    }

    /**
     * 设置数据去向集成方式 0:内置 1：插件
     *
     * @param destType 数据去向集成方式 0:内置 1：插件
     */
    public void setDestType(String destType) {
        this.destType = destType;
    }

    /**
     * 获取数据去向集成模型ID
     *
     * @return dest_intg_model_id - 数据去向集成模型ID
     */
    public String getDestIntgModelId() {
        return destIntgModelId;
    }

    /**
     * 设置数据去向集成模型ID
     *
     * @param destIntgModelId 数据去向集成模型ID
     */
    public void setDestIntgModelId(String destIntgModelId) {
        this.destIntgModelId = destIntgModelId;
    }

    /**
     * 获取数据去向数据源(或插件)名称
     *
     * @return dest_datasource_name - 数据去向数据源(或插件)名称
     */
    public String getDestDatasourceName() {
        return destDatasourceName;
    }

    /**
     * 设置数据去向数据源(或插件)名称
     *
     * @param destDatasourceName 数据去向数据源(或插件)名称
     */
    public void setDestDatasourceName(String destDatasourceName) {
        this.destDatasourceName = destDatasourceName;
    }

    /**
     * 获取数据去向数据源(或插件)ID
     *
     * @return dest_datasource_id - 数据去向数据源(或插件)ID
     */
    public String getDestDatasourceId() {
        return destDatasourceId;
    }

    /**
     * 设置数据去向数据源(或插件)ID
     *
     * @param destDatasourceId 数据去向数据源(或插件)ID
     */
    public void setDestDatasourceId(String destDatasourceId) {
        this.destDatasourceId = destDatasourceId;
    }

    /**
     * 获取数据去向插件路径
     *
     * @return dest_plugin_path - 数据去向插件路径
     */
    public String getDestPluginPath() {
        return destPluginPath;
    }

    /**
     * 设置数据去向插件路径
     *
     * @param destPluginPath 数据去向插件路径
     */
    public void setDestPluginPath(String destPluginPath) {
        this.destPluginPath = destPluginPath;
    }

    /**
     * 获取数据去向基础信息实际Json数据
     *
     * @return dest_base_json - 数据去向基础信息实际Json数据
     */
    public String getDestBaseJson() {
        return destBaseJson;
    }

    /**
     * 设置数据去向基础信息实际Json数据
     *
     * @param destBaseJson 数据去向基础信息实际Json数据
     */
    public void setDestBaseJson(String destBaseJson) {
        this.destBaseJson = destBaseJson;
    }

    /**
     * 获取数据去向进阶信息实际Json数据
     *
     * @return dest_adv_json - 数据去向进阶信息实际Json数据
     */
    public String getDestAdvJson() {
        return destAdvJson;
    }

    /**
     * 设置数据去向进阶信息实际Json数据
     *
     * @param destAdvJson 数据去向进阶信息实际Json数据
     */
    public void setDestAdvJson(String destAdvJson) {
        this.destAdvJson = destAdvJson;
    }

    /**
     * 获取调度参数
     *
     * @return scheduler_expr - 调度参数
     */
    public String getSchedulerExpr() {
        return schedulerExpr;
    }

    /**
     * 设置调度参数
     *
     * @param schedulerExpr 调度参数
     */
    public void setSchedulerExpr(String schedulerExpr) {
        this.schedulerExpr = schedulerExpr;
    }

    /**
     * 获取数据去向高级信息实际Json数据
     *
     * @return dest_high_json - 数据去向高级信息实际Json数据
     */
    public String getDestHighJson() {
        return destHighJson;
    }

    /**
     * 设置数据去向高级信息实际Json数据
     *
     * @param destHighJson 数据去向高级信息实际Json数据
     */
    public void setDestHighJson(String destHighJson) {
        this.destHighJson = destHighJson;
    }

    /**
     * 获取数据去向列信息实际Json数据
     *
     * @return dest_column_json - 数据去向列信息实际Json数据
     */
    public String getDestColumnJson() {
        return destColumnJson;
    }

    /**
     * 设置数据去向列信息实际Json数据
     *
     * @param destColumnJson 数据去向列信息实际Json数据
     */
    public void setDestColumnJson(String destColumnJson) {
        this.destColumnJson = destColumnJson;
    }

    /**
     * 获取路由策略
     *
     * @return route_strategy - 路由策略
     */
    public String getRouteStrategy() {
        return routeStrategy;
    }

    /**
     * 设置路由策略
     *
     * @param routeStrategy 路由策略
     */
    public void setRouteStrategy(String routeStrategy) {
        this.routeStrategy = routeStrategy;
    }

    /**
     * 获取阻塞处理策略
     *
     * @return block_strategy - 阻塞处理策略
     */
    public String getBlockStrategy() {
        return blockStrategy;
    }

    /**
     * 设置阻塞处理策略
     *
     * @param blockStrategy 阻塞处理策略
     */
    public void setBlockStrategy(String blockStrategy) {
        this.blockStrategy = blockStrategy;
    }

    /**
     * 获取子任务ID组
     *
     * @return child_jobid - 子任务ID组
     */
    public String getChildJobid() {
        return childJobid;
    }

    /**
     * 设置子任务ID组
     *
     * @param childJobid 子任务ID组
     */
    public void setChildJobid(String childJobid) {
        this.childJobid = childJobid;
    }

    /**
     * 获取任务执行超时时间，单位秒
     *
     * @return executor_timeout - 任务执行超时时间，单位秒
     */
    public Integer getExecutorTimeout() {
        return executorTimeout;
    }

    /**
     * 设置任务执行超时时间，单位秒
     *
     * @param executorTimeout 任务执行超时时间，单位秒
     */
    public void setExecutorTimeout(Integer executorTimeout) {
        this.executorTimeout = executorTimeout;
    }

    /**
     * 获取失败重试次数
     *
     * @return executor_fail_retry_count - 失败重试次数
     */
    public Integer getExecutorFailRetryCount() {
        return executorFailRetryCount;
    }

    /**
     * 设置失败重试次数
     *
     * @param executorFailRetryCount 失败重试次数
     */
    public void setExecutorFailRetryCount(Integer executorFailRetryCount) {
        this.executorFailRetryCount = executorFailRetryCount;
    }

    /**
     * 获取是否删除 0：否 1：是
     *
     * @return has_delete - 是否删除 0：否 1：是
     */
    public String getHasDelete() {
        return hasDelete;
    }

    /**
     * 设置是否删除 0：否 1：是
     *
     * @param hasDelete 是否删除 0：否 1：是
     */
    public void setHasDelete(String hasDelete) {
        this.hasDelete = hasDelete;
    }

    /**
     * 获取数据集成同步模式（BATCH/STREAMING）
     *
     * @return job_mode - 数据集成同步模式（BATCH/STREAMING）
     */
    public String getJobMode() {
        return jobMode;
    }

    /**
     * 设置数据集成同步模式（BATCH/STREAMING）
     *
     * @param jobMode 数据集成同步模式（BATCH/STREAMING）
     */
    public void setJobMode(String jobMode) {
        this.jobMode = jobMode;
    }

    /**
     * 获取数据集成同步字段映射
     *
     * @return field_mapping_json - 数据集成同步字段映射
     */
    public String getFieldMappingJson() {
        return fieldMappingJson;
    }

    /**
     * 设置数据集成同步字段映射
     *
     * @param fieldMappingJson 数据集成同步字段映射
     */
    public void setFieldMappingJson(String fieldMappingJson) {
        this.fieldMappingJson = fieldMappingJson;
    }

    /**
     * @return orgin_plugin_id
     */
    public String getOrginPluginId() {
        return orginPluginId;
    }

    /**
     * @param orginPluginId
     */
    public void setOrginPluginId(String orginPluginId) {
        this.orginPluginId = orginPluginId;
    }

    /**
     * @return dest_plugin_id
     */
    public String getDestPluginId() {
        return destPluginId;
    }

    /**
     * @param destPluginId
     */
    public void setDestPluginId(String destPluginId) {
        this.destPluginId = destPluginId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", intgName=").append(intgName);
        sb.append(", orginType=").append(orginType);
        sb.append(", orginIntgModelId=").append(orginIntgModelId);
        sb.append(", orginDatasourceName=").append(orginDatasourceName);
        sb.append(", orginDatasourceId=").append(orginDatasourceId);
        sb.append(", orginPluginPath=").append(orginPluginPath);
        sb.append(", orginBaseJson=").append(orginBaseJson);
        sb.append(", orginAdvJson=").append(orginAdvJson);
        sb.append(", orginHighJson=").append(orginHighJson);
        sb.append(", orginColumnJson=").append(orginColumnJson);
        sb.append(", orginRunSchema=").append(orginRunSchema);
        sb.append(", destType=").append(destType);
        sb.append(", destIntgModelId=").append(destIntgModelId);
        sb.append(", destDatasourceName=").append(destDatasourceName);
        sb.append(", destDatasourceId=").append(destDatasourceId);
        sb.append(", destPluginPath=").append(destPluginPath);
        sb.append(", destBaseJson=").append(destBaseJson);
        sb.append(", destAdvJson=").append(destAdvJson);
        sb.append(", schedulerExpr=").append(schedulerExpr);
        sb.append(", destHighJson=").append(destHighJson);
        sb.append(", destColumnJson=").append(destColumnJson);
        sb.append(", routeStrategy=").append(routeStrategy);
        sb.append(", blockStrategy=").append(blockStrategy);
        sb.append(", childJobid=").append(childJobid);
        sb.append(", executorTimeout=").append(executorTimeout);
        sb.append(", executorFailRetryCount=").append(executorFailRetryCount);
        sb.append(", hasDelete=").append(hasDelete);
        sb.append(", jobMode=").append(jobMode);
        sb.append(", fieldMappingJson=").append(fieldMappingJson);
        sb.append(", orginPluginId=").append(orginPluginId);
        sb.append(", destPluginId=").append(destPluginId);
        sb.append("]");
        return sb.toString();
    }
}