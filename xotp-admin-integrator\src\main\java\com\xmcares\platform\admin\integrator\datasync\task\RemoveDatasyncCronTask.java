package com.xmcares.platform.admin.integrator.datasync.task;

import com.xmcares.platform.admin.integrator.datasync.service.DatasyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/17 15:07
 */
@Component
public class RemoveDatasyncCronTask {

    private static final Logger LOG = LoggerFactory.getLogger("CRON_DATASYNC");
    private static final int MAX_TIMER = 30 * 60 * 1000;
    private static final int RUN_SLEEP = 2000;
    private static final String TASK_NAME = "自动删除数据同步定义信息";
    @Autowired
    private DatasyncService datasyncService;

    /**
     * 自动删除数据同步定义信息
     * 每隔1个小时执行一次
     */
    @Scheduled(cron = "0 0 */1 * * ?")
    public void removeDatasync() {
        new RemoveDataCronTask<>(LOG, TASK_NAME, datasyncService, RUN_SLEEP, MAX_TIMER).execute();
    }

}
