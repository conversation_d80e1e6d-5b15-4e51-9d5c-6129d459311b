/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/10
 */
package com.xmcares.platform.admin.common.datasource.jdbc;

import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;
import java.util.logging.Logger;

/**
 * javax.sql.DataSource数据源包装类
 * <AUTHOR>
 * @since 1.0.0
 */
public class JdbcDataSourceWrapper implements JdbcDataSource {
    /**
     * 数据源唯一名称
     */
    private final String name;
    private final javax.sql.DataSource dataSource;

    public JdbcDataSourceWrapper(String name, javax.sql.DataSource dataSource) {
        if (name == null || name.isEmpty()) {
            throw new IllegalArgumentException("id must not be null or empty");
        }
        if (dataSource == null) {
            throw new IllegalArgumentException("dataSource must not be null");
        }
        this.name = name;
        this.dataSource = dataSource;
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public AvailableStatus testAvailable() {
        boolean available = false;
        try (Connection connection = dataSource.getConnection()) {
            // 可执行简单查询来确认连接有效性
            available = connection.isValid(5); // 5秒超时
        } catch (SQLException e) {
            return new AvailableStatus(available, e.getMessage());
        }
        return new AvailableStatus(available, null);
    }

    @Override
    public Connection getConnection() throws SQLException {
        return this.dataSource.getConnection();
    }

    @Override
    public Connection getConnection(String username, String password) throws SQLException {
        return this.dataSource.getConnection(username, password);
    }

    @Override
    public <T> T unwrap(Class<T> iface) throws SQLException {
        return this.dataSource.unwrap(iface);
    }

    @Override
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        return this.dataSource.isWrapperFor(iface);
    }

    @Override
    public PrintWriter getLogWriter() throws SQLException {
        return this.dataSource.getLogWriter();
    }

    @Override
    public void setLogWriter(PrintWriter out) throws SQLException {
        this.dataSource.setLogWriter(out);
    }

    @Override
    public void setLoginTimeout(int seconds) throws SQLException {
        this.dataSource.setLoginTimeout(seconds);
    }

    @Override
    public int getLoginTimeout() throws SQLException {
        return this.dataSource.getLoginTimeout();
    }

    @Override
    public Logger getParentLogger() throws SQLFeatureNotSupportedException {
        return this.dataSource.getParentLogger();
    }

    public javax.sql.DataSource getRawDataSource() {
        return dataSource;
    }

    @Override
    public void close() throws Exception {
        if (this.dataSource instanceof AutoCloseable) {
            ((AutoCloseable)this.dataSource).close();
        }
    }

}
