package com.xmcares.platform.admin.dataservice.dataset.service;

import com.xmcares.platform.admin.dataservice.dataset.config.OpenApiProperties;
import com.xmcares.platform.admin.dataservice.dataset.repository.DatasetPublishedRepository;
import com.xmcares.platform.admin.dataservice.dataset.repository.ServiceRepository;
import com.xmcares.platform.admin.dataservice.dataset.vo.MetaDatasetVO;
import com.xmcares.platform.admin.metadata.common.model.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 数据集发布服务，用于管理已发布的数据集操作
 * 
 * <AUTHOR> <<EMAIL>>
 * @version 1.0
 * @since 2025-05-19
 */
@Service
public class DatasetPublishedService {

    private static final Logger LOG = LoggerFactory.getLogger(DatasetPublishedService.class);

    @Autowired
    private DatasetPublishedRepository datasetPublishedRepository;

    @Autowired
    private ServiceRepository serviceRepository;

    @Autowired
    private OpenApiProperties openApiProperties;

    /**
     * 根据数据集ID获取已发布的数据集列表
     *
     * @param datasetId 要查询的数据集ID
     * @return 包含数据服务列表的结果对象
     */
    public Result<List<MetaDatasetVO>> getPublishedDatasets(String datasetId) {
        boolean success = true;
        String message = "";
        List<MetaDatasetVO> data = null;
        try {
            data = datasetPublishedRepository.getByDatasetId(datasetId);
            if (data != null) {
                for (MetaDatasetVO vo : data) {
                    vo.setServiceApi(openApiProperties.getUrl() + vo.getServiceUri());
                }
            }
            if (data == null) {
                success = false;
                message = "未找到ID为" + datasetId + "的数据服务";
            }
        } catch (Exception e) {
            success = false;
            message = e.toString();
            LOG.error("获取数据服务失败", e);
        }
        return new Result<>(success, message, data);
    }

    /**
     * 创建新的数据服务
     *
     * @param datasetVO   原始数据集信息
     * @param serviceId   发布服务的ID
     * @param datasetCode 发布版本的数据集代码
     * @return 返回操作成功或失败的结果
     */
    public Result<Boolean> createPublishedDataset(MetaDatasetVO datasetVO) {
        boolean success = true;
        String message = "";
        try {
            success = createService(datasetVO);
            if (!success) {
                message = "创建数据服务失败";
            }
        } catch (Exception e) {
            success = false;
            message = e.toString();
            LOG.error("创建数据服务出错", e);
        }
        return new Result<>(success, message, success);
    }

    @Transactional
    public boolean createService(MetaDatasetVO datasetVO) throws Exception {
        String serviceUri = openApiProperties.getBasePath() + "/" + datasetVO.getDatasetCode();
        String serviceId = serviceRepository.createService(datasetVO.getDatasetName(), datasetVO.getDatasetCode(),
                serviceUri);
        if (null == serviceId) {
            throw new Exception("createService 失败");
        } else {
            int result = datasetPublishedRepository.createDatasetPublished(datasetVO, serviceId,
                    datasetVO.getDatasetCode());

            return result > 0;
        }
    }

    /**
     * 检查数据集代码是否已存在
     *
     * @param datasetCode 要检查的数据集代码
     * @return 如果代码唯一返回true的结果，否则返回false的结果
     */
    public Result<Boolean> checkDatasetCodeUnique(String datasetCode) {
        boolean success = true;
        String message = "";
        Boolean isUnique = false;
        try {
            Integer count = datasetPublishedRepository.checkDatasetCode(datasetCode);
            isUnique = count != null && count == 0;
            if (count == null || count < 0) {
                success = false;
                message = "检查数据集代码唯一性时发生错误";
            }
        } catch (Exception e) {
            success = false;
            message = e.toString();
            LOG.error("检查数据集代码时出错", e);
        }
        return new Result<>(success, message, isUnique);
    }

    public boolean checkServiceId(String serviceId) {
        try {
            Integer count = datasetPublishedRepository.checkServiceId(serviceId);
            return count != null && count > 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 上下线服务
     *
     * @param id
     * @param published 1 上线 0 下线
     * @return 返回操作成功或失败的结果
     */
    public Result<Boolean> updatePublished(String id, int published) {
        boolean success = true;
        String message = "";
        try {
            success = updatePublishedByCode(id, published);
            if (!success) {
                message = "停用ID为" + id + "的上下线失败";
            }
        } catch (Exception e) {
            success = false;
            message = e.toString();
            LOG.error("上下线出错", e);
        }
        return new Result<>(success, message, success);
    }

    @Transactional
    public boolean updatePublishedByCode(String datasetCode, int published) {
        if (published != 1) {
            published = 0;
        }
        boolean success = serviceRepository.updateStatusByName(datasetCode, published);
        if (success) {
            success = datasetPublishedRepository.updatePublishedByDatasetCode(datasetCode, published);
        }

        return success;
    }

    public Result<Boolean> deletePublishedDataset(String id) {
        boolean success = false;
        String message = "";
        try {
            MetaDatasetVO datasetVO = datasetPublishedRepository.getDatasetById(id);
            if (datasetVO != null) {
                int count = serviceRepository.deleteService(datasetVO.getServiceId());
                if (count > 0) {
                    success = datasetPublishedRepository.deleteById(id);
                    if (!success) {
                        message = "删除数据集失败";
                    }
                } else {
                    message = "删除服务失败";
                }
            } else {
                message = "未找到ID为" + id + "的数据集";
            }
        } catch (Exception e) {
            message = e.toString();
            LOG.error("删除已发布数据集出错", e);
        }
        return new Result<>(success, message, success);
    }
}
