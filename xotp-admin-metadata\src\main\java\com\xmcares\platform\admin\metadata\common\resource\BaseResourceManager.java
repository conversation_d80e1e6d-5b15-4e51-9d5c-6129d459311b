package com.xmcares.platform.admin.metadata.common.resource;


import java.io.InputStream;
import java.io.OutputStream;

public abstract class BaseResourceManager<P> implements IResourceManager<P> {

    protected IResourceHolder holder;
    protected P param;

    @Override
    public void init(IResourceHolder holder, P param) {
        this.holder = holder;
        this.param = param;
    }

    @Override
    public String addResource(String type, InputStream stream) {
        return null;
    }

    @Override
    public void removeResource(String type) {

    }

    @Override
    public void downloadResource(String type, OutputStream stream) {

    }

    @Override
    public String findPathByType(String type) {
        return null;
    }
}
