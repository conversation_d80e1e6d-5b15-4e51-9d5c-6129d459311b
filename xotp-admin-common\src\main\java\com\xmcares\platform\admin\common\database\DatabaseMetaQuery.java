package com.xmcares.platform.admin.common.database;

import com.xmcares.platform.admin.common.database.metainfo.ColumnInfo;
import com.xmcares.platform.admin.common.database.metainfo.TableInfo;

import java.util.List;

/**
 * DatabaseMetaQuery接口用于查询数据库元信息。
 * 它提供了一系列方法来获取数据库中的表信息和列信息。
 * 该接口主要用于抽象数据库元数据查询的功能，以便在不同的数据库实现之间进行切换。
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public interface DatabaseMetaQuery {

    /**
     * 获取schema下一系列表的表信息。
     *
     * @return 返回一个TableInfo对象列表，每个对象包含一个表的元信息。
     */
    List<TableInfo> getTableInfos();

    /**
     * 获取数据库schema下指定表的表信息。
     *
     * @param tableName 表名，用于指定需要获取信息的表。
     * @return 返回一个TableInfo对象，包含表的元信息。
     */
    TableInfo getTableInfo(String tableName);


    default TableInfo getTableInfoWithColumns(String tableName) {
        TableInfo result = this.getTableInfo(tableName);
        if (result != null) {
            List<ColumnInfo> columnInfos = this.getColumnInfos(tableName);
            result.setColumns(columnInfos);
        }
        return result;
    }


    /**
     * 获取schema下指定表的所有列信息。
     *
     * @param tableName 表名称，用于指定需要获取列信息的表。
     * @return 返回一个ColumnInfo对象列表，每个对象包含一个列的元信息。
     */
    List<ColumnInfo> getColumnInfos(String tableName);

    /**
     * 获取指定表中指定列的最大值。
     * 这个方法主要用于在执行某些数据迁移或同步操作时，确定数据的起始点。
     *
     * @param tableName 表名称，用于指定需要查询的表。
     * @param columnName 列名称，用于指定需要查询列的最大值。
     * @return 返回一个字符串，表示指定列的最大值。
     */
    Object getColumnMaxValue(String tableName, String columnName);


    default boolean check() {
        //TODO 测试连接
        return true;
    }

    default void close(){

    }

}
