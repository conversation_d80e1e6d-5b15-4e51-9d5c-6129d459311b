{
    "name": "hdfswriter",
    "parameter": {
        "writeMode": "${dest.writeMode}",
        "defaultFS": "${dest.defaultFS}",
        "fileType": "${dest.fileType}",
        "path": "${dest.path}",
        "fileName": "${dest.fileName}",
        "fieldDelimiter": "${dest.fieldDelimiter}",
        "column": [
            <#list destColumns! as column>
                {
                "name": "${column.name}",
                "type": "${column.type}"
                }<#if column_has_next>,</#if>
            </#list>
        ],
        <#if dest.compress??>"compress":"${dest.compress}",</#if>
        <#if dest.encoding??>"encoding":"${dest.encoding}",</#if>
        <#if dest.hadoopConfig?? && (dest.hadoopConfig.size > 0)>
            "hadoopConfig":{
                <#list dest.hadoopConfig as config>
                    "${config.key}":"${config.value}"<#if config_has_next>,</#if>
                </#list>
            },
        </#if>
        <#if destDatasource.enableKerberos?? && destDatasource.enableKerberos = '1'>
            "haveKerberos":true,
            "kerberosKeytabFilePath": "<#noparse>${ROOT_PATH}/DATABASE_RESOURCE/${keytabPath}</#noparse>",
            "kerberosPrincipal": "${destDatasource.kerberosPrincipal}",
        </#if>
    }
}