package com.xmcares.platform.admin.integrator.common.config;

/**
 * XxljobClientFeignConfiguration
 *
 * <AUTHOR>
 * @Descriptions XxljobClientFeignConfiguration
 * @Date 2025/5/19 13:51
 */

import feign.RequestInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class XxljobClientFeignConfiguration {

    @Bean
    public RequestInterceptor xxlJobFeignRequestInterceptor() {
        return new XxlJobFeignRequestInterceptor();
    }
}
