package com.xmcares.platform.admin.metadata.database.repository;

import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.platform.admin.metadata.database.model.DatasourceResourceSyncTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据源资源同步任务仓库
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/9
 */
@Repository
public class DatasourceResourceSyncTaskRepository {

    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    public boolean add(DatasourceResourceSyncTask syncTask) {
        Map<String, Object> map = DBUtils.insertSqlAndObjects(syncTask, DatasourceResourceSyncTask.class, DatasourceResourceSyncTask.TABLE);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.update(sql, args) > 0;
    }

    public int findNeedHandlers() {
        String[] args = new String[2];
        args[0] = "1";
        args[1] = "2";
        String sqlBuilder = "SELECT COUNT(*) FROM " + DatasourceResourceSyncTask.TABLE + " WHERE `type` IN (?, ?)";
        return xcfJdbcTemplate.queryForObject(sqlBuilder, Integer.class, args);
    }

    public List<DatasourceResourceSyncTask> findAll() {
        Map<String, Object> map = DBUtils.queryList(DatasourceResourceSyncTask.TABLE, new HashMap<>());
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntities(sql, args, DatasourceResourceSyncTask.class);
    }

    public void toRemove(String id) {
        xcfJdbcTemplate.update(
                "UPDATE " + DatasourceResourceSyncTask.TABLE + " SET `type` = '2' WHERE id = ?",
                id
        );
    }

    public void removeAll(List<String> ids) {
        Object[] args = new Object[ids.size()];
        StringBuilder sqlBuilder = new StringBuilder("DELETE FROM ").append(DatasourceResourceSyncTask.TABLE).append(" WHERE id IN ");
        sqlBuilder.append("(").append("?");
        args[0] = ids.get(0);
        if (ids.size() > 1) {
            for (int i = 1; i < ids.size(); i++) {
                sqlBuilder.append(",").append("?");
                args[i] = ids.get(i);
            }
        }
        sqlBuilder.append(")");
        xcfJdbcTemplate.update(sqlBuilder.toString(), args);
    }

    public void toOk(List<String> ids) {
        Object[] args = new Object[ids.size()];
        StringBuilder sqlBuilder = new StringBuilder("UPDATE ").append(DatasourceResourceSyncTask.TABLE)
                .append(" SET `type` = '3' ")
                .append(" WHERE id IN ")
                .append("(").append("?");
        args[0] = ids.get(0);
        if (ids.size() > 1) {
            for (int i = 1; i < ids.size(); i++) {
                sqlBuilder.append(",").append("?");
                args[i] = ids.get(i);
            }
        }
        sqlBuilder.append(")");
        sqlBuilder.append(" AND `type` != '2' ");
        xcfJdbcTemplate.update(sqlBuilder.toString(), args);
    }
}
