{
    "name": "ximc_reader",
    "parameter": {
        "password": "${orginDatasource.password}",
        "name": "${orginDatasource.name}",
        "format": "${orgin.format}",
        "topic": "${orgin.topic}",
        "imccUrl": "${orginDatasource.imccUrl}",
        "username": "${orginDatasource.username}",
        "pulsarServiceUrl": "${pulsarConfig.pulsarServiceUrl}",
        "pulsarTopic": "${pulsarConfig.pulsarTopic}",
        "pulsarSubscription": "${pulsarConfig.pulsarSubscription}",
        "maxDuration": "${orgin.maxDuration}",
        "column": [
        <#list orginColumns! as column>
            {
            <#list column?keys as key>
                "${key}":"${column[key]}"<#if key_has_next>,</#if>
            </#list>
            }<#if column_has_next>,</#if>
        </#list>
        ]
    }
}
