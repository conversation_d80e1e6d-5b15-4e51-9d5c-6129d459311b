package com.xmcares.platform.admin.metadata.database.repository;

import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.metadata.database.model.DatasourceColumn;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/18 15:23
 */
@Repository
public class DatasourceColumnRepository {
    public static final String DATASOURCE_COLUMN = "bdp_meta_datatable_column";
    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    public void batchSave(List<DatasourceColumn> datasourceColumns) {
        List<Object[]> params = new ArrayList<>();
        String sql = null;
        for (DatasourceColumn datasourceColumn : datasourceColumns) {
            Map<String, Object> map = DBUtils.insertSqlAndObjects(datasourceColumn, DatasourceColumn.class, DATASOURCE_COLUMN);
            params.add(DBUtils.getObjects(map));
            if (sql == null) {
                sql = DBUtils.getSql(map);
            }
        }
        assert sql != null;
        xcfJdbcTemplate.batchUpdate(sql, params);
    }

    public void removeByTableIds(List<String> tableIds) {
        StringBuilder sqlBuild = new StringBuilder("DELETE FROM ");
        sqlBuild.append(DATASOURCE_COLUMN).append(" WHERE ").append(" datatable_id ").append(" IN ").append("(").append("?");
        if (tableIds.size() > 1) {
            for (int i = 1; i < tableIds.size(); i++) {
                sqlBuild.append(",").append("?");
            }
        }
        sqlBuild.append(")");
        xcfJdbcTemplate.update(sqlBuild.toString(), tableIds.toArray(new Object[]{}));
    }


    public List<DatasourceColumn> listDatasourceColumns(String datasourceTableId) {
        List<DatasourceColumn> datasourceColumns = xcfJdbcTemplate.queryForEntities(
                "SELECT * FROM " + DATASOURCE_COLUMN + " WHERE  datatable_id = ?",
                new Object[]{datasourceTableId},
                DatasourceColumn.class);
        return datasourceColumns;
    }



    public List<DatasourceColumn> listDatasourceColumnsByName(String datasourceTableName, String dataSourceId) {
        List<DatasourceColumn> datasourceColumns = xcfJdbcTemplate.queryForEntities(
                "SELECT * FROM " + DATASOURCE_COLUMN + " WHERE datatable_name = ? AND datasource_id = ?",
                new Object[]{datasourceTableName, dataSourceId},
                DatasourceColumn.class);
        return datasourceColumns;
    }

    public int deleteDatasourceColumns(String datasourceTableId) {

        int deleteColumn = xcfJdbcTemplate.update(
                "DELETE  FROM " + DATASOURCE_COLUMN + " WHERE datatable_id = ? ",
                datasourceTableId);
        return deleteColumn;
    }

    public boolean saveColumns(String datasourceTableId, List<DatasourceColumn> datasourceColumns) {
        try {
            int deleteColumn = deleteDatasourceColumns(datasourceTableId);
            String sql = "INSERT INTO " + DATASOURCE_COLUMN + " (" +
                    "id, datasource_id,datatable_id,datatable_name," +
                    "alisa,`name`,`type`, `length`," +
                    "accuracy,allow_empty,column_key,has_system,remark) " +
                    "VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?) ";
            ArrayList<Object[]> params = new ArrayList<>();
            for (DatasourceColumn datasourceColumn : datasourceColumns) {
                Object[] objects = {
                        SnowflakeGenerator.getNextId(), datasourceColumn.getDatasourceId(),
                        datasourceColumn.getDatatableId(),datasourceColumn.getDatatableName(),
                        datasourceColumn.getAlisa(),datasourceColumn.getName(),
                        datasourceColumn.getType(),datasourceColumn.getLength(),
                        datasourceColumn.getAccuracy(), datasourceColumn.getAllowEmpty(),
                        datasourceColumn.getColumnKey(),datasourceColumn.getHasSystem(),
                        datasourceColumn.getRemark()
                };
                params.add(objects);
            }
            xcfJdbcTemplate.batchUpdate(sql, params);

            return true;
        } catch (Exception e) {
            return false;
        }
    }
    public List<DatasourceColumn> listSyncDatasourceColumn(String datasourceTableId) {
        return xcfJdbcTemplate.queryForEntities(
                "SELECT * FROM " + DATASOURCE_COLUMN + " WHERE  datatable_id = ? AND has_system = ? ",
                new Object[]{datasourceTableId, '0'},
                DatasourceColumn.class);
    }


}
