package com.xmcares.platform.admin.metadata.database.repository;

import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.metadata.database.model.DatasourceModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/2/18 16:37
 */
@Repository
public class DatasourceModelRepository {

    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;
    public static String TABLE_DATASOURCE_MODEL = "bdp_meta_datasource_model";

    public List<DatasourceModel> queryList(DatasourceModel datasourceModel) {
        Map<String, Object> conditions = buildCondition(datasourceModel);
        Map<String, Object> map = DBUtils.queryList(TABLE_DATASOURCE_MODEL, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntities(sql, args, DatasourceModel.class);
    }

    private Map<String, Object> buildCondition(DatasourceModel datasourceModel) {
        Map<String, Object> conditions = new HashMap<>();
        if (datasourceModel != null) {
            if (datasourceModel.getType() != null && !datasourceModel.getType().equals("")) {
                conditions.put("type", datasourceModel.getType());
            }
            if (datasourceModel.getCategory() != null && !datasourceModel.getCategory().equals("")) {
                conditions.put("category", datasourceModel.getCategory());
            }
        }
        return conditions;
    }

    public DatasourceModel get(String id) {
        HashMap<String, Object> conditions = new HashMap<>();
        conditions.put("id", String.valueOf(id));
        Map<String, Object> map = DBUtils.queryList(TABLE_DATASOURCE_MODEL, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntity(sql, args, DatasourceModel.class);
    }

    public DatasourceModel getByType(String type) {
        HashMap<String, Object> conditions = new HashMap<>();
        conditions.put("type", type);
        Map<String, Object> map = DBUtils.queryList(TABLE_DATASOURCE_MODEL, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntity(sql, args, DatasourceModel.class);
    }

    public Boolean addDataSourceModel(DatasourceModel datasourceModel) {
        datasourceModel.setId(SnowflakeGenerator.getNextId() + "");
        datasourceModel.setCreateTime(new java.sql.Date(System.currentTimeMillis()));
        datasourceModel.setUpdateTime(new java.sql.Date(System.currentTimeMillis()));
        Map<String, Object> map = DBUtils.insertSqlAndObjects(datasourceModel, DatasourceModel.class, TABLE_DATASOURCE_MODEL);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.update(sql, args) > 0;
//
//
//        Long id = SnowflakeGenerator.getNextId();
//        int isInsertOk = xcfJdbcTemplate.update(
//                "INSERT INTO " + TABLE_DATASOURCE_MODEL + "(id, `type`, create_time, update_time, create_user, template, category) VALUES (?, ?, ?, ?, ?, ?, ?)",
//                id,
//                datasourceModel.getType(),
//                new Date(),
//                new Date(),
//                datasourceModel.getCreateUser(),
//                datasourceModel.getTemplate(),
//                datasourceModel.getCategory()
//        );
//        if (isInsertOk == 1) {
//            return id;
//        } else {
//            return -1L;
//        }
    }

    public Boolean updateDatasourceModel(DatasourceModel datasourceModel) {
        Map<String, Object> map = DBUtils.updateSqlAndObjects("id", datasourceModel, DatasourceModel.class, TABLE_DATASOURCE_MODEL);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.update(sql, args) > 0;
//
//
//        return xcfJdbcTemplate.update(
//                "UPDATE  " + TABLE_DATASOURCE_MODEL + " SET `type` = ?, remark = ? ,template = ?, category = ?, update_time = ? WHERE id = ?",
//                datasourceModel.getType(),
//                datasourceModel.getRemark(),
//                datasourceModel.getTemplate(),
//                datasourceModel.getCategory(),
//                new Date(),
//                datasourceModel.getId()
//        );
    }

    public int removeDatasourceModel(String id) {
        return xcfJdbcTemplate.update("DELETE FROM " + TABLE_DATASOURCE_MODEL + " where id = ?", id);
    }

    public List<String> findSupportTypes() {
        String sql = "SELECT type FROM " + TABLE_DATASOURCE_MODEL;
        return xcfJdbcTemplate.queryForList(sql, String.class);
    }

    public List<DatasourceModel> querySimpleList() {
        String sql = "SELECT id, type FROM " + TABLE_DATASOURCE_MODEL;
        return xcfJdbcTemplate.queryForEntities(sql, new Object[]{}, DatasourceModel.class);
    }
}
