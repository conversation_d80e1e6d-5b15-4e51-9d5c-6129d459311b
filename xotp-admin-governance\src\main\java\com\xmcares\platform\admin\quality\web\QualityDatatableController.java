package com.xmcares.platform.admin.quality.web;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.platform.admin.quality.model.QltyDim;
import com.xmcares.platform.admin.quality.model.QltyRule;
import com.xmcares.platform.admin.quality.model.QltyRuleScheduler;
import com.xmcares.platform.admin.quality.model.vo.QualityDatatableListQueryVO;
import com.xmcares.platform.admin.quality.service.QualityDatatableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/25 09:10
 */
@Api(value = "表质量规则配置服务")
@Validated
@RestController
@RequestMapping("/quality/datatable")
public class QualityDatatableController {

    @Autowired
    QualityDatatableService qualityDatatableService;

    @ApiOperation("查询库-表中规则配置的概览信息")
    @PostMapping("/list-query")
    @ResponseBody
    public Page<QualityDatatableListQueryVO> listQuery(
             String datawareId,
             String datatableId,
             String datatableAlias,
            @RequestParam(name = "pageNo") Integer pageNo,
            @RequestParam(name = "pageSize") Integer pageSize
    ) {
        return qualityDatatableService.listQuery(datawareId, datatableId, datatableAlias, pageNo, pageSize);
    }

    @ApiOperation("查询表的质量规则明细")
    @PostMapping("/rule/list-query")
    @ResponseBody
    public Page<QltyRule> ruleListQuery(
            String datatableId,
            String ruleName,
            String ruleLevel,
            String dimCode,
            @RequestParam(name = "pageNo") Integer pageNo,
            @RequestParam(name = "pageSize") Integer pageSize
    ) {
        return qualityDatatableService.ruleListQuery(datatableId, ruleName, ruleLevel, dimCode, pageNo, pageSize);
    }

    @ApiOperation("查询表的质量规则明细")
    @PostMapping("/rule/all-query")
    @ResponseBody
    public List<QltyRule> ruleAllQuery(
    ) {
        return qualityDatatableService.ruleAllQuery();
    }

    @ApiOperation("查询质量维度")
    @PostMapping("/dim/all-query")
    @ResponseBody
    public List<QltyDim> dimAllQuery(
    ) {
        return qualityDatatableService.dimAllQuery();
    }

    @ApiOperation("新增规则到表上")
    @PostMapping("/rule/add")
    @ResponseBody
    public String ruleAdd(@RequestBody QltyRule qltyRule) {
        return qualityDatatableService.ruleAdd(qltyRule);
    }

    @ApiOperation("查询表规则的调度信息")
    @PostMapping("/scheduler/all-query")
    @ResponseBody
    public List<QltyRuleScheduler> schedulerAllQuery(
            String datawareId,
            String datatableId
    ) {
        return qualityDatatableService.schedulerAllQuery(datawareId, datatableId);
    }

    @ApiOperation("配置表规则的调度信息")
    @PostMapping("/scheduler/save")
    @ResponseBody
    public String schedulerSave(@RequestBody QltyRuleScheduler qltyRuleScheduler) {
        return qualityDatatableService.schedulerSave(qltyRuleScheduler);
    }

    @ApiOperation("修改规则")
    @PostMapping("/rule/update")
    @ResponseBody
    public String ruleUpdate(@RequestBody QltyRule qltyRule) {
        return qualityDatatableService.ruleUpdate(qltyRule);
    }

    @ApiOperation("删除表规则")
    @PostMapping("/rule/delete")
    @ResponseBody
    public String ruleDelete(
            @RequestParam(name = "ruleId") String ruleId
    ) {
        return qualityDatatableService.ruleDelete(ruleId);
    }

    @ApiOperation("控制调度中规则的启用或停止")
    @PostMapping("/rule/control")
    @ResponseBody
    public String ruleControl(
            @RequestParam(name = "ruleId") String ruleId,
            @RequestParam(name = "disabled") String disabled
    ) {
        return qualityDatatableService.ruleControl(ruleId, disabled);
    }

    @ApiOperation("试运行表规则一次")
    @PostMapping("/rule/test-run")
    @ResponseBody
    public String ruleTestRun(
            @RequestParam(name = "ruleIds") String[] ruleIds
    ) {
        for (String ruleId : ruleIds) {
            ruleControl(ruleId, "0");
        }
        return qualityDatatableService.ruleTestRun(ruleIds);
    }

    @ApiOperation("控制标表规则调度是否启用")
    @PostMapping("/scheduler/run")
    @ResponseBody
    public String schedulerRun(
            @RequestParam(name = "datawareId") String datawareId,
            @RequestParam(name = "datatableId") String datatableId,
            @RequestParam(name = "started") String started
    ) {
        return qualityDatatableService.schedulerRun(datawareId, datatableId, started);
    }
}
