package com.xmcares.platform.admin.quality.service;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.platform.admin.quality.model.QltyRuleTmpl;
import com.xmcares.platform.admin.quality.repository.QualityRuleTmplRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/25 09:10
 */
@Service
public class QualityRuleTmplService {

    @Autowired
    QualityRuleTmplRepository qualityRuleTmplRepository;

    public Page<QltyRuleTmpl> pageQuery(String ruleName, String ruleLevel, String dimCode, Integer pageNo, Integer pageSize) {
        return qualityRuleTmplRepository.pageQuery(ruleName, ruleLevel, dimCode, pageNo, pageSize);
    }

    public List<QltyRuleTmpl> allQuery() {
        return qualityRuleTmplRepository.allQuery();
    }

}
