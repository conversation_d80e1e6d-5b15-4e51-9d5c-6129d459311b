package com.xmcares.platform.admin.integrator;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * <AUTHOR> chenYG
 * @date : 2022/3/29 11:11
 */
@ConfigurationProperties(prefix = "xbdp.integrator")
public class IntegratorProperties {

    /**
     * 数据集成模块的资源根路径，默认为/xbdp/integrator
     */
    private String fileServerRoot = "/xbdp/integrator";

    /** 本地临时文件的根目录，默认为${user.dir}/tmp/integrator */
    private String localTmpRoot = System.getProperty("user.dir")+ "/tmp/integrator";

    /**
     * 调度器相关配置
     */
    @NestedConfigurationProperty
    private XxlJobProperties xxlJob = new XxlJobProperties();

    /**
     * datax文件相关配置
     */
    @NestedConfigurationProperty
    private DataxProperties datax = new DataxProperties();



    public String getFileServerRoot() {
        return fileServerRoot;
    }

    public void setFileServerRoot(String fileServerRoot) {
        this.fileServerRoot = fileServerRoot;
    }

    public String getLocalTmpRoot() {
        return localTmpRoot;
    }

    public void setLocalTmpRoot(String localTmpRoot) {
        this.localTmpRoot = localTmpRoot;
    }

    public XxlJobProperties getXxlJob() {
        return xxlJob;
    }

    public void setXxlJob(XxlJobProperties xxlJob) {
        this.xxlJob = xxlJob;
    }

    public DataxProperties getDatax() {
        return datax;
    }

    public void setDatax(DataxProperties datax) {
        this.datax = datax;
    }

    /**
     * <AUTHOR> 修改为内嵌类，修改hookbox属性名为group，即数据同步任务的xxl-job group name
     * <AUTHOR> chenYG
     * @date : 2022/3/29 11:11
     */
    public static class XxlJobProperties {

        /** 调度任务执行时的告警邮箱 */
        private String alarmEmail;
        /** 勾盒服务应用的名称 */
        private String group = "integrator";

        public String getAlarmEmail() {
            return alarmEmail;
        }

        public void setAlarmEmail(String alarmEmail) {
            this.alarmEmail = alarmEmail;
        }

        public String getGroup() {
            return group;
        }

        public void setGroup(String group) {
            this.group = group;
        }
    }

    /**
     * DataX相关配置
     * <AUTHOR> 修改为内嵌类，并改名为DataX配置
     * <AUTHOR> chenYG
     * @date : 2022/3/30 9:52
     */
    public static class DataxProperties {


    }
}
