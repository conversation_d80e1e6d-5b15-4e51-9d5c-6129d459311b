package com.xmcares.platform.admin.metadata.database.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xmcares.platform.admin.common.validation.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/9/21 15:01
 */
@ApiModel(value = DatasourceResource.TABLE, description = "数据源资源")
public class DatasourceResource implements Serializable {
    public static final String TABLE = "bdp_meta_datasource_resource";
    /**
     * ID
     */
    @NotNull(message = "ID不允许为空", groups = Update.class)
    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "资源名称")
    private String name;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "资源路径")
    private String path;

    @ApiModelProperty(value = "上传用户")
    private String uploadUser;

    @ApiModelProperty(value = "标记")
    private String remark;

    @ApiModelProperty(value = "使用次数")
    private Integer useCount;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getUploadUser() {
        return uploadUser;
    }

    public void setUploadUser(String uploadUser) {
        this.uploadUser = uploadUser;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getUseCount() {
        return useCount;
    }

    public void setUseCount(Integer useCount) {
        this.useCount = useCount;
    }
}
