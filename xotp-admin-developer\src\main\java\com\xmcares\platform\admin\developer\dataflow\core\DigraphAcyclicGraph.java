package com.xmcares.platform.admin.developer.dataflow.core;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chenYG
 * @date : 2021/9/27 16:20
 */
public class DigraphAcyclicGraph<T extends IDAGNode> {
    /** 所有节点 */
    private Map<String, T> nodes = new HashMap<>();
    /** 节点之间的逆临连接表 */
    private Map<String, Set<String>> nlTable = new HashMap<>();
    /** 拓扑排序结果集 */
    private List<String> tpTable = new ArrayList<>();
    /** 执行顺序结果集 */
    private Map<Integer, Set<String>> indexTable = new HashMap<>();
    /** 最大路由深度 */
    private Integer maxRoute = -1;
    /** 是否有向无环图 */
    private boolean isDAG = false;


    public  static <R extends IDAGNode>  DigraphAcyclicGraph<R> create(List<R> nodes) {
        return new DigraphAcyclicGraph<>(nodes);
    }

    /**
     * 从逆鳞链表中获取入度为0的节点, 并且对逆临连接表进行重置
     * @param graph 逆鳞链表
     * @return 入度为0的节点
     */
    private Set<String> findZeroInDegreeNodeIdAndResetGraph(Map<String, Set<String>> graph) {
        if (graph.isEmpty()) {
            return new HashSet<>();
        }
        Set<String> resultNodes = graph.entrySet().stream().filter(e->e.getValue().isEmpty()).map(Map.Entry::getKey).collect(Collectors.toSet());
        if (resultNodes.isEmpty()) {
            return null;
        }
        for (String nodeId : resultNodes) {
            graph.remove(nodeId);
        }
        for (Set<String> value : graph.values()) {
            for (String nodeId : resultNodes) {
                value.remove(nodeId);
            }
        }
        return resultNodes;
    }

    public DigraphAcyclicGraph(List<T> nodes) {
        // 构建节点和逆临链接表
        Map<String, Set<String>> graph = new HashMap<>();
        for (T node : nodes) {
            this.nodes.put(node.nodeId(), node);
            graph.put(node.nodeId(), new HashSet<>());
            nlTable.put(node.nodeId(), new HashSet<>());
        }
        // 构建逆临链接表
        for (T node : nodes) {
            if (CollectionUtils.isNotEmpty(node.nextNodes())) {
                for (String nodeId : node.nextNodes()) {
                    graph.get(nodeId).add(node.nodeId());
                    nlTable.get(nodeId).add(node.nodeId());
                }
            }
        }
        // Kahn算法
        Set<String> findNodes = findZeroInDegreeNodeIdAndResetGraph(graph);
        if (CollectionUtils.isEmpty(findNodes)) {
            // 说明该图不是有向无环图
            return;
        }
        int index = 0;
        Queue<String> zeroQueue = new LinkedList<>(findNodes);
        Map<Integer, Set<String>> indexResult = new HashMap<>();
        indexResult.put(index, findNodes);
        List<String> sortResult = new ArrayList<>();
        while (!zeroQueue.isEmpty()) {
            String nodeId = zeroQueue.poll();
            sortResult.add(nodeId);
            findNodes = findZeroInDegreeNodeIdAndResetGraph(graph);
            if (findNodes == null) {
                return;
            }
            if (findNodes.isEmpty()) { continue; }
            index ++;
            zeroQueue.addAll(findNodes);
            indexResult.put(index, findNodes);
        }
        this.indexTable = indexResult;
        this.tpTable = sortResult;
        this.isDAG = true;
        // 获取最长链路
        int maxLen = -1;
        for (String nodeId : indexTable.get(0)) {
            int findResult = findMaxRoute(nodeId, 1);
            if (findResult > maxLen) {
                maxLen = findResult;
            }
        }
        maxRoute = maxLen;
        System.out.println(toString());
    }

    private int findMaxRoute(String nodeId, int depth) {
        int maxResult = depth;
        depth ++;
        for (Map.Entry<String, Set<String>> source : nlTable.entrySet()) {
            if (source.getValue().contains(nodeId)) {
                int findResult = findMaxRoute(source.getKey(), depth);
                if (maxResult < findResult) {
                    maxResult = findResult;
                }
            }
        }
        return maxResult;
    }

    /**
     * 是否有向无环图
     * @return
     */
    public boolean isDAG() {
        return isDAG;
    }

    public Integer maxRoute() {
        return maxRoute;
    }

    public Map<Integer, Set<String>> indexTable() {
        return indexTable;
    }

    @Override
    public String toString() {
        StringBuilder result = new StringBuilder("========================== 逆邻连接表 ==========================").append("\n");
        for (Map.Entry<String, Set<String>> source : nlTable.entrySet()) {
            result.append(source.getKey()).append(" :\t").append(source.getValue().isEmpty() ? "NULL" : StringUtils.join(source.getValue(),",")).append("\n");
        }
        result.append("========================== 额外的信息 ==========================").append("\n");
        result.append("最大深度").append(" :\t").append(maxRoute);
        return result.toString();
    }

}