package com.xmcares.platform.admin.asset.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/27 14:26
 */
@ApiModel(value = MetaCatalog.TABLE, description = "资产目录")
public class MetaCatalog {

    public static final String TABLE = "bdp_meta_catalog";

    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "资产编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "父级类目")
    private String parent_id;

    @ApiModelProperty(value = "排序（预留）")
    private int sort_no;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "仓库ID")
    private String dataware_id;

    @ApiModelProperty(value = "create_user")
    private String create_user;

    @ApiModelProperty(value = "create_time")
    private Date create_time;

    @ApiModelProperty(value = "update_user")
    private String update_user;

    @ApiModelProperty(value = "update_time")
    private Date update_time;

    public static String getTABLE() {
        return TABLE;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParent_id() {
        return parent_id;
    }

    public void setParent_id(String parent_id) {
        this.parent_id = parent_id;
    }

    public int getSort_no() {
        return sort_no;
    }

    public void setSort_no(int sort_no) {
        this.sort_no = sort_no;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDataware_id() {
        return dataware_id;
    }

    public void setDataware_id(String dataware_id) {
        this.dataware_id = dataware_id;
    }

    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_user() {
        return update_user;
    }

    public void setUpdate_user(String update_user) {
        this.update_user = update_user;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }
}
