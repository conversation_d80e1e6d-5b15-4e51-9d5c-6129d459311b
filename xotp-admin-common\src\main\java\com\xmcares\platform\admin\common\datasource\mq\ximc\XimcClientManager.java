/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/31
 */
package com.xmcares.platform.admin.common.datasource.mq.ximc;

import com.xmcares.imcc.IMCCManager;
import com.xmcares.imcc.IMXMPPManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class XimcClientManager {
    private static final Logger logger = LoggerFactory.getLogger(XimcClientManager.class);

    private static final Map<String, IMCCManager> xmppManagerMap = new ConcurrentHashMap<>();
    private static final Map<String, Set<String>> borrowerMap = new ConcurrentHashMap<>();

    /**
     * 获取消息中心XMPPManager
     * @param borrower 获取消息中心XMPPManager的源头
     * @param properties 消息中心配置
     * @return XMPPManager
     */
    public static IMCCManager borrowImcManager(String borrower, XimcProperties properties) {
        String uniqueKey = getUniqueKey(properties);
        // 原子初始化并复用 XMPPManager
        IMCCManager manager = xmppManagerMap.computeIfAbsent(uniqueKey, key -> {
            logger.info("初始创建消息中心XMPPManager[{}]", uniqueKey);
            IMXMPPManager newManager = new IMXMPPManager();
            newManager.setXMPPConfig(properties);
            return newManager;
        });
        // 原子初始化并添加 borrower
        borrowerMap.compute(uniqueKey, (key, borrowers) -> {
            logger.info("Borrower[{}]获取消息中心XMPPManager[{}]", borrower, uniqueKey);
            if (borrowers == null) {
                borrowers = Collections.synchronizedSet(new HashSet<>());
            }
            borrowers.add(borrower);
            return borrowers;
        });

        return manager;
    }

    /**
     * 释放消息中心XMPPManager
     * @param borrower 释放消息中心XMPPManager的源头
     * @param properties 消息中心配置
     */
    public static void releaseImcManager(String borrower, XimcProperties properties) {
        String uniqueKey = getUniqueKey(properties);
        borrowerMap.computeIfPresent(uniqueKey, (key, borrowers) -> {
            logger.info("Borrower[{}]释放消息中心XMPPManager[{}]", borrower, uniqueKey);
            borrowers.remove(borrower);
            // 若无更多借用者，则清理资源
            if (borrowers.isEmpty()) {
                borrowerMap.remove(uniqueKey); // 先移除记录，防止重复释放
                IMCCManager manager = xmppManagerMap.remove(uniqueKey);
                if (manager != null && manager.hasLogined()) {
                    logger.info("关闭无Borrower的消息中心XMPPManager[{}]连接", uniqueKey);
                    try {
                        manager.logout();
                    } catch (Exception e) {
                        logger.warn("Failed to logout XMPPManager[{}]: {}", uniqueKey, e.getMessage());
                    }
                }
                return null;
            }
            return borrowers;
        });
    }

   /**
     * ip:端口@用户名 作为唯一标识
     * @return 唯一标识
     */
   private static String getUniqueKey(XimcProperties properties) {
        return String.format("%s:%d@%s", properties.getServerHost(),
                properties.getServerPort(), properties.getUsername());
    }

}
