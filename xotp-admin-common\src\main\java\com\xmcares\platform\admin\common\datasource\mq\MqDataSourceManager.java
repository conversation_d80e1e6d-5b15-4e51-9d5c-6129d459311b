/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/24
 */
package com.xmcares.platform.admin.common.datasource.mq;

import com.xmcares.platform.admin.common.datasource.DataSource;
import com.xmcares.platform.admin.common.datasource.DataSourceManager;
import com.xmcares.platform.admin.common.datasource.DataSourceOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * JDBC 类型(包括SQL 、BIGDATA组的部分)的数据源管理
 * <AUTHOR>
 * @since 1.0.0
 */
public class MqDataSourceManager implements DataSourceManager<MqDataSource> {
    private static final Logger logger = LoggerFactory.getLogger(MqDataSourceManager.class);

    private final Map<String, MqDataSource> dataSources = new ConcurrentHashMap<>();

    public MqDataSourceManager() {
        this.addShutdownHook();
    }

    @Override
    public MqDataSource getOrCreateDataSource(DataSourceOptions options) {
        MqType type = MqType.fromTypeName(options.getTypeName());
        if (type == null) {
            throw new IllegalArgumentException("不支持的MQ组的类型: " + options.getTypeName());
        }
        return dataSources.computeIfAbsent(options.getName(), k -> type.createDataSource(options));
    }

    @Override
    public void destroyDataSource(String dataSourceName) {
        DataSource removed = dataSources.remove(dataSourceName);
        if (removed != null) {
            try {
                removed.close();
            } catch (Exception e) {
                logger.warn("销毁数据源[{}]失败:{}", dataSourceName, e.getMessage());
            }
        }
    }

    private void addShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            for (MqDataSource ds : dataSources.values()) {
                try {
                    ds.close();
                } catch (Exception e) {
                    logger.warn("关闭数据源[{}]失败: {}", ds.getName(), e.getMessage());
                }
            }
        }));
    }

}
