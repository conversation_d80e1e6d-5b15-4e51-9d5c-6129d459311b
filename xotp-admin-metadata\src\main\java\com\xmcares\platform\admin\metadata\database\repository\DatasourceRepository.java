package com.xmcares.platform.admin.metadata.database.repository;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/2/11 08:58
 */

@Repository
public class DatasourceRepository {
    public static String TABLE_DATASOURCE = "bdp_meta_datasource";
    public static String TABLE_DATASOURCE_RESOURCE = "bdp_meta_datasource_resource";

    public static final String TABLE_DATASYNC = "bdp_intg_datasync";

    private static final String COUNT_BY_DATASOURCE_ID = "SELECT COUNT(1) FROM " + TABLE_DATASYNC +
            " WHERE (orgin_type = '0' and orgin_datasource_id = ?) " +
            " or (dest_type = '0' and dest_datasource_id = ?)";

    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    /**
     * 查询id对应的数据源信息
     *
     * @param id
     * @return
     */
    public Datasource getDatasource(String id) {
        return xcfJdbcTemplate.queryForEntity(
                "SELECT * FROM " + TABLE_DATASOURCE + " WHERE id = ?",
                new Object[]{id}, Datasource.class
        );
    }


    //=======================================================================================================


    public boolean add(Datasource datasource) {
        Map<String, Object> map = DBUtils.insertSqlAndObjects(datasource, Datasource.class, TABLE_DATASOURCE);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.update(sql, args) > 0;
    }

    public boolean update(Datasource datasource) {
        Map<String, Object> map = DBUtils.updateSqlAndObjects("id", datasource, Datasource.class, TABLE_DATASOURCE);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.update(sql, args) > 0;
    }

    public boolean delete(String id) {
        String sql = DBUtils.deleteSql(TABLE_DATASOURCE, "id");
        xcfJdbcTemplate.update(sql, id);
        return true;
    }

    public Datasource get(String id) {
        HashMap<String, Object> conditions = new HashMap<>();
        conditions.put("id", String.valueOf(id));
        Map<String, Object> map = DBUtils.queryList(TABLE_DATASOURCE, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntity(sql, args, Datasource.class);
    }

    public Datasource getByName(String name) {
        HashMap<String, Object> conditions = new HashMap<>();
        conditions.put("name", name);
        Map<String, Object> map = DBUtils.queryList(TABLE_DATASOURCE, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntity(sql, args, Datasource.class);
    }

    public List<Datasource> queryPage(Datasource datasource, Page<Datasource> page) {
        Map<String, Object> conditions = buildCondition(datasource);
        Map<String, Object> map = DBUtils.queryList(TABLE_DATASOURCE, conditions);
        String sql = DBUtils.getSql(map) + " ORDER BY update_time DESC ";
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntities(sql, args, page, Datasource.class);
    }

    public List<Datasource> queryList(Datasource datasource) {
        Map<String, Object> conditions = buildCondition(datasource);
        Map<String, Object> map = DBUtils.queryList(TABLE_DATASOURCE, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntities(sql, args, Datasource.class);
    }

    public List<Datasource> queryList(List<String> ids) {
        String[] args = new String[ids.size()];
        StringBuilder sqlBuilder = new StringBuilder("SELECT * FROM ").append(TABLE_DATASOURCE).append(" WHERE id IN ");
        sqlBuilder.append("(").append("?");
        args[0] = ids.get(0);
        if (ids.size() > 1) {
            for (int i = 1; i < ids.size(); i++) {
                sqlBuilder.append(",").append("?");
                args[i] = ids.get(i);
            }
        }
        sqlBuilder.append(")");
        return xcfJdbcTemplate.queryForEntities(sqlBuilder.toString(), args, Datasource.class);
    }

    public int count(Datasource datasource) {
        Map<String, Object> conditions = buildCondition(datasource);
        Map<String, Object> map = DBUtils.queryCount(TABLE_DATASOURCE, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForObject(sql, Integer.class, args);
    }

    private Map<String, Object> buildCondition(Datasource datasource) {
        Map<String, Object> conditions = new HashMap<>();
        if (datasource != null) {
            if (datasource.getName() != null && !datasource.getName().equals("")) {
                conditions.put("name", "%" + datasource.getName() + "%");
            }
            if (datasource.getType() != null && !datasource.getType().equals("")) {
                conditions.put("type", datasource.getType());
            }
            if (datasource.getCategory() != null && !datasource.getCategory().equals("")) {
                conditions.put("category", datasource.getCategory());
            }
            if (StringUtils.isNotEmpty(datasource.getModelId())) {
                conditions.put("modelId", datasource.getModelId());
            }
        }
        return conditions;
    }


    public Integer getResourceUseCount(String resourceId) {

        return xcfJdbcTemplate.queryForObject(
                "SELECT use_count FROM " + TABLE_DATASOURCE_RESOURCE + " WHERE id = ? ",
                Integer.class,
                resourceId
        );
    }

    public boolean addResourceUseCount(String resourceId, Integer nowCount) {
        return xcfJdbcTemplate.update(
                " UPDATE " + TABLE_DATASOURCE_RESOURCE +
                        " SET use_count= ? " +
                        " WHERE id = ? ",
                nowCount + 1, resourceId
        ) == 1;
    }


    /**
     * 判断数据源是否在使用中
     *
     * @param datasourceId 数据源id
     * @return 是否使用中
     */
    public boolean isUsed(String datasourceId) {
//        return datasyncRepository.isUsed(datasourceId);
        return xcfJdbcTemplate.queryForObject(COUNT_BY_DATASOURCE_ID, Integer.class, datasourceId, datasourceId) > 0;
    }
}
