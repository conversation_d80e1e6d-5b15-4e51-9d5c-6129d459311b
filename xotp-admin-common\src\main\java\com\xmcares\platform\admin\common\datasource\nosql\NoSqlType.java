/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/22
 */
package com.xmcares.platform.admin.common.datasource.nosql;

import com.xmcares.platform.admin.common.datasource.DataSource;
import com.xmcares.platform.admin.common.datasource.DataSourceGroup;
import com.xmcares.platform.admin.common.datasource.DataSourceOptions;
import com.xmcares.platform.admin.common.datasource.DataSourceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public enum NoSqlType implements DataSourceType<DataSource> {
    REDIS(DataSourceGroup.NOSQL,"redis") {
        @Override
        public DataSource createDataSource(DataSourceOptions options) {
            //TODO

            return null;
        }
    },
    ;

    private static final Logger logger = LoggerFactory.getLogger(NoSqlType.class);

    private final DataSourceGroup group;
    private final String typeName;

    NoSqlType(DataSourceGroup group, String typeName) {
        this.group = group;
        this.typeName = typeName;
    }

    @Override
    public String getTypeName() {
        return this.typeName;
    }

    @Override
    public DataSourceGroup getGroup() {
        return this.group;
    }

    @Override
    public boolean equalsTypeName(String typeName) {
        return DataSourceType.super.equalsTypeName(typeName);
    }


    public static NoSqlType fromTypeName(String typeName) {
        if (typeName != null && !typeName.isEmpty()) {
            for (NoSqlType candidate : values()) {
                if (candidate.equalsTypeName(typeName)) {
                    return candidate;
                }
            }
        }
        return null;
    }

}
