package com.xmcares.platform.admin.dataservice.dataset.vo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/9/16 09:02
 */
public class DatatableFieldVO implements Serializable {

    private String id;
    private String datasourceId;
    private String datatableId;
    private String datatableName;
    private String alisa;
    private String name;
    private String type;
    private Integer length;
    private Integer accuracy;
    private String allowEmpty;
    private String columnKey;
    private String hasSystem;
    private String remark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDatasourceId() {
        return datasourceId;
    }

    public void setDatasourceId(String datasourceId) {
        this.datasourceId = datasourceId;
    }

    public String getDatatableId() {
        return datatableId;
    }

    public void setDatatableId(String datatableId) {
        this.datatableId = datatableId;
    }

    public String getDatatableName() {
        return datatableName;
    }

    public void setDatatableName(String datatableName) {
        this.datatableName = datatableName;
    }

    public String getAlisa() {
        return alisa;
    }

    public void setAlisa(String alisa) {
        this.alisa = alisa;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getLength() {
        return length;
    }

    public void setLength(Integer length) {
        this.length = length;
    }

    public Integer getAccuracy() {
        return accuracy;
    }

    public void setAccuracy(Integer accuracy) {
        this.accuracy = accuracy;
    }

    public String getAllowEmpty() {
        return allowEmpty;
    }

    public void setAllowEmpty(String allowEmpty) {
        this.allowEmpty = allowEmpty;
    }

    public String getColumnKey() {
        return columnKey;
    }

    public void setColumnKey(String columnKey) {
        this.columnKey = columnKey;
    }

    public String getHasSystem() {
        return hasSystem;
    }

    public void setHasSystem(String hasSystem) {
        this.hasSystem = hasSystem;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
