/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：4/24/23
 */
package com.xmcares.platform.admin.common.expression;

import com.xmcares.platform.admin.common.expression.function.FmtDateTimeFunction;
import com.xmcares.platform.admin.common.expression.function.SysDateTimeFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 使用占位符的文本表达式，包括变量表达式占位符 和 函数表达式占位符
 * 1）变量表示格式： ${变量名}；
 * 2）函数表达式格式：$函数标识名{变量1, 变量2, 变量3......}。
 * 示例：${userName}，$SYS_DT{yyyy-MM-dd, 1d}
 * <AUTHOR>
 * @since 1.4.1
 */
public class PlaceholderExpression implements StringExpression {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 正则表达式
     */
    public final static String REGEX = "\\$(\\w*)\\{([^{}]*)\\}";
    public final static Pattern PATTERN = Pattern.compile(REGEX);
    public final static String ARGS_SEPARATOR = ",";

    private final Map<String, PseudoFunction> functionRegistry = new HashMap<>(16);

    private static volatile PlaceholderExpression EXPRESSION;

    private PlaceholderExpression() {
    }

    /**
     * 解析表达式，替换变量
     *
     * @param content 表达式内容（不含占位符）
     * @param environment 运行时的环境变量（key=value, ....）
     * @return 解析后的内容
     */
    @Override
    public String eval(String content, Map<String, Object> environment) {
        if (null == content || content.isEmpty()) {
            return content;
        }
        return parseValue(content, environment, new HashSet<>());
    }


    protected String parseValue(String text, Map<String, Object> environment, Set<String> ignorePlaceholders) {
        Matcher matcher = PATTERN.matcher(text);
        //<原始占位字符串, [变量类型标记, 变量名]>
        Map<String, String[]> groups = new HashMap<>(16);
        while (matcher.find()) {
            if (!ignorePlaceholders.contains(matcher.group(0))) {
                groups.put(matcher.group(0), new String[]{matcher.group(1), matcher.group(2)});
            }
        }
        if (groups.isEmpty()) {
            return text;
        }
        for (Map.Entry<String, String[]> entry : groups.entrySet()) {
            String value = resolveValue(entry.getValue()[0], entry.getValue()[1], environment);
            if (null != value) {
                text = text.replace(entry.getKey(), value);
            } else {
                logger.warn("解析表达式变量[ {} ]失败，系统默认采用了空值", entry.getKey());
                ignorePlaceholders.add(entry.getKey());
            }
        }
        return parseValue(text, environment, ignorePlaceholders);
    }

    /**
     * 解析参数值
     * @param $funcName 函数名
     * @param content 表达式黎明的内容
     * @param environment 环境变量
     * @return 解析结果
     */
    private String resolveValue(String $funcName, String content, Map<String, Object> environment) {
        //动态变量
        if (null == $funcName || $funcName.isEmpty()) {
            Object value = environment.get(content);
            if (null == value) {
                return null;
            }
            // Handle java.time API types first (most specific and recommended)
            if (value instanceof java.time.LocalDateTime) {
                return ((java.time.LocalDateTime) value).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else if (value instanceof java.time.LocalDate) {
                return ((java.time.LocalDate) value).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } else if (value instanceof java.time.LocalTime) {
                return ((java.time.LocalTime) value).format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            }
            // Handle java.sql types next (more specific than java.util.Date)
            else if (value instanceof java.security.Timestamp) {
                return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format((java.security.Timestamp) value); // Timestamp includes milliseconds
            } else if (value instanceof java.sql.Date) { // java.sql.Date (date only)
                return new SimpleDateFormat("yyyy-MM-dd").format((java.sql.Date) value);
            } else if (value instanceof java.sql.Time) { // java.sql.Time (time only)
                return new SimpleDateFormat("HH:mm:ss").format((java.sql.Time) value);
            }
            // Handle java.util.Date and Calendar
            else if (value instanceof java.util.Date) {
                return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((java.util.Date) value);
            } else if (value instanceof Calendar) {
                return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(((Calendar) value).getTime());
            } else if (value instanceof Long) {
                return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((Long)value);
            }

            return (String)value;
        }
        //函数
        PseudoFunction function = functionRegistry.get($funcName);
        if (function == null) {
            if (logger.isWarnEnabled()) {
                logger.warn("未找到表达式函数[ {} ]", $funcName);
            }
            return null;
        }
        String[] args = new String[0];
        if (null != content && !content.isEmpty()) {
            String[] argStrs = content.split(ARGS_SEPARATOR);
            args = new String[argStrs.length];
            for (int i = 0; i < argStrs.length; i++) {
                args[i] = argStrs[i].trim();
            }
        }
        return function.execute(environment, args);
    }
    /**
     * 注册伪函数
     * @param flag 函数标识名
     * @param function 函数
     */
    private void registerPseudoFunction(String flag, PseudoFunction function) {
        PseudoFunction exist = this.functionRegistry.get(flag);
        if (exist!= null && !exist.getClass().equals(function.getClass())) {
            throw new IllegalArgumentException(String.format("[%s]类型的函数注册失败，已存在同名的不同类型函数[%s]",
                    function.getClass().getName(), exist.getClass().getName()));
        }
        this.functionRegistry.put(flag, function);
    }


    public static synchronized PlaceholderExpression getInstance() {
        if (EXPRESSION == null) {
            PlaceholderExpression expression = new PlaceholderExpression();

            // Load pseudo functions using ServiceLoader (SPI)
            ServiceLoader<PseudoFunction> serviceLoader = ServiceLoader.load(PseudoFunction.class);
            for (PseudoFunction function : serviceLoader) {
                expression.registerPseudoFunction(function.getName(), function);
            }

            if (!expression.functionRegistry.containsKey(SysDateTimeFunction.DEFAULT_FUNC_NAME)) {
                expression.registerPseudoFunction(SysDateTimeFunction.DEFAULT_FUNC_NAME, new SysDateTimeFunction());
            }
            if (!expression.functionRegistry.containsKey(FmtDateTimeFunction.DEFAULT_FUNC_NAME)) {
                expression.registerPseudoFunction(FmtDateTimeFunction.DEFAULT_FUNC_NAME, new FmtDateTimeFunction());
            }

            EXPRESSION = expression;
        }
        return EXPRESSION;
    }




    public static void main(String[] args) {
        String input1 = "Hello, ${world}! How are you today? ${I am ${fine}, ${thank} you.} Goodbye!$SYS_DT{yyyy/MM/dd, 1d}, ${Yours} - ${datetime, yyyy-MM-dd HH:00:00}, ${date}";

        Map<String, Object> map = new HashMap<>();
        map.put("world", "WORLD");
        map.put("fine", "FINE");
        map.put("thank", "THANK");
        map.put("I am FINE, THANK you.", "You are a hello kitty");
        map.put("datetime", new java.util.Date());
        map.put("date", new java.sql.Date(System.currentTimeMillis()));

        PlaceholderExpression expr = PlaceholderExpression.getInstance();
        long s1 = System.currentTimeMillis();
        System.out.println(expr.eval(input1, map));
        System.out.println(System.currentTimeMillis() - s1);


    }
}
