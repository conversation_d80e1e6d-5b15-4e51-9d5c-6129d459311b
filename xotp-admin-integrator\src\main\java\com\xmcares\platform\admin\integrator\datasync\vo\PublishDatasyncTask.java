package com.xmcares.platform.admin.integrator.datasync.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/17 14:36
 */
@ApiModel(value = "PublishDatasyncTask", description = "数据同步任务发布试图")
public class PublishDatasyncTask implements Serializable {

    /** ID */
    @ApiModelProperty(value = "主键")
    @NotEmpty(message = "id不允许为空")
    private String id;
    /** 实例名称 */
    @ApiModelProperty(value = "实例名称")
    @NotEmpty(message = "实例名称不允许为空")
    private String instanceName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }
}
