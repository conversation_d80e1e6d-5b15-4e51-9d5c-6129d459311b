package com.xmcares.platform.admin.integrator;

import freemarker.template.TemplateExceptionHandler;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

import java.net.URL;

/**
 * 平台管理中心 > 数据集成（管理）模块的配置类
 * <AUTHOR>
 * <AUTHOR> chenYG
 * @date : 2022/3/30 9:31
 */
@EnableFeignClients
@EnableConfigurationProperties({IntegratorProperties.class})
@ComponentScan({"com.xmcares.platform.admin.integrator"})
public class AdminIntegratorConfiguration {

    @Bean(name = "tmplConfiguration")
    public freemarker.template.Configuration tmplConfiguration() {
        //初始化datax配置模板引擎
        freemarker.template.Configuration dataxTemplateConfig = new freemarker.template.Configuration(freemarker.template.Configuration.VERSION_2_3_31);
        URL url = this.getClass().getResource("/seatunnel_ftl");
        try {
            // 试图将URL转为File对象，这在JAR包中会失败
            // 这是修改后的核心代码
            // 直接设置从类路径加载模板，并指定根路径
            // Spring Boot和Freemarker会自动处理JAR包内部的路径
            // dataxTemplateConfig.setDirectoryForTemplateLoading(new File(url.toURI()));
            dataxTemplateConfig.setClassForTemplateLoading(this.getClass(), "/seatunnel_ftl");
        } catch (Exception e) {
            throw new IllegalArgumentException("datax config tmpl error create", e);
        }
        dataxTemplateConfig.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
        dataxTemplateConfig.setLogTemplateExceptions(false);
        dataxTemplateConfig.setFallbackOnNullLoopVariable(false);
        dataxTemplateConfig.setDefaultEncoding("UTF-8");
        return dataxTemplateConfig;
    }

}
