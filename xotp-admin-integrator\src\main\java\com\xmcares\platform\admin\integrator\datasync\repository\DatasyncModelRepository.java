
package com.xmcares.platform.admin.integrator.datasync.repository;

import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/03/29 09:48:18
 * @version 1.0.0
 */
@Repository
public class DatasyncModelRepository {

    public static final String TABLE_BDP_INTG_DATASYNC_MODEL = "bdp_intg_datasync_model";

    private static final String UPDATE_PLUGIN_PATH = "UPDATE " + TABLE_BDP_INTG_DATASYNC_MODEL + " SET plugin_path = ? WHERE id = ?";

	@Autowired
	private XcfJdbcTemplate xcfJdbcTemplate;

	public Boolean add(DatasyncModel datasyncModel) {
		datasyncModel.setId(SnowflakeGenerator.getNextId() + "");
		datasyncModel.setCreateTime(new Date());
		datasyncModel.setUpdateTime(new Date());
		datasyncModel.setCreateUser(UserContextHolder.getUserContext().getUsername());
		Map map = DBUtils.insertSqlAndObjects(datasyncModel, DatasyncModel.class, TABLE_BDP_INTG_DATASYNC_MODEL);
		String sql = DBUtils.getSql(map);
		Object[] args = DBUtils.getObjects(map);
		return xcfJdbcTemplate.update(sql, args) > 0;
	}

	public Boolean update(DatasyncModel datasyncModel) {
		Map map = DBUtils.updateSqlAndObjects("id", datasyncModel, DatasyncModel.class, TABLE_BDP_INTG_DATASYNC_MODEL);
		String sql = DBUtils.getSql(map);
		Object[] args = DBUtils.getObjects(map);
		return xcfJdbcTemplate.update(sql, args) > 0;
	}

	public Boolean updatePluginPath(String id, String path) {
		xcfJdbcTemplate.update(UPDATE_PLUGIN_PATH, path, id);
		return true;
	}

	public boolean delete(String id) {
		String sql = DBUtils.deleteSql(TABLE_BDP_INTG_DATASYNC_MODEL, "id");
		xcfJdbcTemplate.update(sql, id);
		return true;
	}

	public DatasyncModel get(String id) {
		HashMap<String, Object> conditions = new HashMap<>();
		conditions.put("id", String.valueOf(id));
		Map<String, Object> map = DBUtils.queryList(TABLE_BDP_INTG_DATASYNC_MODEL, conditions);
		String sql = DBUtils.getSql(map);
		Object[] args = DBUtils.getObjects(map);
		return xcfJdbcTemplate.queryForEntity(sql, args, DatasyncModel.class);
	}

	public List<DatasyncModel> queryPage(DatasyncModel datasyncModel, Page<DatasyncModel> page) {
		Map<String, Object> conditions = buildCondition(datasyncModel);
		Map<String, Object> map = DBUtils.queryList(TABLE_BDP_INTG_DATASYNC_MODEL, conditions);
		String sql = DBUtils.getSql(map) + " ORDER BY id ASC ";
		Object[] args = DBUtils.getObjects(map);
		return xcfJdbcTemplate.queryForEntities(sql, args, page, DatasyncModel.class);
	}

	public List<DatasyncModel> queryList(DatasyncModel datasyncModel) {
		Map<String, Object> conditions = buildCondition(datasyncModel);
		Map<String, Object> map = DBUtils.queryList(TABLE_BDP_INTG_DATASYNC_MODEL, conditions);
		String sql = DBUtils.getSql(map);
		Object[] args = DBUtils.getObjects(map);
		return xcfJdbcTemplate.queryForEntities(sql, args, DatasyncModel.class);
	}

	public int count(DatasyncModel datasyncModel) {
		Map<String, Object> conditions = buildCondition(datasyncModel);
		Map<String, Object> map = DBUtils.queryCount(TABLE_BDP_INTG_DATASYNC_MODEL, conditions);
		String sql = DBUtils.getSql(map);
		Object[] args = DBUtils.getObjects(map);
		return xcfJdbcTemplate.queryForObject(sql, Integer.class, args);
	}

	private Map<String, Object> buildCondition(DatasyncModel datasyncModel) {
		Map<String, Object> conditions = new HashMap<>();
		if (datasyncModel != null) {
			if (datasyncModel.getId() != null && !datasyncModel.getId().equals("")) {
				conditions.put("id", datasyncModel.getId());
			}
			if (datasyncModel.getCreateTime() != null && !datasyncModel.getCreateTime().equals("")) {
				conditions.put("createTime", datasyncModel.getCreateTime());
			}
			if (datasyncModel.getUpdateTime() != null && !datasyncModel.getUpdateTime().equals("")) {
				conditions.put("updateTime", datasyncModel.getUpdateTime());
			}
			if (datasyncModel.getCreateUser() != null && !datasyncModel.getCreateUser().equals("")) {
				conditions.put("createUser", datasyncModel.getCreateUser());
			}
			if (datasyncModel.getModelName() != null && !datasyncModel.getModelName().equals("")) {
				conditions.put("modelName", "%" + datasyncModel.getModelName() + "%");
			}
			if (datasyncModel.getRunSchema() != null && !datasyncModel.getRunSchema().equals("")) {
				conditions.put("runSchema", datasyncModel.getRunSchema());
			}
			if (datasyncModel.getIntegrationType() != null && !datasyncModel.getIntegrationType().equals("")) {
				conditions.put("integrationType", datasyncModel.getIntegrationType());
			}
			if (datasyncModel.getIntegrationWay() != null && !datasyncModel.getIntegrationWay().equals("")) {
				conditions.put("integrationWay", datasyncModel.getIntegrationWay());
			}
			if (datasyncModel.getDatasourceModelId() != null && !datasyncModel.getDatasourceModelId().equals("")) {
				conditions.put("datasourceModelId", datasyncModel.getDatasourceModelId());
			}
			if (datasyncModel.getPluginPath() != null && !datasyncModel.getPluginPath().equals("")) {
				conditions.put("pluginPath", datasyncModel.getPluginPath());
			}
			if (datasyncModel.getRemark() != null && !datasyncModel.getRemark().equals("")) {
				conditions.put("remark", datasyncModel.getRemark());
			}
			if (datasyncModel.getBaseJson() != null && !datasyncModel.getBaseJson().equals("")) {
				conditions.put("baseJson", datasyncModel.getBaseJson());
			}
			if (datasyncModel.getAdvJson() != null && !datasyncModel.getAdvJson().equals("")) {
				conditions.put("advJson", datasyncModel.getAdvJson());
			}
			if (datasyncModel.getHighJson() != null && !datasyncModel.getHighJson().equals("")) {
				conditions.put("highJson", datasyncModel.getHighJson());
			}
			if (datasyncModel.getColumnJson() != null && !datasyncModel.getColumnJson().equals("")) {
				conditions.put("columnJson", datasyncModel.getColumnJson());
			}

		}
		return conditions;
	}

	public DatasyncModel getByName(String name) {
		HashMap<String, Object> conditions = new HashMap<>();
		conditions.put("modelName", name);
		Map<String, Object> map = DBUtils.queryList(TABLE_BDP_INTG_DATASYNC_MODEL, conditions);
		String sql = DBUtils.getSql(map);
		Object[] args = DBUtils.getObjects(map);
		return xcfJdbcTemplate.queryForEntity(sql, args, DatasyncModel.class);
	}

	public DatasyncModel get(String datasourceModelId, String type) {
		HashMap<String, Object> conditions = new HashMap<>();
		conditions.put("datasourceModelId", String.valueOf(datasourceModelId));
		conditions.put("integrationType", type);
		Map<String, Object> map = DBUtils.queryList(TABLE_BDP_INTG_DATASYNC_MODEL, conditions);
		String sql = DBUtils.getSql(map);
		Object[] args = DBUtils.getObjects(map);
		return xcfJdbcTemplate.queryForEntity(sql, args, DatasyncModel.class);
	}

	public List<DatasyncModel> listByDatasourceModelIds(List<String> modelIds) {
		StringBuilder whereBuilder = new StringBuilder();
		Object[] args = new Object[modelIds.size()];
		if (modelIds.size() == 1) {
			whereBuilder.append("?");
			args[0] = modelIds.get(0);
		} else {
			whereBuilder.append("?");
			args[0] = modelIds.get(0);
			for (int i = 1; i < modelIds.size(); i++) {
				whereBuilder.append(",").append("?");
				args[i] = modelIds.get(i);
			}
		}
		String sql = "SELECT * FROM " + TABLE_BDP_INTG_DATASYNC_MODEL + " WHERE datasource_model_id IN (" + whereBuilder.toString() + ")";
		return xcfJdbcTemplate.queryForEntities(sql, args, DatasyncModel.class);
	}
}
