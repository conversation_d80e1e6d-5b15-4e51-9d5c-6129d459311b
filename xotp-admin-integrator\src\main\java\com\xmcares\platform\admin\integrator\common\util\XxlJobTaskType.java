package com.xmcares.platform.admin.integrator.common.util;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR> chenYG
 * @date : 2022/3/29 10:37
 */
public enum XxlJobTaskType {

    /** XXL_JOB的默认任务 */
    DEF("基础任务"),

    /** 数据集成模块提交的任务 */
    DATAX_DEF("Datax默认任务"),
    DATAX_INC("Datax增量任务"),

    /** 数据开发模块提交的惹怒 */
    DEV_BATCH("数据开发批次任务"),
    DEV_STREAM("数据开发流式任务"),
    ArchiveJob("归档任务"),
    ;

    private static final Set<String> INTEGRATORS = new HashSet<>();
    private static final Set<String> DEVELOPERS = new HashSet<>();
    static {
        INTEGRATORS.add(XxlJobTaskType.DATAX_DEF.name());
        INTEGRATORS.add(XxlJobTaskType.DATAX_INC.name());
        DEVELOPERS.add(DEV_BATCH.name());
        DEVELOPERS.add(DEV_STREAM.name());
    }

    private XxlJobTaskType(String title){
        this.title = title;
    }
    private String title;
    public String getTitle() {
        return title;
    }

    public static XxlJobTaskType match(String value, XxlJobTaskType def) {
        XxlJobTaskType result = def;
        try {
            result = XxlJobTaskType.valueOf(value);
        } catch (Exception e) {
            return def;
        }
        return result;
    }

    /**
     * 验证是否是集成任务
     * @param type
     * @return
     */
    public static boolean isIntegrator(String type) {
        if (type == null) { return false; }
        return INTEGRATORS.contains(type.toUpperCase());
    }

    /**
     * 验证是否是数据开发任务
     * @param type
     * @return
     */
    public static boolean isDeveloper(String type) {
        if (type == null) { return false; }
        return DEVELOPERS.contains(type.toUpperCase());
    }
}
