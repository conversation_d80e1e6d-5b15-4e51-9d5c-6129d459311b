package com.xmcares.platform.admin.dataservice.dataset.web;

import com.xmcares.platform.admin.dataservice.dataset.service.DatasetPublishedService;
import com.xmcares.platform.admin.dataservice.dataset.vo.MetaDatasetVO;
import com.xmcares.platform.admin.metadata.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据服务
 * 
 * <AUTHOR> <<EMAIL>>
 * @version 1.0
 * @since 2025-05-20
 */
@Api(value = "发布数据服务")
@Validated
@RestController
@RequestMapping(value = "${xbdp.api.dataservice:/dataservice}/dataset/published", produces = "application/json")
public class DatasetPublishedController {

    @Autowired
    private DatasetPublishedService datasetPublishedService;

    /**
     * 获取已发布的数据集列表
     */
    @ApiOperation("获取已发布的数据集列表")
    @GetMapping("/list")
    @ResponseBody
    public Result<List<MetaDatasetVO>> getPublishedDatasets(
            @RequestParam(name = "datasetId") String datasetId) {
        return datasetPublishedService.getPublishedDatasets(datasetId);
    }

    /**
     * 创建已发布的数据集
     */
    @ApiOperation("创建已发布的数据集")
    @PostMapping("/create")
    @ResponseBody
    public Result<Boolean> createPublishedDataset(
            @RequestBody MetaDatasetVO request) {
        return datasetPublishedService.createPublishedDataset(request);
    }

    /**
     * 检查数据集代码是否唯一
     */
    @ApiOperation("检查数据集代码是否唯一")
    @GetMapping("/check-code")
    @ResponseBody
    public Result<Boolean> checkDatasetCodeUnique(
            @RequestParam(name = "datasetCode") String datasetCode) {
        return datasetPublishedService.checkDatasetCodeUnique(datasetCode);
    }

    /**
     * 上下线服务
     */
    @ApiOperation("上下线服务")
    @PostMapping("/up-down")
    @ResponseBody
    public Result<Boolean> stopPublishedDataset(
            @RequestParam(name = "datasetCode") String datasetCode,
            @RequestParam(name = "published") int published) {
        return datasetPublishedService.updatePublished(datasetCode, published);
    }

    /**
     * 删除服务
     */
    @ApiOperation("删除服务")
    @PostMapping("/delete")
    @ResponseBody
    public Result<Boolean> deletePublishedDataset(
            @RequestParam(name = "id") String id) {
        return datasetPublishedService.deletePublishedDataset(id);
    }
}
