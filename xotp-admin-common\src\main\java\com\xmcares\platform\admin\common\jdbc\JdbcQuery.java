package com.xmcares.platform.admin.common.jdbc;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/9/2 09:33
 */
public class JdbcQuery {
    protected static final Logger logger = LoggerFactory.getLogger(JdbcQuery.class.getName());

    /**
     * 获取数据
     * 注意：本方法内部不补货异常，直接跑出"checked"异常，由上层业务处理
     * @param ds 数据源
     * @param sql
     * @param sqlArgs sql statement的参数
     * @return
     * @throws SQLException
     */
    public static List<Map<String, Object>> getJdbcData(DataSource ds, String sql, Object... sqlArgs) throws SQLException {
        List<Map<String, Object>> jdbcData;
        Connection conn = null;
        try {
            conn = ds.getConnection();
            jdbcData = getJdbcData(conn, sql, sqlArgs);
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    //do nothing
                }
            }
        }
        return jdbcData;
    }



    /**
     *
     * @param conn 数据库连接对象，需要调用者自行关闭
     * @param sql  sql语句
     * @param sqlArgs sql参数值
     * @return
     * @throws SQLException
     */
    public static List<Map<String, Object>> getJdbcData(Connection conn, String sql, Object... sqlArgs) throws SQLException {
        List<Map<String, Object>> resultList = new ArrayList<>();
        PreparedStatement ps = null;
        try {
            ps = conn.prepareStatement(sql);
            if (sqlArgs != null && sqlArgs.length > 0) {
                for (int i = 1; i < sqlArgs.length + 1; i++) {
                    ps.setObject(i, sqlArgs[i - 1]);
                }
            }

            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                HashMap<String, Object> resultLine = new HashMap<>();
                ResultSetMetaData metaData = rs.getMetaData();
                for (int i = 0; i < metaData.getColumnCount(); i++) {
                    String columnName = metaData.getColumnLabel(i + 1);
                    Object object = rs.getObject(i + 1);
                    resultLine.put(columnName, object);
                }
                resultList.add(resultLine);
            }
        } finally {
            if (ps != null) {
                try {
                    ps.close();
                } catch (Exception e) {
                }
            }
        }
        return resultList;
    }


    public static String getDBName(String dbType, Connection connection){
        try {
            switch (dbType){
                case "hive":
                case "hive1.1":
                    return connection.getSchema();
                default:
                    return connection.getCatalog();
            }

        } catch (SQLException e) {
            return null;
        }
    }

}
