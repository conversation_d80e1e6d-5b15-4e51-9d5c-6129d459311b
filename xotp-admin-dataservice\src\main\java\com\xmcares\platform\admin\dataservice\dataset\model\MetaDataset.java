package com.xmcares.platform.admin.dataservice.dataset.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xmcares.platform.admin.common.validation.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/29 09:14
 */
@ApiModel(value = MetaDataset.TABLE, description = "数据模型服务")
public class MetaDataset {
    public static final String TABLE = "bdp_api_dataset";
    /**
     * ID
     */
    @NotNull(message = "ID不允许为空", groups = Update.class)
    @ApiModelProperty(value = "ID")
    private String id;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除", notes = "0: 否 1: 是")
    private String deleted;

    @ApiModelProperty(value = "datasource_id")
    private String datasourceId;

    @ApiModelProperty(value = "模型名称")
    private String datasetName;

    @ApiModelProperty(value = "服务名称")
    private String datasetCode;

    @ApiModelProperty(value = "模型SQL")
    private String datasetSql;

    @ApiModelProperty(value = "已发布")
    private Integer datasetPublished;

    @ApiModelProperty(value = "service权限表id")
    private String serviceId;

    @ApiModelProperty(value = "参数")
    private String datasetParameter;

    @ApiModelProperty(value = "模型模式")
    private Integer datasetMode;

    public String getDatasourceId() {
        return datasourceId;
    }

    public void setDatasourceId(String datasourceId) {
        this.datasourceId = datasourceId;
    }

    public static String getTABLE() {
        return TABLE;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDeleted() {
        return deleted;
    }

    public void setDeleted(String deleted) {
        this.deleted = deleted;
    }

    public String getDatasetName() {
        return datasetName;
    }

    public void setDatasetName(String datasetName) {
        this.datasetName = datasetName;
    }

    public String getDatasetCode() {
        return datasetCode;
    }

    public void setDatasetCode(String datasetCode) {
        this.datasetCode = datasetCode;
    }

    public String getDatasetSql() {
        return datasetSql;
    }

    public void setDatasetSql(String datasetSql) {
        this.datasetSql = datasetSql;
    }

    public Integer getDatasetPublished() {
        return datasetPublished;
    }

    public void setDatasetPublished(Integer datasetPublished) {
        this.datasetPublished = datasetPublished;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getDatasetParameter() {
        return datasetParameter;
    }

    public void setDatasetParameter(String datasetParameter) {
        this.datasetParameter = datasetParameter;
    }


    public Integer getDatasetMode() {
        return datasetMode;
    }

    public void setDatasetMode(Integer datasetMode) {
        this.datasetMode = datasetMode;
    }
}
