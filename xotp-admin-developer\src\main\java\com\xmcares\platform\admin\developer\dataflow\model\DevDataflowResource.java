package com.xmcares.platform.admin.developer.dataflow.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.common.validation.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description zhanlh 增加一个根据流程定义id
 */
@ApiModel(value = DevDataflowResource.TABLE, description = "数据开发资源信息")
public class DevDataflowResource implements Serializable {

    public static final String TABLE = "bdp_dev_dataflow_resource";

    public static DevDataflowResource init(String dataflowId, String name, String remark) {
        DevDataflowResource result = new DevDataflowResource();
        result.setId(SnowflakeGenerator.getNextId() + "");
        result.setUploadTime(new Date());
        result.setUpdateTime(new Date());
        result.setUploadUser(UserContextHolder.getUserContext().getUsername());
        result.setName(name);
        result.setPath(null);
        result.setRemark(remark);
        result.setDataflowId(dataflowId);
        result.setUseCount(0);
        result.setDeleted(YNEnum.NO.getIntCharCode());
        return result;
    }

    /** ID */
    @NotNull(message = "ID不允许为空", groups = Update.class)
    @ApiModelProperty(value = "ID")
    private String id;
    /** 工作流ID */
    @ApiModelProperty(value = "工作流ID")
    private String dataflowId;

    /** 创建时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date uploadTime;

    /** 更新时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 维护人ID */
    @ApiModelProperty(value = "维护人ID")
    private String uploadUser;

    /** 资源名称 */
    @ApiModelProperty(value = "资源名称")
    private String name;

    /** 资源路径 */
    @ApiModelProperty(value = "资源路径")
    private String path;

    /** 资源描述 */
    @ApiModelProperty(value = "资源描述")
    private String remark;

    @ApiModelProperty(value = "使用次数")
    private Integer useCount;

    @ApiModelProperty(value = "是否删除")
    private String deleted;



    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUploadUser() {
        return uploadUser;
    }

    public void setUploadUser(String uploadUser) {
        this.uploadUser = uploadUser;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDataflowId() {
        return dataflowId;
    }

    public void setDataflowId(String dataflowId) {
        this.dataflowId = dataflowId;
    }

    public Integer getUseCount() {
        return useCount;
    }

    public void setUseCount(Integer useCount) {
        this.useCount = useCount;
    }

    public String getDeleted() {
        return deleted;
    }

    public void setDeleted(String deleted) {
        this.deleted = deleted;
    }
}
