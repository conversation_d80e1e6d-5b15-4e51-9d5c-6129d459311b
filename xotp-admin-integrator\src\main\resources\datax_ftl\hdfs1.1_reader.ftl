{
    "name": "hdfsreader",
    "parameter": {
        "path": "${orgin.path}",
        "defaultFS": "${orgin.defaultFS}",
        "column": [
            <#list orginColumns! as column>
                {
                "index": "${column.index}",
                "type": "${column.type}"
                }<#if column_has_next>,</#if>
            </#list>
        ],
        <#if orgin.fieldDelimiter??>"fieldDelimiter":"${orgin.fieldDelimiter}",</#if>
        <#if orgin.encoding??>"encoding":"${orgin.encoding}",</#if>
        <#if orgin.nullFormat??>"nullFormat":"${orgin.nullFormat}",</#if>
        <#if orgin.compress??>"compress":"${orgin.compress}",</#if>
        <#if orgin.hadoopConfigs?? && (orgin.hadoopConfigs.size > 0)>
            "hadoopConfig":{
                <#list orgin.hadoopConfigs as config>
                    "${config.key}":"${config.value}"<#if config_has_next>,</#if>
                </#list>
            },
        </#if>
        <#if orgin.csvReaderConfig?? && (orgin.csvReaderConfig.size > 0)>
            "csvReaderConfig":{
                <#list orgin.csvReaderConfig as config>
                    "${config.key}":"${config.value}"<#if config_has_next>,</#if>
                </#list>
            },
        </#if>
        <#if orginDatasource.enableKerberos?? && orginDatasource.enableKerberos = '1'>
            "haveKerberos":true,
            "kerberosKeytabFilePath": "<#noparse>${ROOT_PATH}/DATABASE_RESOURCE/${keytabPath}</#noparse>",
            "kerberosPrincipal": "${orginDatasource.kerberosPrincipal}",
        </#if>
        "fileType": "${orgin.fileType}"
    }
}