package com.xmcares.platform.admin.dataservice.dataset.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.beans.factory.annotation.Value;

/**
 * 
 * <AUTHOR> <<EMAIL>>
 * @version 1.0
 * @since: 2025-05-22
 */
@Configuration
@ConfigurationProperties(prefix = "xbdp.openapi")
public class OpenApiProperties {
    // http://www.openapi.com:{port}{basePath}
    @Value("${xbdp.openapi.url:http://127.0.0.1:8087}")
    private String url;

    @Value("${xbdp.openapi.basePath:/api}")
    private String basePath;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getBasePath() {
        return basePath;
    }

    public void setBasePath(String basePath) {
        this.basePath = basePath;
    }
}
