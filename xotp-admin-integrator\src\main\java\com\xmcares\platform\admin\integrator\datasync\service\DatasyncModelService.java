package com.xmcares.platform.admin.integrator.datasync.service;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.framework.fsclient.FSTemplate;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.errors.MetadataException;
import com.xmcares.platform.admin.integrator.IntegratorProperties;
import com.xmcares.platform.admin.integrator.common.util.ConstantUtils;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncModel;
import com.xmcares.platform.admin.integrator.datasync.repository.DatasyncModelRepository;
import com.xmcares.platform.admin.integrator.datasync.repository.DatasyncReaderModelMapperRepository;
import com.xmcares.platform.admin.integrator.datasync.repository.DatasyncRepository;
import com.xmcares.platform.admin.integrator.datasync.repository.DatasyncWriteModelMapperRepository;
import com.xmcares.platform.admin.metadata.common.enums.SupportDatasourceModel;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import com.xmcares.platform.admin.metadata.database.model.DatasourceModel;
import com.xmcares.platform.admin.metadata.database.model.DevDataflowResourceReaderMapper;
import com.xmcares.platform.admin.metadata.database.model.DevDataflowResourceWriteMapper;
import com.xmcares.platform.admin.metadata.database.repository.DatasourceModelRepository;
import com.xmcares.platform.admin.metadata.database.repository.DatasourceRepository;
import com.xmcares.platform.admin.metadata.database.vo.SupportDatasourceModelVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/03/29 09:48:18
 * @version 1.0.0
 */
@Service
public class DatasyncModelService {

	private static final List<String> SUPPORT_SUFFIX = Arrays.asList("zip");

	@Autowired
    private DatasyncModelRepository datasyncModelRepository;
	@Autowired
	private DatasourceModelRepository datasourceModelRepository;
	@Autowired
	private DatasourceRepository datasourceRepository;
	@Autowired
	private DatasyncReaderModelMapperRepository datasyncReaderModelMapperRepository;
	@Autowired
	private DatasyncWriteModelMapperRepository datasyncWriteModelMapperRepository;
	@Autowired
	private FSTemplate fsTemplate;
	@Autowired
	private IntegratorProperties properties;
    @Autowired
    private DatasyncRepository datasyncRepository;

	public Page page(DatasyncModel datasyncModel, Pagination pagination) {
		List<DatasyncModel> datasyncModels =  datasyncModelRepository.queryPage(datasyncModel, new Page<>(pagination.getPageNo()-1, pagination.getPageSize()));
		int total = datasyncModelRepository.count(datasyncModel);
		Page<DatasyncModel> page = new Page<>();
		page.setData(datasyncModels);
		page.setTotal(total);
		page.setPageNo(pagination.getPageNo());
		page.setPageSize(pagination.getPageSize());
		return page;
	}

	public List list(DatasyncModel datasyncModel) {
		return datasyncModelRepository.queryList(datasyncModel);
	}

	public DatasyncModel get(String id) {
		return datasyncModelRepository.get(id);
	}

	public DatasyncModel get(String datasourceId, String type) {
		Datasource datasource = datasourceRepository.get(datasourceId);
		if(datasource==null) {
			throw new MetadataException("数据源不存在");
		}
		DatasourceModel datasourceModel = datasourceModelRepository.getByType(datasource.getType());
		if(datasourceModel==null) {
			throw new MetadataException("数据源模型不存在");
		}
		return datasyncModelRepository.get(datasourceModel.getId(), type);
	}

	public boolean add(DatasyncModel datasyncModel) {
		this.checkRepeat(datasyncModel);
		if ("0".equals(datasyncModel.getIntegrationWay())) {
			DatasourceModel findResult = datasourceModelRepository.get(datasyncModel.getDatasourceModelId());
			if (findResult == null) {
				throw new BusinessException("数据源模型不存在");
			}
			datasyncModel.setDatasourceModelType(findResult.getType());
		}
		datasyncModelRepository.add(datasyncModel);
		return true;
	}

	public boolean update(DatasyncModel datasyncModel) {
		DatasyncModel older = this.checkExist(datasyncModel.getId());
		datasyncModel.setDatasourceModelId(older.getDatasourceModelId());
		datasyncModel.setDatasourceModelType(older.getDatasourceModelType());
		if(older.getModelName().equals(datasyncModel.getModelName())) {
			//直接更新
			datasyncModelRepository.update(datasyncModel);
		}else {
			//先判重
			this.checkRepeat(datasyncModel);
		    datasyncModelRepository.update(datasyncModel);
		}
		return true;
	}
    @Transactional(rollbackFor = Exception.class)
	public void delete(String id) {
		DatasyncModel datasyncModel = this.checkExist(id);
		if (SupportDatasourceModel.ONLY_READER.getCode().equals(datasyncModel.getIntegrationType())) {
			this.datasyncReaderModelMapperRepository.updateToDeletedByPId(id);
		}
		if (SupportDatasourceModel.ONLY_WIRTE.getCode().equals(datasyncModel.getIntegrationType())) {
			this.datasyncWriteModelMapperRepository.updateToDeletedByPId(id);
		}
		datasyncModelRepository.delete(id);
	}

	public void deleteBatch(List<String> ids) {
		if(ids!=null && !ids.isEmpty()) {
			for(String id : ids) {
				if (datasyncRepository.datasyncModelIdForeignKeyUseCount(id) == 0){
					this.delete(id);
				}
			}
		}
	}

	@Transactional(rollbackFor = Exception.class)
	public void uploadPlugin(String id, MultipartFile file) throws Exception {
		checkPluginFile(file);
		DatasyncModel datasyncModel = this.checkExist(id);
		boolean isReader = SupportDatasourceModel.ONLY_READER.getCode().equals(datasyncModel.getIntegrationType());

		String pluginPath = this.getPluginFilePath(file, isReader ? "reader": "writer");
		// 新增文件管理表记录
		if (isReader) {
			if (this.datasyncReaderModelMapperRepository.exist(id)) {
				this.datasyncReaderModelMapperRepository.updateToDeletedByPId(id);
			}
			DevDataflowResourceReaderMapper sourceInfo = DevDataflowResourceReaderMapper.init(id, pluginPath);
			this.datasyncReaderModelMapperRepository.add(sourceInfo);
			pluginPath = sourceInfo.getPath();
		} else {
			if (this.datasyncWriteModelMapperRepository.exist(id)) {
				this.datasyncWriteModelMapperRepository.updateToDeletedByPId(id);
			}
			DevDataflowResourceWriteMapper sourceInfo = DevDataflowResourceWriteMapper.init(id, pluginPath);
			this.datasyncWriteModelMapperRepository.add(sourceInfo);
			pluginPath = sourceInfo.getPath();
		}
		//更新插件地址
		datasyncModelRepository.updatePluginPath(datasyncModel.getId(), pluginPath);
		//上传插件
		fsTemplate.saveFile(new FileDesc.FileDescImpl(null, pluginPath), file.getInputStream());
	}




	//判断数据集成模型是否被使用
	public boolean assertDatasyncModelUsing(String id) {
		return datasyncRepository.datasyncModelIdForeignKeyUseCount(id) > 0;
	}

	private void checkPluginFile(MultipartFile file) {
		String filename = file.getOriginalFilename();
		if (StringUtils.isEmpty(filename)) {
			throw new BusinessException("文件名称不允许为空");
		}
		if (filename.split("\\.").length != 2) {
			throw new BusinessException("文件格式有误");
		}
		String pluginName = file.getOriginalFilename().split("\\.")[0];
		String suffix = file.getOriginalFilename().split("\\.")[1];
		if(StringUtils.isBlank(pluginName)) {
			throw new MetadataException("上传的插件名称有误");
		}
		if (!SUPPORT_SUFFIX.contains(suffix)) {
			throw new BusinessException("只能上传 ZIP|RAR 格式的文件");
		}
	}

	private void checkRepeat(DatasyncModel datasyncModel) {
		DatasyncModel repeat = datasyncModelRepository.getByName(datasyncModel.getModelName());
		if(repeat != null) {
			throw new MetadataException("集成模型重复");
		}
	}

	private DatasyncModel checkExist(String id) {
		DatasyncModel older = datasyncModelRepository.get(id);
		if(older==null) {
			throw new MetadataException("集成模型不存在");
		}
		return older;
	}

	public List<SupportDatasourceModelVo> findSupportDatasourceModel(List<SupportDatasourceModelVo> sources) {
		List<DatasyncModel> queryResults = datasyncModelRepository.listByDatasourceModelIds(sources.stream().map(SupportDatasourceModelVo::getId).collect(Collectors.toList()));
		if (CollectionUtils.isEmpty(queryResults)) {
			return sources.stream().map(source-> new SupportDatasourceModelVo(source.getId(), source.getType(), SupportDatasourceModel.ALL.getCode())).collect(Collectors.toList());
		} else {
			Map<String, List<DatasyncModel>> handler = queryResults.stream().collect(Collectors.groupingBy(DatasyncModel::getDatasourceModelId));
			List<SupportDatasourceModelVo> result = new ArrayList<>();
			sources.forEach(source-> {
				if (handler.containsKey(source.getId())) {
					if (handler.get(source.getId()).size() == 1) {
						if ("0".equals(handler.get(source.getId()).get(0).getIntegrationType())) {
							result.add(new SupportDatasourceModelVo(source.getId(),source.getType(),SupportDatasourceModel.ONLY_WIRTE.getCode()));
						} else {
							result.add(new SupportDatasourceModelVo(source.getId(),source.getType(),SupportDatasourceModel.ONLY_READER.getCode()));
						}
					}
				} else {
					result.add(new SupportDatasourceModelVo(source.getId(), source.getType(), SupportDatasourceModel.ALL.getCode()));
				}
			});
			return result;
		}
	}


	private String getPluginFilePath(MultipartFile file, String ... subDirNames) {
		//根路径
		StringBuilder sb = new StringBuilder(properties.getFileServerRoot());
		sb.append(ConstantUtils.FILE_DATAX_PLUGIN_DIR);
		if (subDirNames != null) {
			for (String subDirName : subDirNames) {
				sb.append("/").append(subDirName);
			}
		}
		sb.append("/{}_").append(file.getOriginalFilename());
		return sb.toString();
	 }
}
