package com.xmcares.platform.admin.developer.dataflow.service;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.framework.fsclient.FSTemplate;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.platform.admin.common.errors.SystemException;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.developer.common.config.DeveloperProperties;
import com.xmcares.platform.admin.developer.dataflow.model.DevDataflowResource;
import com.xmcares.platform.admin.developer.dataflow.model.DevDataflowResourceMapper;
import com.xmcares.platform.admin.developer.dataflow.repository.DevDataflowResourceMapperRepository;
import com.xmcares.platform.admin.developer.dataflow.repository.DevDataflowResourceRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Service
public class DevDataflowResourceService {
    private static final Logger LOG = LoggerFactory.getLogger(DevDataflowResourceService.class);

    @Autowired
    private DevDataflowResourceRepository devDataflowResourceRepository;
    @Autowired
    private DevDataflowResourceMapperRepository devDataflowResourceMapperRepository;

    @Autowired
    private FSTemplate fsTemplate;

    @Autowired
    private DeveloperProperties properties;

    @Transactional(rollbackFor = SystemException.class)
    public void upload(DevDataflowResource devDataflowResource, MultipartFile file) {
        // 1. 构建记录
        DevDataflowResource initResource = DevDataflowResource.init(devDataflowResource.getDataflowId(), devDataflowResource.getName(), devDataflowResource.getRemark());
        String path = properties.getFileServerDir()+"/dataflow/plugin/"+ initResource.getId() + "_" + file.getOriginalFilename();
        initResource.setPath(path);
        DevDataflowResourceMapper initMapperResource = DevDataflowResourceMapper.init(initResource.getId(), initResource.getPath());
        // 2. 保存记录
        try {
            devDataflowResourceRepository.add(initResource);
            devDataflowResourceMapperRepository.insertResourceMapper(initMapperResource);
        } catch (Exception e) {
            LOG.error("上传文件-{}-时，保存记录异常", file.getOriginalFilename(), e);
            throw new SystemException("上传文件异常， 保存记录失败");
        }
        // 3. 上传文件
        try {
            fsTemplate.saveFile(new FileDesc.FileDescImpl(null, initResource.getPath()), file.getInputStream());
        } catch (Exception e) {
            LOG.error("上传文件-{}-时，文件上传失败", file.getOriginalFilename(), e);
            throw new SystemException("上传文件异常，文件上传失败");
        }
    }

    public Page<DevDataflowResource> pageDevDataflowResource(DevDataflowResource devDataflowResource, Pagination pagination) {
        devDataflowResource.setDeleted(YNEnum.NO.getIntCharCode());
        List<DevDataflowResource> devDataflowResourceList = devDataflowResourceRepository.queryDevDataflowResourcePage(devDataflowResource, new Page<>(pagination.getPageNo() - 1, pagination.getPageSize()));
        int total = devDataflowResourceRepository.countDevDataflowResource(devDataflowResource);
        Page<DevDataflowResource> page = new Page<>();
        page.setData(devDataflowResourceList);
        page.setTotal(total);
        page.setPageNo(pagination.getPageNo());
        page.setPageSize(pagination.getPageSize());
        return page;
    }

    @Transactional
    public Boolean resourceDelete(String id) {
        // 删除时， 先查询， 如果 use_count <= 0 直接删除， 并且 设置 dbp_dataflow_resource_mapper 对应的字段 的 deleted 为 '1'
        // 如果 use_count > 0 则标记 deleted 标记为 '1'
        DevDataflowResource devDataflowResource = this.devDataflowResourceRepository.getById(id);
        if (devDataflowResource == null) {
            return true;
        }
        if (devDataflowResource.getUseCount() <= 0) {
            devDataflowResourceRepository.delete(id);
            devDataflowResourceMapperRepository.updateToDeletedById(devDataflowResource.getId());
            return true;
        } else {
            return devDataflowResourceRepository.updateToDeletedById(id);
        }
    }

    @Transactional
    public Boolean removeByDataflowId(String dataflowId) {
        // 1. 使用 连接 的方式 将 dbp_dataflow_resource_mapper 表 的 deleted 标记 为 ‘1’
        // 2. 删除表数据
        return devDataflowResourceMapperRepository.deleteResourceMapper(dataflowId) && devDataflowResourceRepository.deleteByDataflowId(dataflowId);
    }


}
