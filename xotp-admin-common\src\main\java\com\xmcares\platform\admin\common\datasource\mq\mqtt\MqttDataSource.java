/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/23
 */
package com.xmcares.platform.admin.common.datasource.mq.mqtt;

import com.xmcares.platform.admin.common.datasource.mq.MessageHeaders;
import com.xmcares.platform.admin.common.datasource.mq.MqDataSource;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public class MqttDataSource implements MqDataSource {
    private static final Logger logger = LoggerFactory.getLogger(MqttDataSource.class);

    private final String name;
    private final MqttProperties props;

    private volatile MqttClient mqttClient;
    private final AtomicBoolean connected = new AtomicBoolean(false);

    private final Map<String, MqMessageListener> listeners = new ConcurrentHashMap<>();

    public MqttDataSource(String name, MqttProperties props) {
        this.name = name;
        this.props = props;
    }

    private synchronized MqttClient getClient() {
        if (mqttClient != null && mqttClient.isConnected()) {
            return mqttClient;
        }
        try {
            mqttClient = new MqttClient(props.getBrokerUrl(), props.getClientId(), new MemoryPersistence());

            MqttConnectOptions options = new MqttConnectOptions();
            options.setCleanSession(true);
            if (props.getUsername() != null) {
                options.setUserName(props.getUsername());
            }
            if (props.getPassword() != null) {
                options.setPassword(props.getPassword().toCharArray());
            }

            mqttClient.setCallback(new MqttCallback() {
                @Override
                public void connectionLost(Throwable cause) {
                    connected.set(false);
                    logger.warn("MQTT connection lost: {}", cause.getMessage());
                }

                @Override
                public void messageArrived(String topic, MqttMessage message) {
                    MqMessageListener listener = listeners.get(topic);
                    if (listener != null) {
                        try {
                            MessageHeaders headers = new MessageHeaders();
                            headers.put("qos", message.getQos());
                            headers.put("retained", message.isRetained());
                            headers.put("duplicate", message.isDuplicate());
                            listener.onMessage(headers, message.getPayload());
                        } catch (Exception e) {
                            logger.error("MQTT message processing failed for topic {}", topic, e);
                        }
                    }
                }

                @Override
                public void deliveryComplete(IMqttDeliveryToken token) {
                    // optional
                }
            });

            mqttClient.connect(options);
            connected.set(true);
            logger.info("MQTT connected to {}", props.getBrokerUrl());
            return mqttClient;
        } catch (MqttException e) {
            logger.error("MQTT connect failed", e);
            throw new RuntimeException("MQTT connect failed", e);
        }
    }

    @Override
    public void close() {
        try {
            if (mqttClient != null && mqttClient.isConnected()) {
                mqttClient.disconnect();
            }
            if (mqttClient != null) {
                mqttClient.close();
            }
        } catch (Exception e) {
            logger.warn("MQTT close failed", e);
        }
    }

    @Override
    public AvailableStatus testAvailable() {
        try {
            return getClient().isConnected() ? new AvailableStatus(true, null) :
                    new AvailableStatus(false, null);
        } catch (Exception e) {
            logger.warn("MQTT testAvailable failed", e);
            return  new AvailableStatus(false, e.getMessage());
        }
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void sendMessage(String topic, MessageHeaders headers, byte[] body) {
        try {
            MqttMessage message = new MqttMessage(body);
            message.setQos(props.getQos());
            getClient().publish(topic, message);
        } catch (Exception e) {
            logger.error("MQTT sendMessage failed for topic {}", topic, e);
            throw new RuntimeException("MQTT sendMessage failed", e);
        }
    }

    @Override
    public void pullMessage(String topic, String group, MessageHeaders headers, Callback callback) {
        throw new UnsupportedOperationException("MQTT does not support pull mode; use addMessageListener instead.");
    }

    @Override
    public void addMessageListener(String topic, String group, MqMessageListener listener) {
        try {
            listeners.put(topic, listener);
            getClient().subscribe(topic, props.getQos());
        } catch (MqttException e) {
            logger.error("MQTT addMessageListener failed for topic {}", topic, e);
            throw new RuntimeException("MQTT subscribe failed", e);
        }
    }

    @Override
    public void removeMessageListener(String topic, String group, MqMessageListener listener) {
        try {
            getClient().unsubscribe(topic);
            listeners.remove(topic);
        } catch (MqttException e) {
            logger.warn("MQTT removeMessageListener failed for topic {}", topic, e);
        }
    }
}
