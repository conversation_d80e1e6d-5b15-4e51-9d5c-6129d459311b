package com.xmcares.platform.admin.integrator.datasync.repository;

import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.metadata.database.model.DevDataflowResourceWriteMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/20 10:22
 **/
@Repository
public class DatasyncWriteModelMapperRepository {
    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    public List<DevDataflowResourceWriteMapper> selectAllByUnSync() {
        String sql = "select * from " + DevDataflowResourceWriteMapper.TABLE + " where synced = ? order by upload_time desc ";
        return xcfJdbcTemplate.queryForEntities(sql, new Object[]{YNEnum.NO.getIntCharCode()}, DevDataflowResourceWriteMapper.class);
    }

    public List<DevDataflowResourceWriteMapper> selectAll() {
        String sql = "select * from " + DevDataflowResourceWriteMapper.TABLE + "  order by upload_time desc ";
        return xcfJdbcTemplate.queryForEntities(sql,new Object[]{}, DevDataflowResourceWriteMapper.class);
    }

    public void delete(String id) {
        String sql = DBUtils.deleteSql(DevDataflowResourceWriteMapper.TABLE, "id");
        xcfJdbcTemplate.update(sql, id);
    }

    public Boolean add(DevDataflowResourceWriteMapper entity) {
        Map map = DBUtils.insertSqlAndObjects(entity, DevDataflowResourceWriteMapper.class, DevDataflowResourceWriteMapper.TABLE);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.update(sql, args) > 0;
    }

    public boolean exist(String pid) {
        String sql = "SELECT COUNT(id) FROM " + DevDataflowResourceWriteMapper.TABLE + " WHERE pid=? AND deleted=?";
        String[] args = new String[2];
        args[0] = pid;
        args[1] = YNEnum.NO.getIntCharCode();
        return xcfJdbcTemplate.queryForObject(sql, Integer.class, args) > 0;
    }

    public Boolean updateToDeletedByPId(String pid) {
        String sql = "update " + DevDataflowResourceWriteMapper.TABLE + " set deleted=? , synced=? where pid=? AND deleted=?";
        return xcfJdbcTemplate.update(sql, new Object[]{YNEnum.YES.getIntCharCode(), YNEnum.NO.getIntCharCode(), pid, YNEnum.NO.getIntCharCode()}) > 0;
    }

    public void updateToSyncByIds(List<String> ids) {
        String[] args = new String[ids.size() + 1];
        StringBuilder sqlBuilder = new StringBuilder(" UPDATE ").append(DevDataflowResourceWriteMapper.TABLE).append(" SET synced=? ")
                .append(" WHERE id IN ");
        sqlBuilder.append("(").append("?");
        args[0] = YNEnum.YES.getIntCharCode();
        args[1] = ids.get(0);
        if (ids.size() > 1) {
            for (int i = 1; i < ids.size(); i++) {
                sqlBuilder.append(",").append("?");
                args[i + 1] = ids.get(i);
            }
        }
        sqlBuilder.append(")");
        xcfJdbcTemplate.update(sqlBuilder.toString(), args);
    }
}
