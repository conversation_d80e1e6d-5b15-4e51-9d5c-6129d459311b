package com.xmcares.platform.admin.common.errors;

import com.xmcares.framework.commons.error.ErrorCode;

/**
 *
 * <AUTHOR> chenYG
 * @date : 2022/3/29 9:57
 */
public class BusinessException extends com.xmcares.framework.commons.error.BusinessException {
    public BusinessException(String message) {
        super(message);
    }
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
    }

    public BusinessException(ErrorCode code) {
        super(code);
    }

    public BusinessException(ErrorCode code, String message) {
        super(code, message);
    }

    public BusinessException(ErrorCode code, String message, Throwable cause) {
        super(code, message, cause);
    }

    public BusinessException(ErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
    }

    public BusinessException(ErrorCode code, String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(code, message, cause, enableSuppression, writableStackTrace);
    }

    public BusinessException() {
    }
}
