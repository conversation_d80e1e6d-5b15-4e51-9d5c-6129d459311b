{
  "plugin_name": "Pulsar",
  <#if fieldMappings?? && fieldMappings?has_content>
  "plugin_input": "${sink.plugin_input!'default_transform_output'}",
  <#else>
  "plugin_input": "${sink.plugin_input!'default_source_output'}",
  </#if>
  "client.service-url": "${destDatasource.url}",
  "admin.service-url": "${destDatasource.adminServiceUrl}",
  "topic": "${dest.topic}",
  "format": "${dest.format!'json'}"
}
