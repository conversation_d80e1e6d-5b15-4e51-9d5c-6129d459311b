/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/3/6
 */
package com.xmcares.platform.admin.integrator.datasync.model;

/**
 * 数据同步支持的类型：1，基于内部数据源；2，基于附加代码包的插件
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum DatasyncTypes {
    /**
     * 内置数据源
     */
    INNER('1') {

        public boolean supports (String orgin) {

            return false;
        }
    },

    /**
     * 附件代码
     */
    ADDON('2') ;

    private char code;


    DatasyncTypes(char code) {
        this.code = code;
    }

    public static DatasyncTypes valueFromCode(char typeCode) {
        switch (typeCode) {
            case '1': return INNER;
            case '2': return ADDON;
            default: return null;
        }
    }

    public char code() {
        return code;
    }
}
