package com.xmcares.platform.admin.metadata.database.model;

import com.xmcares.platform.admin.common.validation.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * 数据源资源同步任务表 模型
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/9
 */
@ApiModel(value = DatasourceResourceSyncTask.TABLE, description = "数据源资源同步任务表")
public class DatasourceResourceSyncTask {
    public static final String TABLE = "bdp_meta_datasource_resource_sync_task";




    /** ID */
    @NotNull(message = "ID不允许为空", groups = Update.class)
    @ApiModelProperty(value = "ID")
    private String id;
    /** 任务类型 1：添加 2：删除 */
    @ApiModelProperty(value = "type")
    private String type;
    /** 要操作的记录 */
    @ApiModelProperty(value = "record")
    private String record;

    public static DatasourceResourceSyncTask add(String id, String path) {
        DatasourceResourceSyncTask result = new DatasourceResourceSyncTask();
        result.setId(id);
        result.setType("1");
        result.setRecord(id + "=" + id + ", " + path);
        return result;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRecord() {
        return record;
    }

    public void setRecord(String record) {
        this.record = record;
    }

}
