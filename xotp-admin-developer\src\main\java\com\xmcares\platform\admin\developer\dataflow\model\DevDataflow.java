package com.xmcares.platform.admin.developer.dataflow.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xmcares.platform.admin.common.validation.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel(value = DevDataflow.TABLE, description = "数据开发工作流信息")
public class DevDataflow implements Serializable {

    public static final String TABLE = "bdp_dev_dataflow";

    /** ID */
    @NotNull(message = "ID不允许为空", groups = Update.class)
    @ApiModelProperty(value = "ID")
    private String id;

    /** 创建时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 维护人ID */
    @ApiModelProperty(value = "维护人ID")
    private String createUser;

    /** 流程定义的名称 */
    @ApiModelProperty(value = "流程定义的名称")
    private String name;

    /** 界面配置信息 */
    @ApiModelProperty(value = "界面配置信息")
    private String graphOptions;

    /** 是否删除 */
    @ApiModelProperty(value = "是否删除", notes = "0: 否 1: 是")
    private String deleted;

    /** 是否上架 */
    @ApiModelProperty(value = "是否上架", notes = "0: 否 1: 是")
    private String shelved;

    /** 备注信息 */
    @ApiModelProperty(value = "备注信息")
    private String remark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGraphOptions() {
        return graphOptions;
    }

    public void setGraphOptions(String graphOptions) {
        this.graphOptions = graphOptions;
    }

    public String getDeleted() {
        return deleted;
    }

    public void setDeleted(String deleted) {
        this.deleted = deleted;
    }

    public String getShelved() {
        return shelved;
    }

    public void setShelved(String shelved) {
        this.shelved = shelved;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
