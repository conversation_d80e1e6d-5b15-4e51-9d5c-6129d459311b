package com.xmcares.platform.admin.asset.model.vo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/6/2 15:11
 */
public class AssetSummaryStatusQueryVO implements Serializable {
    private String name;
    private String metricDate;
    private List<StatusCountInfo> data;

    public static class StatusCountInfo{
        private String key;
        private String value;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMetricDate() {
        return metricDate;
    }

    public void setMetricDate(String metricDate) {
        this.metricDate = metricDate;
    }

    public List<StatusCountInfo> getData() {
        return data;
    }

    public void setData(List<StatusCountInfo> data) {
        this.data = data;
    }
}
