package com.xmcares.platform.admin.integrator.datasync.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "XxlJobLog与DatasyncJobInstance联表查询结果")
public class XxlJobLogWithInstanceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    // XxlJobLog 字段
    @ApiModelProperty(value = "日志ID")
    private Long id;

    @ApiModelProperty(value = "任务名称")
    private String jobName;

    @ApiModelProperty(value = "执行器主键ID")
    private Integer jobGroup;

    @ApiModelProperty(value = "任务，主键ID")
    private Integer jobId;

    @ApiModelProperty(value = "执行器地址，本次执行的地址")
    private String executorAddress;

    @ApiModelProperty(value = "执行器任务handler")
    private String executorHandler;

    @ApiModelProperty(value = "执行器任务参数")
    private String executorParam;

    @ApiModelProperty(value = "执行器任务分片参数，格式如 1/2")
    private String executorShardingParam;

    @ApiModelProperty(value = "失败重试次数")
    private Integer executorFailRetryCount;

    @ApiModelProperty(value = "调度-时间")
    private Date triggerTime;

    @ApiModelProperty(value = "调度-结果")
    private Integer triggerCode;

    @ApiModelProperty(value = "调度-日志")
    private String triggerMsg;

    @ApiModelProperty(value = "执行-时间")
    private Date handleTime;

    @ApiModelProperty(value = "执行-状态")
    private Integer handleCode;

    @ApiModelProperty(value = "执行-日志")
    private String handleMsg;

    @ApiModelProperty(value = "告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败")
    private Byte alarmStatus;

    // DatasyncJobInstance 字段
    @ApiModelProperty(value = "实例ID")
    private String instanceId;

    @ApiModelProperty(value = "同步作业ID")
    private String instanceJobId;

    @ApiModelProperty(value = "作业参数")
    private String jobParams;

    @ApiModelProperty(value = "调度ID")
    private String scheduleId;

    @ApiModelProperty(value = "启动时间")
    private Date scheduleTime;

    @ApiModelProperty(value = "结束时间")
    private Date finishTime;

    @ApiModelProperty(value = "作业实例执行的状态：0-Dead Started; 1-Running; 2-Finished; 3-Failed；4-Canceled/Stopped")
    private Byte status;

    @ApiModelProperty(value = "状态消息")
    private String statusMessage;

    @ApiModelProperty(value = "接收数量")
    private Integer receivedCount;

    @ApiModelProperty(value = "接收QPS")
    private Double receviedQps;

    @ApiModelProperty(value = "写入数量")
    private Integer writedCount;

    @ApiModelProperty(value = "写入QPS")
    private Double writedQps;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getJobGroup() {
        return jobGroup;
    }

    public void setJobGroup(Integer jobGroup) {
        this.jobGroup = jobGroup;
    }

    public Integer getJobId() {
        return jobId;
    }

    public void setJobId(Integer jobId) {
        this.jobId = jobId;
    }

    public String getExecutorAddress() {
        return executorAddress;
    }

    public void setExecutorAddress(String executorAddress) {
        this.executorAddress = executorAddress;
    }

    public String getExecutorHandler() {
        return executorHandler;
    }

    public void setExecutorHandler(String executorHandler) {
        this.executorHandler = executorHandler;
    }

    public String getExecutorParam() {
        return executorParam;
    }

    public void setExecutorParam(String executorParam) {
        this.executorParam = executorParam;
    }

    public String getExecutorShardingParam() {
        return executorShardingParam;
    }

    public void setExecutorShardingParam(String executorShardingParam) {
        this.executorShardingParam = executorShardingParam;
    }

    public Integer getExecutorFailRetryCount() {
        return executorFailRetryCount;
    }

    public void setExecutorFailRetryCount(Integer executorFailRetryCount) {
        this.executorFailRetryCount = executorFailRetryCount;
    }

    public Date getTriggerTime() {
        return triggerTime;
    }

    public void setTriggerTime(Date triggerTime) {
        this.triggerTime = triggerTime;
    }

    public Integer getTriggerCode() {
        return triggerCode;
    }

    public void setTriggerCode(Integer triggerCode) {
        this.triggerCode = triggerCode;
    }

    public String getTriggerMsg() {
        return triggerMsg;
    }

    public void setTriggerMsg(String triggerMsg) {
        this.triggerMsg = triggerMsg;
    }

    public Date getHandleTime() {
        return handleTime;
    }

    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    public Integer getHandleCode() {
        return handleCode;
    }

    public void setHandleCode(Integer handleCode) {
        this.handleCode = handleCode;
    }

    public String getHandleMsg() {
        return handleMsg;
    }

    public void setHandleMsg(String handleMsg) {
        this.handleMsg = handleMsg;
    }

    public Byte getAlarmStatus() {
        return alarmStatus;
    }

    public void setAlarmStatus(Byte alarmStatus) {
        this.alarmStatus = alarmStatus;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getInstanceJobId() {
        return instanceJobId;
    }

    public void setInstanceJobId(String instanceJobId) {
        this.instanceJobId = instanceJobId;
    }

    public String getJobParams() {
        return jobParams;
    }

    public void setJobParams(String jobParams) {
        this.jobParams = jobParams;
    }

    public String getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(String scheduleId) {
        this.scheduleId = scheduleId;
    }

    public Date getScheduleTime() {
        return scheduleTime;
    }

    public void setScheduleTime(Date scheduleTime) {
        this.scheduleTime = scheduleTime;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getStatusMessage() {
        return statusMessage;
    }

    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }

    public Integer getReceivedCount() {
        return receivedCount;
    }

    public void setReceivedCount(Integer receivedCount) {
        this.receivedCount = receivedCount;
    }

    public Double getReceviedQps() {
        return receviedQps;
    }

    public void setReceviedQps(Double receviedQps) {
        this.receviedQps = receviedQps;
    }

    public Integer getWritedCount() {
        return writedCount;
    }

    public void setWritedCount(Integer writedCount) {
        this.writedCount = writedCount;
    }

    public Double getWritedQps() {
        return writedQps;
    }

    public void setWritedQps(Double writedQps) {
        this.writedQps = writedQps;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }
}
