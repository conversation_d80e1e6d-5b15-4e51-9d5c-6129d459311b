package com.xmcares.platform.admin.dataservice.dataset.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xmcares.platform.admin.common.validation.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/29 09:16
 */
@ApiModel(value = MetaDataSetQuery.TABLE, description = "数据模型执行记录")
public class MetaDataSetQuery {
    public static final String TABLE = "bdp_api_dataset_service_query";

    /**
     * ID
     */
    @NotNull(message = "ID不允许为空", groups = Update.class)
    @ApiModelProperty(value = "ID")
    private String id;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 是否删除
     */    @ApiModelProperty(value = "是否删除", notes = "0: 否 1: 是")
    @NotNull(message = "deleted不允许为空")
    private char deleted = '0';

    /**
     * 服务ID
     */
    @NotNull(message = "serviceId不允许为空")
    @ApiModelProperty(value = "服务ID")
    private String serviceId;

    /**
     * 数据集ID
     */
    @NotNull(message = "datasetId不允许为空")
    @ApiModelProperty(value = "数据集ID")
    private String datasetId;
    @NotNull(message = "datasetHash不允许为空")
    @ApiModelProperty(value = "模型SQL HASH")
    private String datasetHash;

    @NotNull(message = "startTime不允许为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始执行时间")
    private Date startTime;    @NotNull(message = "runningTime不允许为空")
    @ApiModelProperty(value = "执行耗时")
    private Integer runningTime = -1;

    @NotNull(message = "resultRows不允许为空")
    @ApiModelProperty(value = "返回行数")
    private Integer resultRows = 0;

    @ApiModelProperty(value = "执行异常日志")    private String resultError;

    /**
     * 应用密钥
     */
    @NotNull(message = "appKey不允许为空")
    @ApiModelProperty(value = "应用密钥")
    private String appKey;

    /**
     * 客户端信息
     */
    @ApiModelProperty(value = "客户端信息")
    private String clientInfo;


    public static String getTABLE() {
        return TABLE;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public char getDeleted() {
        return deleted;
    }

    public void setDeleted(char deleted) {
        this.deleted = deleted;
    }    public String getDatasetId() {
        return datasetId;
    }

    public void setDatasetId(String datasetId) {
        this.datasetId = datasetId;
    }

    public String getDatasetHash() {
        return datasetHash;
    }

    public void setDatasetHash(String datasetHash) {
        this.datasetHash = datasetHash;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Integer getRunningTime() {
        return runningTime;
    }

    public void setRunningTime(Integer runningTime) {
        this.runningTime = runningTime;
    }

    public Integer getResultRows() {
        return resultRows;
    }

    public void setResultRows(Integer resultRows) {
        this.resultRows = resultRows;
    }

    public String getResultError() {
        return resultError;
    }    public void setResultError(String resultError) {
        this.resultError = resultError;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getClientInfo() {
        return clientInfo;
    }

    public void setClientInfo(String clientInfo) {
        this.clientInfo = clientInfo;
    }
}
