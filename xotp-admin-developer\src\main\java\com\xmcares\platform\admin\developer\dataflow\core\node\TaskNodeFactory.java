package com.xmcares.platform.admin.developer.dataflow.core.node;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xmcares.framework.commons.util.CollectionUtils;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.errors.SystemException;
import com.xmcares.platform.admin.developer.common.enums.DeveloperTaskType;
import com.xmcares.platform.admin.developer.dataflow.core.node.impl.*;
import com.xmcares.platform.admin.developer.dataflow.vo.DevDataflowLineVo;
import com.xmcares.platform.admin.developer.dataflow.vo.DevDataflowNodeVo;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/29 16:14
 **/
public class TaskNodeFactory {

    public static class CommonNodeVo {
        /** 任务ID */
        private String id;
        /** 任务名称 */
        private String label;
        /** 任务类型 */
        private String type;
        /** 节点所处的组ID */
        private String groupId;
        /** 节点的前置ID列表 */
        private List<String> preIds;
        /** 节点的后置ID列表 */
        private List<String> postIds;
        /** 节点参数信息 */
        private Object params;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getGroupId() {
            return groupId;
        }

        public void setGroupId(String groupId) {
            this.groupId = groupId;
        }

        public List<String> getPreIds() {
            return preIds;
        }

        public void setPreIds(List<String> preIds) {
            this.preIds = preIds;
        }

        public List<String> getPostIds() {
            return postIds;
        }

        public void setPostIds(List<String> postIds) {
            this.postIds = postIds;
        }

        public Object getParams() {
            return params;
        }

        public void setParams(Object params) {
            this.params = params;
        }
    }

    private final static Map<String, Class<?>> NODES = new HashMap<>();
    static {
        NODES.put(DeveloperTaskType.BATCH_SQL.name(), BatchSqlTaskNode.class);
        NODES.put(DeveloperTaskType.STREAM_FLINK.name(), StreamFlinkTaskNode.class);
        NODES.put(DeveloperTaskType.SYS_BEGIN.name(), BeginTaskNode.class);
        NODES.put(DeveloperTaskType.SYS_END.name(), EndTaskNode.class);
        NODES.put(DeveloperTaskType.SYS_GLOBAL_PARAM.name(), GlobalParamTaskNode.class);
    }

    public static ITaskNode<DevDataflowNodeVo> create(DeveloperTaskType nodeType, CommonNodeVo nodeInfo, TaskNodeManager<DevDataflowNodeVo> manager) throws SystemException{
        if (NODES.containsKey(nodeType.name())) {
            try {
                BaseTaskNode baseTaskNode = (BaseTaskNode) NODES.get(nodeType.name()).newInstance();
                baseTaskNode.init(nodeInfo,manager);
                return baseTaskNode;
            } catch (InstantiationException | IllegalAccessException e) {
                throw new SystemException("获取节点实例失败", e);
            }
        }
        throw new SystemException("节点类型未实现！");
    }

    public static ITaskNodeManager<DevDataflowNodeVo> create(String graphOptions) {
        JSONObject graphOptionsObj = JSON.parseObject(graphOptions);
        JSONArray nodeArray = graphOptionsObj.getJSONArray("nodes");
        JSONArray lineArrays = graphOptionsObj.getJSONArray("edges");
        if (lineArrays == null){
            lineArrays = JSON.parseArray("[]");
        }
        if (CollectionUtils.isEmpty(nodeArray)) { throw new BusinessException("节点不允许为空！"); }

        List<CommonNodeVo> nodes = buildLineNodes(nodeArray.toJavaList(CommonNodeVo.class),
                lineArrays.toJavaList(DevDataflowLineVo.class));
        // 1. 获取分组信息
        Map<String, String> idGroup = createGroup(nodes);
        for (CommonNodeVo node : nodes) {
            node.setGroupId(idGroup.get(node.getId()));
        }
        // 2. 校验记录是否均设置了组信息
        List<CommonNodeVo> errorNodes = nodes.stream().filter(node-> StringUtils.isEmpty(node.groupId)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(errorNodes)) {
            throw new BusinessException(String.format("节点【%s】连接线信息不正确！",errorNodes.get(0).getLabel()));
        }
        // 3. 创建任务节点管理器
        TaskNodeManager<DevDataflowNodeVo> result = new TaskNodeManager<>();
        List<ITaskNode<DevDataflowNodeVo>> nodeAgents = new ArrayList<>();
        for (CommonNodeVo node : nodes) {
            DeveloperTaskType developerTaskType = DeveloperTaskType.match(node.getType());
            if (developerTaskType == null) {
                throw new SystemException(String.format("【%s】未找到匹配的任务类型", node.getLabel()));
            }
            nodeAgents.add(create(developerTaskType, node, result));
        }
        result.init(nodeAgents);
        return result;
    }


    /**
     * 根据线的内容构建节点的Pre表与Post表
     * @param nodes
     * @param lines
     * @return
     */
    private static List<CommonNodeVo> buildLineNodes(List<CommonNodeVo> nodes, List<DevDataflowLineVo> lines) {
        if (CollectionUtils.isEmpty(lines)) { return nodes; }
        Map<String, CommonNodeVo> resultHandler = nodes.stream().collect(Collectors.toMap(CommonNodeVo::getId, v->v));
        for (DevDataflowLineVo line : lines) {
            // 1. 找到前置节点， 并在前置节点的后置节点列表中追加记录
            if (resultHandler.containsKey(line.getSource())) {
                List<String> postIds = resultHandler.get(line.getSource()).getPostIds();
                if (CollectionUtils.isEmpty(postIds)) {
                    postIds = new ArrayList<>();
                }
                postIds.add(line.getTarget());
                resultHandler.get(line.getSource()).setPostIds(postIds);
            } else {
                throw new BusinessException("非法的连接线，未找到对应的前置节点");
            }
            // 2. 找到后置节点， 并在后置节点的前置列表中追加记录
            if (resultHandler.containsKey(line.getTarget())) {
                List<String> preIds = resultHandler.get(line.getTarget()).getPreIds();
                if (CollectionUtils.isEmpty(preIds)) {
                    preIds = new ArrayList<>();
                }
                preIds.add(line.getSource());
                resultHandler.get(line.getTarget()).setPreIds(preIds);
            } else {
                throw new BusinessException("非法的连接线，未找到对应的后置节点");
            }
        }
        return new ArrayList<>(resultHandler.values());
    }

    /**
     * 创建组信息
     * @param nodes
     * @return
     */
    private static Map<String, String> createGroup(List<CommonNodeVo> nodes) {
        Map<String, String> result = new HashMap<>();
        Map<String, List<CommonNodeVo>> groupNodeResult = nodes.stream().collect(Collectors.groupingBy(node -> {
            // 单一的无关联节点
            if (CollectionUtils.isEmpty(node.getPreIds()) && CollectionUtils.isEmpty(node.getPostIds())) {
                return "simple";
            }
            // 存在关联关系的父节点
            if (CollectionUtils.isEmpty(node.getPreIds()) && !CollectionUtils.isEmpty(node.getPostIds())) {
                return "header";
            }
            // 身体节点 （包含End）
            return "body";
        }));
        // 处理单一无关联节点的组
        result.putAll(Optional.ofNullable(groupNodeResult.get("simple")).orElse(
                new ArrayList<>()).stream().map(node-> new AbstractMap.SimpleEntry<>(node.getId(), SnowflakeGenerator.getNextId() + ""))
                .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue)));
        // 处理头节点的组
        result.putAll(Optional.ofNullable(groupNodeResult.get("header")).orElse(
                new ArrayList<>()).stream().map(node-> new AbstractMap.SimpleEntry<>(node.getId(), SnowflakeGenerator.getNextId() + ""))
                .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue)
                ));
        // 处理身体节点
        List<CommonNodeVo> headerNodes = Optional.ofNullable(groupNodeResult.get("header")).orElse(
                new ArrayList<>());
        Map<String, CommonNodeVo> bodyNodes = Optional.ofNullable(groupNodeResult.get("body")).orElse(
                new ArrayList<>()).stream().collect(Collectors.toMap(CommonNodeVo::getId, t->t));
        for (CommonNodeVo headerNode : headerNodes) {
            createBodyGroup(result, bodyNodes, result.get(headerNode.getId()), headerNode.getPostIds());
        }
        return result;
    }

    /**
     * 创建节点为身体的组信息
     * @param result
     * @param nodes
     * @param groupId
     * @param postIds
     */
    private static void createBodyGroup(Map<String, String> result, Map<String, CommonNodeVo> nodes, String groupId, List<String> postIds) {
        if (CollectionUtils.isEmpty(postIds)){return;}
        for (String postId : postIds) {
            if (result.containsKey(postId)) {
                continue;
            }
            result.put(postId, groupId);
            CommonNodeVo findResult = nodes.get(postId);
            createBodyGroup(result, nodes, groupId, findResult.getPostIds());
        }
    }
}
