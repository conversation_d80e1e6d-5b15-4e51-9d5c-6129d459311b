/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/3/3
 */
package com.xmcares.platform.admin.developer.dataflow.repository.xxljob;

import com.xmcares.platform.admin.common.vo.ReturnT;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 4.0.0
 */
@FeignClient(name = "${xbdp.feign.scheduler-service.name:scheduler-service}",
        url = "${xbdp.feign.scheduler-service.url:}"
)
public interface ProcessTaskClient {

    @GetMapping(value = "/xbdp/process/api/begin")
    ReturnT<Boolean> begin(@RequestParam("processId") String processId);

    @GetMapping(value = "/xbdp/process/api/reRun")
    ReturnT<Boolean> reRun(@RequestParam("processId") String processId);

    @GetMapping(value = "/xbdp/process/api/stop")
    ReturnT<Boolean> stop(@RequestParam("processId") String processId);

    @GetMapping(value = "/xbdp/process/api/pause")
    ReturnT<Boolean> pause(@RequestParam("processId") String processId);

    @GetMapping(value = "/xbdp/process/api/go")
    ReturnT<Boolean> goOn(@RequestParam("processId") String processId);

    @GetMapping("/xbdp/process/api/remove")
    ReturnT<Boolean> remove(@RequestParam("processId") String processId);
}
