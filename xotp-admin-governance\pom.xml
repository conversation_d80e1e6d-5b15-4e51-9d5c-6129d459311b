<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xmcares.platform</groupId>
        <artifactId>xotp-admin</artifactId>
        <version>1.0.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>xotp-admin-governance</artifactId>

    <properties>

    </properties>


    <dependencies>
        <!-- :::: XOTP其他模块依赖 :::: 开始！-->
        <dependency>
            <groupId>com.xmcares.platform</groupId>
            <artifactId>xotp-admin-metadata</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xmcares.platform</groupId>
            <artifactId>xotp-admin-dataservice</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xmcares.platform</groupId>
            <artifactId>xotp-admin-developer</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- :::: XOTP其他模块依赖 :::: 结束！-->


        <!-- :::: XCNF & Spring Boot & Spring Cloud :::: 开始！-->

        <!-- :::: XCNF & Spring Boot & Spring Cloud :::: 结束！-->

    </dependencies>

</project>