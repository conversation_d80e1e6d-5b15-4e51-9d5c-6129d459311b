package com.xmcares.platform.admin.metadata.database.web;

import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.database.DatabaseMetaQuery;
import com.xmcares.platform.admin.common.database.DatabaseMetaQueryFactory;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import com.xmcares.platform.admin.metadata.database.service.DatasourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> chenYG
 * @date : 2022/4/1 8:46
 */
@RestController
@RequestMapping("/datasourceApi")
public class DatasourceApiController {

    @Autowired
    private DatasourceService service;

    @Autowired
    private DatabaseMetaQueryFactory metaQueryFactory;

    @GetMapping("/maxId")
    public String findMaxId(@RequestParam(name = "datasourceId") String datasourceId,
                            @RequestParam(name = "tableName") String tableName,
                            @RequestParam(name = "primaryKey") String primaryKey) {
        Datasource datasource = service.getDatasource(datasourceId);
        if (datasource == null) { throw new BusinessException("数据源不存在"); }
        DatabaseMetaQuery metaQuery = metaQueryFactory.getDatabaseMetaQuery(datasource.toDataSourceOptions());
        return  metaQuery.getColumnMaxValue(tableName, primaryKey) + "";
    }


}
