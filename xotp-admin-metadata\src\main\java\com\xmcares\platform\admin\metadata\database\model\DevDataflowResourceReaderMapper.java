/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： zhanlh
 * Date：2022/7/18
 */
package com.xmcares.platform.admin.metadata.database.model;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.framework.commons.util.string.StringUtils;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.common.validation.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 3.0.3
 */
@ApiModel(value = DevDataflowResourceReaderMapper.TABLE, description = "资源开发读取信息表")
public class DevDataflowResourceReaderMapper implements Serializable {
    public static final String TABLE = "bdp_datasync_model_reader_mapper";

    public static DevDataflowResourceReaderMapper init(String id, String pluginPath) {
        DevDataflowResourceReaderMapper result = new DevDataflowResourceReaderMapper();
        result.setId(SnowflakeGenerator.getNextId().toString());
        result.setPid(id);
        result.setPath(StringUtils.format(pluginPath, result.getId()));
        result.setDeleted(YNEnum.NO.getIntCharCode());
        result.setUploadTime(new Date());
        result.setSynced(YNEnum.NO.getIntCharCode());
        return result;
    }

    /** ID */
    @NotNull(message = "ID不允许为空", groups = Update.class)
    @ApiModelProperty(value = "ID")
    private String id;
    /** PID */
    @NotNull(message = "PID不允许为空", groups = Update.class)
    @ApiModelProperty(value = "PID")
    private String pid;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date uploadTime;
    /** 资源路径 */
    @ApiModelProperty(value = "资源路径")
    private String path;

    @ApiModelProperty(value = "是否删除")
    private String deleted;

    @ApiModelProperty(value = "是否同步")
    private String synced;



    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getDeleted() {
        return deleted;
    }

    public void setDeleted(String deleted) {
        this.deleted = deleted;
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public String getSynced() {
        return synced;
    }

    public void setSynced(String synced) {
        this.synced = synced;
    }

    public String toLine() {
        return id + "=" + id +
                "," + path;
    }
}
