{"properties": [{"name": "xxl.job.access-token", "type": "java.lang.String", "description": "Access token for secure communication between executor and admin. Leave empty if not needed.", "defaultValue": "default_token"}, {"name": "xxl.job.i18n", "type": "java.lang.String", "description": "Language setting for XXL-JOB: zh_<PERSON><PERSON> (Simplified Chinese), zh_TC (Traditional Chinese), en (English).", "defaultValue": "zh_CN"}, {"name": "xxl.job.timeout", "type": "java.lang.Integer", "description": "Request timeout in seconds for communication between executor and admin.", "defaultValue": 3}, {"name": "xxl.job.triggerpool.fast.max", "type": "java.lang.Integer", "description": "Maximum thread count for the fast trigger pool in the executor.", "defaultValue": 200}, {"name": "xxl.job.triggerpool.slow.max", "type": "java.lang.Integer", "description": "Maximum thread count for the slow trigger pool in the executor.", "defaultValue": 100}, {"name": "xxl.job.logretentiondays", "type": "java.lang.Integer", "description": "Number of days to retain job log data. Set to -1 for no cleanup.", "defaultValue": 30}, {"name": "spring.mail.from", "type": "java.lang.String", "description": "Email address used as the sender in xxl-job mail notifications."}], "hints": []}