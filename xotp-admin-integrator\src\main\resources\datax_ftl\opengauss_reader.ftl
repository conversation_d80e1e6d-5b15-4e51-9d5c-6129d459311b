{
    "name": "gaussdbreader",
    "parameter": {
        "username": "${orginDatasource.username}",
        "password": "${orginDatasource.password}",
        "column": [
            <#list orginFieldNames! as field>
                "${field}"<#if field_has_next>,</#if>
            </#list>
        ],
        <#if orgin.querySql??>
        <#else>
            <#if orgin.where??>"where":"${orgin.where}",</#if>
        </#if>
        <#if orgin.splitPk??>"splitPk":"${orgin.splitPk}",</#if>
            "connection": [
            {
                <#if orgin.querySql??>
                    "querySql": [
                        <#list orgin.querySql?split("#") as query>
                            "${query}"<#if query_has_next>,</#if>
                        </#list>
                    ],
                </#if>
                "jdbcUrl": [
                    "${orginDatasource.url}"
                ],
                <#if orgin.querySql??>
                <#else>
                    <#if orgin.table??>"table":["${orgin.table}"]</#if>
                </#if>
            }
            ]
    }
}
