package com.xmcares.platform.admin.quality.repository;

import com.alibaba.fastjson.JSON;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.platform.admin.asset.model.AsstStatsMetrics;
import com.xmcares.platform.admin.asset.model.vo.ServiceHotTopsQueryVO;
import com.xmcares.platform.admin.quality.model.QltyRuleMetrics;
import com.xmcares.platform.admin.quality.model.QltyRuleScheduler;
import com.xmcares.platform.admin.quality.model.vo.QualitySummaryRuleStateCountVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/25 09:10
 */
@Repository
public class QualitySummaryRepository {
    private static final Logger LOG = LoggerFactory.getLogger(QualitySummaryRepository.class);

    @Autowired
    XcfJdbcTemplate xcfJdbcTemplate;

    public static final String TABLE_BDP_QLTY_RULE_METRICS = "bdp_qlty_rule_metrics";
    public static final String TABLE_BDP_QLTY_RULE_SCHEDULER = "bdp_qlty_rule_scheduler";
    public static final String TABLE_BDP_META_DATAWARE = "bdp_meta_dataware";
    public static final String TABLE_BDP_META_DATATABLE = "bdp_meta_datatable";
    public static final SimpleDateFormat metricDateFormat = new SimpleDateFormat("yyyy-MM-dd");

    public List<QltyRuleMetrics> queryQltyRuleMetricsByDataware(String[] datawareIds, String[] ruleId, String ruleLevel, String beginDate, String endDate) {
        try {
            String datawareIdsStr = null;
            if (datawareIds != null) {
                datawareIdsStr = Arrays.stream(datawareIds).collect(Collectors.joining("','", "('", "')"));
            }
            String ruleIdStr = null;
            if (ruleId != null) {
                ruleIdStr = Arrays.stream(ruleId).collect(Collectors.joining("','", "('", "')"));
            }

            if (beginDate == null || beginDate.length() == 0) {
                beginDate = metricDateFormat.format(new Date());
            }
            if (endDate == null || endDate.length() == 0) {
                endDate = metricDateFormat.format(new Date());
            }
            List<Map<String, Object>> step1 = xcfJdbcTemplate.<HashMap<String, Object>>queryForList(
                    " select " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".id id," +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".rule_id rule_id," +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".dim_code dim_code," +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".rule_scheduler_id rule_scheduler_id," +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".dataware_id dataware_id," +
                            " " + TABLE_BDP_META_DATAWARE + ".name dataware_name ," +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".datatable_id datatable_id," +
                            " " + TABLE_BDP_META_DATATABLE + ".name datatable_name, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".rule_level rule_level, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".rule_targets rule_targets, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".metric_date metric_date, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".metric_value metric_value, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".check_time check_time, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".check_status check_status, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".check_report check_report, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".warn_status warn_status, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".create_time create_time, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".update_time update_time " +
                            " from " + TABLE_BDP_QLTY_RULE_METRICS +
                            " left join " + TABLE_BDP_META_DATAWARE + " on " +
                            " " + TABLE_BDP_META_DATAWARE + ".id=" + TABLE_BDP_QLTY_RULE_METRICS + ". dataware_id " +
                            " left join " + TABLE_BDP_META_DATATABLE +
                            "  on " + TABLE_BDP_QLTY_RULE_METRICS + ".datatable_id=" + TABLE_BDP_META_DATATABLE + ".id " +
                            " where true " +
                            " and rule_hashcode='SCORE' " +
                            ((datawareIds == null || datawareIds.length == 0) ? (" and true ") : (" and " + TABLE_BDP_QLTY_RULE_METRICS + ".dataware_id in " + datawareIdsStr) + "  ") +
                            ((ruleId == null || ruleId.length == 0) ? (" and true ") : (" and " + TABLE_BDP_QLTY_RULE_METRICS + ".rule_id in " + ruleIdStr + "  ")) +
                            " and " + TABLE_BDP_QLTY_RULE_METRICS + ".rule_level ='" + ruleLevel + "'  " +
                            (" and " + TABLE_BDP_QLTY_RULE_METRICS + ".metric_date >='" + beginDate + "'  ") +
                            (" and " + TABLE_BDP_QLTY_RULE_METRICS + ".metric_date <='" + endDate + "' ")
            );

            //group and sort filter
            HashMap<String, List<Map<String, Object>>> step2 = new HashMap<>();
            for (Map<String, Object> step1Map : step1) {
                String key = String.valueOf(step1Map.getOrDefault("dim_code", "")) + String.valueOf(step1Map.getOrDefault("metric_date", "")) + String.valueOf(step1Map.getOrDefault("dataware_id", "")) + String.valueOf(step1Map.getOrDefault("rule_id", ""));
                step2.putIfAbsent(key, new ArrayList<Map<String, Object>>());
                step2.get(key).add(step1Map);
            }
            List<Map<String, Object>> step2Max = step2.values().stream().map(g -> g.stream().max((l1, l2) -> ((LocalDateTime) l1.get("create_time")).compareTo((LocalDateTime) l2.get("create_time")))).map(v -> v.get()).collect(Collectors.toList());

            List<QltyRuleMetrics> qltyRuleMetrics = step2Max.stream().map(m -> JSON.parseObject(JSON.toJSONString(m), QltyRuleMetrics.class)).collect(Collectors.toList());

            return qltyRuleMetrics;
        } catch (Exception e) {
            LOG.warn("queryQltyRuleMetricsByDataware", e);
            return null;
        }
    }

    public List<QltyRuleMetrics> queryQltyRuleMetricsTopN(String[] datawareIds, String ruleId, String ruleLevel, Integer topN, String order) {
        try {
            String datawareIdsStr = null;
            if (datawareIds != null) {
                datawareIdsStr = Arrays.stream(datawareIds).collect(Collectors.joining("','", "('", "')"));
            }

            List<Map<String, Object>> step1 = xcfJdbcTemplate.<HashMap<String, Object>>queryForList(
                    " select " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".id id," +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".rule_id rule_id," +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".dim_code dim_code," +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".rule_scheduler_id rule_scheduler_id," +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".dataware_id dataware_id," +
                            " " + TABLE_BDP_META_DATAWARE + ".name dataware_name ," +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".datatable_id datatable_id," +
                            " " + TABLE_BDP_META_DATATABLE + ".name datatable_name, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".rule_level rule_level, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".rule_targets rule_targets, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".metric_date metric_date, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".metric_value metric_value, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".check_time check_time, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".check_status check_status, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".check_report check_report, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".warn_status warn_status, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".create_time create_time, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".update_time update_time " +
                            " from " + TABLE_BDP_QLTY_RULE_METRICS +
                            " left join " + TABLE_BDP_META_DATAWARE + " on " +
                            " " + TABLE_BDP_META_DATAWARE + ".id=" + TABLE_BDP_QLTY_RULE_METRICS + ". dataware_id " +
                            " left join " + TABLE_BDP_META_DATATABLE +
                            "  on " + TABLE_BDP_QLTY_RULE_METRICS + ".datatable_id=" + TABLE_BDP_META_DATATABLE + ".id " +
                            " where true " +
                            " and rule_hashcode='SCORE' " +
                            ((datawareIds == null || datawareIds.length == 0) ? (" and true ") : (" and " + TABLE_BDP_QLTY_RULE_METRICS + ".dataware_id in " + datawareIdsStr) + "  ") +
                            " and " + TABLE_BDP_QLTY_RULE_METRICS + ".rule_id='" + ruleId + "'  " +
                            " and " + TABLE_BDP_QLTY_RULE_METRICS + ".rule_level ='" + ruleLevel + "' " +
                            " and " + TABLE_BDP_QLTY_RULE_METRICS + ".check_status ='1' "
            );

            //group and sort filter
            HashMap<String, List<Map<String, Object>>> step2 = new HashMap<>();
            for (Map<String, Object> step1Map : step1) {
                String key = String.valueOf(step1Map.getOrDefault("dataware_id", "")) + String.valueOf(step1Map.getOrDefault("datatable_id", ""));
                step2.putIfAbsent(key, new ArrayList<Map<String, Object>>());
                step2.get(key).add(step1Map);
            }
            List<Map<String, Object>> step2Max = step2.values().stream().map(g -> g.stream().max((l1, l2) -> ((LocalDateTime) l1.get("create_time")).compareTo((LocalDateTime) l2.get("create_time")))).map(v -> v.get()).collect(Collectors.toList());

            List<QltyRuleMetrics> qltyRuleMetrics = step2Max.stream().map(m -> JSON.parseObject(JSON.toJSONString(m), QltyRuleMetrics.class)).sorted((b1, b2) -> {
                if ("desc".equals(order.toLowerCase())) {
                    return Double.valueOf((Double.valueOf(b2.getMetric_value()) - Double.valueOf(b1.getMetric_value()))).intValue();
                } else {
                    return Double.valueOf((Double.valueOf(b1.getMetric_value()) - Double.valueOf(b2.getMetric_value()))).intValue();
                }
            }).limit(topN).collect(Collectors.toList());


            return qltyRuleMetrics;
        } catch (Exception e) {
            LOG.warn("queryQltyRuleMetricsTopN", e);
            return null;
        }
    }


    public QualitySummaryRuleStateCountVO ruleStateCount(String[] datawareIds, String beginDate, String endDate) {
        try {
            String datawareIdsStr = null;
            if (datawareIds != null) {
                datawareIdsStr = Arrays.stream(datawareIds).collect(Collectors.joining("','", "('", "')"));
            }
            if (beginDate == null || beginDate.length() == 0) {
                beginDate = metricDateFormat.format(new Date());
            }
            if (endDate == null || endDate.length() == 0) {
                endDate = metricDateFormat.format(new Date());
            }
            QualitySummaryRuleStateCountVO qualitySummaryRuleStateCountVO = new QualitySummaryRuleStateCountVO();
            AtomicInteger totalExecuteFaildCount = new AtomicInteger();
            AtomicInteger totalExecuteTotalCount = new AtomicInteger();
            AtomicInteger totalAlarmCount = new AtomicInteger();
            List<QualitySummaryRuleStateCountVO.DatatableSateCount> datatableSateCounts = xcfJdbcTemplate.<QualitySummaryRuleStateCountVO.DatatableSateCount>queryForEntities(
                    " select " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".metric_date date, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".dataware_id, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".datatable_id, " +
                            " sum(IF(check_status=0, 1, 0)) executeFaildCount, " +
                            " count(*) executeTotalCount, " +
                            " sum(warn_status) alarmCount " +
                            " from " + TABLE_BDP_QLTY_RULE_METRICS +
                            " where true " +
                            ((datawareIds == null || datawareIds.length == 0) ? (" and true ") : (" and " + TABLE_BDP_QLTY_RULE_METRICS + ".dataware_id in " + datawareIdsStr + "  ")) +
                            (" and " + TABLE_BDP_QLTY_RULE_METRICS + ".metric_date >='" + beginDate + "'  ") +
                            (" and " + TABLE_BDP_QLTY_RULE_METRICS + ".metric_date <='" + endDate + "' ") +
                            " and " + TABLE_BDP_QLTY_RULE_METRICS + ".check_status ='1' " +
                            " group by " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".dataware_id, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".datatable_id, " +
                            " " + TABLE_BDP_QLTY_RULE_METRICS + ".metric_date "
                    ,
                    new Object[]{},
                    QualitySummaryRuleStateCountVO.DatatableSateCount.class
            );
            datatableSateCounts.stream().forEach(v -> {
                totalExecuteFaildCount.addAndGet(v.getExecuteFaildCount());
                totalExecuteTotalCount.addAndGet(v.getExecuteTotalCount());
                totalAlarmCount.addAndGet(v.getAlarmCount());
            });
            qualitySummaryRuleStateCountVO.setDatatableSateCounts(datatableSateCounts);
            qualitySummaryRuleStateCountVO.setTotalAlarmCount(totalAlarmCount.get());
            qualitySummaryRuleStateCountVO.setTotalExecuteFaildCount(totalExecuteFaildCount.get());
            qualitySummaryRuleStateCountVO.setTotalExecuteTotalCount(totalExecuteTotalCount.get());

            return qualitySummaryRuleStateCountVO;
        } catch (Exception e) {
            LOG.warn("ruleStateCount", e);
            return null;
        }
    }

    public List<QltyRuleScheduler> queryDatatableScheduler(String[] datawareIds) {
        try {
            String datawareIdsStr = null;
            if (datawareIds != null) {
                datawareIdsStr = Arrays.stream(datawareIds).collect(Collectors.joining("','", "('", "')"));
            }

            List<QltyRuleScheduler> qltyRuleSchedulers = xcfJdbcTemplate.<QltyRuleScheduler>queryForEntities(
                    " select " +
                            " * " +
                            " from " + TABLE_BDP_QLTY_RULE_SCHEDULER +
                            " where true " +
                            " and dataware_id != '-1' " +
                            ((datawareIds == null || datawareIds.length == 0) ? (" and true ") : (" and dataware_id in " + datawareIdsStr))
                    ,
                    new Object[]{},
                    QltyRuleScheduler.class
            );

            return qltyRuleSchedulers;
        } catch (Exception e) {
            LOG.warn("queryDatatableSchedulers", e);
            return null;
        }
    }


}
