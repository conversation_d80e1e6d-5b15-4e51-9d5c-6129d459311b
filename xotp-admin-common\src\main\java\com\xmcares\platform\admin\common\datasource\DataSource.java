/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/10
 */
package com.xmcares.platform.admin.common.datasource;

/**
 * 平台定义的数据源的抽象接口（非jdbc标准中的javax.sql.DataSource）
 * <AUTHOR>
 * @since 1.0.0
 */
public interface DataSource extends AutoCloseable{

    /**
     * 测试数据源连通性/是否可用
     * @return true:可用,false:不可用
     */
    AvailableStatus testAvailable();

    /**
     *  获取数据源唯一名称
     * @return String 数据源唯一名称
     */
    String getName();


    class AvailableStatus {
        public boolean available;
        public String message;
        public AvailableStatus(boolean available, String message) {
            this.available = available;
            this.message = message;
        }

        public boolean isAvailable() {
            return available;
        }

        public String getMessage() {
            return message;
        }
    }

}
