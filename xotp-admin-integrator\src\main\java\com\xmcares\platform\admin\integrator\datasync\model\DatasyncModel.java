package com.xmcares.platform.admin.integrator.datasync.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xmcares.platform.admin.common.validation.Insert;
import com.xmcares.platform.admin.common.validation.Update;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/03/29 09:48:18
 * @version 1.0.0
 */
public class DatasyncModel implements Serializable{

	private static final long serialVersionUID = 5454155825314635342L;

	/** ID */
	@NotNull(message = "ID不允许为空", groups = Update.class)
	private String id;

    /** 创建时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private java.util.Date createTime;

    /** 更新时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private java.util.Date updateTime;

	/** 维护人ID */
	private String createUser;

	/** 集成模型名称 */
	@NotEmpty(message = "集成模型名称不允许为空", groups = Insert.class)
	@NotEmpty(message = "集成模型名称不允许为空", groups = Update.class)
	private String modelName;

	/** 运行模式 0: 永久运行 1:手动运行 */
	@NotEmpty(message = "运行模式 0: 永久运行 1:手动运行不允许为空", groups = Insert.class)
	@NotEmpty(message = "运行模式 0: 永久运行 1:手动运行不允许为空", groups = Update.class)
	private String runSchema;

	/** 集成类型 0:读 1:写 */
	@NotEmpty(message = "集成类型 0:读 1:写不允许为空", groups = Insert.class)
	@NotEmpty(message = "集成类型 0:读 1:写不允许为空", groups = Update.class)
	private String integrationType;

	/** 集成方式 0:内置 1:插件 */
	@NotEmpty(message = "集成方式 0:内置 1:插件不允许为空", groups = Insert.class)
	@NotEmpty(message = "集成方式 0:内置 1:插件不允许为空", groups = Update.class)
	private String integrationWay;

	/** 数据源模型ID */
	@NotEmpty(message = "数据源模型ID不允许为空", groups = Insert.class)
	@NotEmpty(message = "数据源模型ID不允许为空", groups = Update.class)
	private String datasourceModelId;

	/** 数据源模型名称 */
	private String datasourceModelType;

	/** 插件路径 */
	private String pluginPath;

    /** 插件路径 */
    private String pluginName;

	/** 描述说明 */
	private String desc;

	private String remark;

	/** 基础JSON模型数据 */
	@NotEmpty(message = "基础JSON模型数据不允许为空", groups = Insert.class)
	@NotEmpty(message = "基础JSON模型数据不允许为空", groups = Update.class)
	private String baseJson;

	/** 进阶JSON数据 */
	private String advJson;

	/** 高级JSON数据 */
	private String highJson;

	/** 列描述JSON数据 */
	@NotEmpty(message = "列描述JSON数据不允许为空", groups = Insert.class)
	@NotEmpty(message = "列描述JSON数据不允许为空", groups = Update.class)
	private String columnJson;


	public void setId(String value) {
		this.id = value;
	}
	public String getId() {
		return this.id;
	}
	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setCreateUser(String value) {
		this.createUser = value;
	}
	public String getCreateUser() {
		return this.createUser;
	}
	public void setModelName(String value) {
		this.modelName = value;
	}
	public String getModelName() {
		return this.modelName;
	}
	public void setRunSchema(String value) {
		this.runSchema = value;
	}
	public String getRunSchema() {
		return this.runSchema;
	}
	public void setIntegrationType(String value) {
		this.integrationType = value;
	}
	public String getIntegrationType() {
		return this.integrationType;
	}
	public void setIntegrationWay(String value) {
		this.integrationWay = value;
	}
	public String getIntegrationWay() {
		return this.integrationWay;
	}
	public void setDatasourceModelId(String value) {
		this.datasourceModelId = value;
	}
	public String getDatasourceModelId() {
		return this.datasourceModelId;
	}
	public void setPluginPath(String value) {
		this.pluginPath = value;
	}
	public String getPluginPath() {
		return this.pluginPath;
	}
	public void setDesc(String value) {
		this.desc = value;
	}
	public String getDesc() {
		return this.desc;
	}
	public void setRemark(String value) {
		this.remark = value;
	}
	public String getRemark() {
		return this.remark;
	}
	public void setBaseJson(String value) {
		this.baseJson = value;
	}
	public String getBaseJson() {
		return this.baseJson;
	}
	public void setAdvJson(String value) {
		this.advJson = value;
	}
	public String getAdvJson() {
		return this.advJson;
	}
	public void setHighJson(String value) {
		this.highJson = value;
	}
	public String getHighJson() {
		return this.highJson;
	}
	public void setColumnJson(String value) {
		this.columnJson = value;
	}
	public String getColumnJson() {
		return this.columnJson;
	}

    public String getPluginName() {
        return pluginName;
    }

    public void setPluginName(String pluginName) {
        this.pluginName = pluginName;
    }

	public String getDatasourceModelType() {
		return datasourceModelType;
	}

	public void setDatasourceModelType(String datasourceModelType) {
		this.datasourceModelType = datasourceModelType;
	}
}

