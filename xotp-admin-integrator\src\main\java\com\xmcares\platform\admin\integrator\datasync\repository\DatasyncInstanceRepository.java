package com.xmcares.platform.admin.integrator.datasync.repository;

import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.CodeGenerator;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.integrator.datasync.model.DatasyncInstance;
import com.xmcares.platform.admin.integrator.datasync.vo.UpdateDatasyncTask;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/16 17:23
 */
@Repository
public class DatasyncInstanceRepository {

    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    /**
     * 使用分页的方式获取需要删除的列表
     * @param page 分页条件
     * @return 需要删除的列表
     */
    public Page<DatasyncInstance> queryNeedDeletePage(Page<Serializable> page) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("hasDelete", "1");
        Map<String, Object> map = DBUtils.queryList(DatasyncInstance.ENTITY_NAME, conditions);
        map.remove("templateContext");
        String sql = DBUtils.getSql(map) + " ORDER BY update_time ASC ";
        Object[] args = DBUtils.getObjects(map);
        List<DatasyncInstance> queryResult = xcfJdbcTemplate.queryForEntities(sql, args, page, DatasyncInstance.class);
        int totalCount = 0;
        if (CollectionUtils.isNotEmpty(queryResult)) {
            Map<String, Object> countMap = DBUtils.queryCount(DatasyncInstance.ENTITY_NAME, conditions);
            totalCount = xcfJdbcTemplate.queryForObject(DBUtils.getSql(countMap), Integer.class, DBUtils.getObjects(countMap));
        } else {
            queryResult = new ArrayList<>();
        }
        Page<DatasyncInstance> pageResult = new Page<>();
        pageResult.setData(queryResult);
        pageResult.setTotal(totalCount);
        pageResult.setPageNo(page.getPageNo());
        pageResult.setPageSize(page.getPageSize());
        return pageResult;
    }

    /**
     * 根据数据同步定义表ID获取同步任务表数据
     * @param parentId 同步定义表ID
     * @return 同步任务表数据
     */
    public List<DatasyncInstance> listByParentId(String parentId) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("datasyncId", parentId);
        conditions.put("hasDelete", YNEnum.NO.getIntCharCode()); // 获取时，仅查询出没有被假删除的
        Map<String, Object> buildResult = DBUtils.queryList(DatasyncInstance.ENTITY_NAME, conditions);
        buildResult.remove("templateContext"); // 获取列表时， 排除掉模板内容
        String sql = DBUtils.getSql(buildResult) + " ORDER BY update_time DESC ";
        Object[] args = DBUtils.getObjects(buildResult);
        List<DatasyncInstance> queryResult = xcfJdbcTemplate.queryForEntities(sql, args, DatasyncInstance.class);
        if (CollectionUtils.isEmpty(queryResult)) {
            return new ArrayList<>();
        }
        return queryResult;
    }

    /**
     * 获取全部任务
     * @return
     */
    public List<DatasyncInstance> list() {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("hasDelete", YNEnum.NO.getIntCharCode()); // 获取时，仅查询出没有被假删除的
        Map<String, Object> buildResult = DBUtils.queryList(DatasyncInstance.ENTITY_NAME, conditions);
        buildResult.remove("templateContext"); // 获取列表时， 排除掉模板内容
        String sql = DBUtils.getSql(buildResult) + " ORDER BY update_time DESC ";
        Object[] args = DBUtils.getObjects(buildResult);
        List<DatasyncInstance> queryResult = xcfJdbcTemplate.queryForEntities(sql, args, DatasyncInstance.class);
        if (CollectionUtils.isEmpty(queryResult)) {
            return new ArrayList<>();
        }
        return queryResult;
    }

    /**
     * 判断该实例编码是否存在
     * @param parentId 同步定义表ID
     * @param code 实例编码
     * @return true or false
     */
    public boolean existInstanceCode(String parentId, String code) {
        String sql = "SELECT count(1) from " + DatasyncInstance.ENTITY_NAME + " WHERE datasync_id = ? and instance_code = ?";
        Integer count = xcfJdbcTemplate.queryForObject(sql, Integer.class, parentId, code);
        return count > 0;
    }

    /**
     * 构建实例编码
     * @param parentId 同步定义表ID
     * @return 实例编码
     */
    public String buildInstanceCode(String parentId) {
        String code = CodeGenerator.getRandomStr(6);
        int max = 0;
        while (max < 3) {
            if (!existInstanceCode(parentId, code)) {
                return code;
            }
            max ++;
        }
        throw new BusinessException("生产实例编码失败");
    }

    /**
     * 查询模板信息
     * @param parentId 同步定义表ID
     * @param templateCode 模板编码
     * @return 模板信息
     */
    public DatasyncInstance findExistTemplate(String parentId, String templateCode) {
        HashMap<String, Object> conditions = new HashMap<>();
        conditions.put("datasyncId", String.valueOf(parentId));
        conditions.put("templateCode", templateCode);
        conditions.put("hasDelete", YNEnum.NO.getIntCharCode());
        Map<String, Object> map = DBUtils.queryList(DatasyncInstance.ENTITY_NAME, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntity(sql, args, DatasyncInstance.class);
    }

    /**
     * 保存记录
     * @param datasyncTask 需要保存的记录
     * @return true or false
     */
    public boolean save(DatasyncInstance datasyncTask) {
        // datasyncTask.setId(SnowflakeGenerator.getNextId() + "");
        datasyncTask.setPublishTime(new Date());
        datasyncTask.setUpdateTime(new Date());
        datasyncTask.setPublishUser(UserContextHolder.getUserContext().getUsername());
        Map<String, Object> map = DBUtils.insertSqlAndObjects(datasyncTask, DatasyncInstance.class, DatasyncInstance.ENTITY_NAME);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.update(sql, args) > 0;
    }

    /**
     * 删除记录
     * @param id 同步定义表ID
     */
    public void removeByParentId(String id) {
        String sql = "DELETE FROM " + DatasyncInstance.ENTITY_NAME + " WHERE datasync_id = ?";
        xcfJdbcTemplate.update(sql, id);
    }

    /**
     * 根据ID获取记录
     * @param id 表记录ID
     * @return 同步定义表记录
     */
    public DatasyncInstance get(String id) {
        HashMap<String, Object> conditions = new HashMap<>();
        conditions.put("id", id);
        Map<String, Object> map = DBUtils.queryList(DatasyncInstance.ENTITY_NAME, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntity(sql, args, DatasyncInstance.class);
    }


    public boolean changeHasDelete(String id, String hasDelete) {
        String sql = "UPDATE " + DatasyncInstance.ENTITY_NAME + " SET has_delete = ?, update_time = ? WHERE id = ?";
        return xcfJdbcTemplate.update(sql, hasDelete, new Date(), id) > 0;
    }


    public Boolean remove(String id) {
        String sql = "DELETE FROM " + DatasyncInstance.ENTITY_NAME + " WHERE id = ?";
        return xcfJdbcTemplate.update(sql, id) > 0;
    }


    /**
     * 根据调度任务ID列表查询数据同步任务
     * @param jobIds 调度任务ID列表
     * @return 数据同步任务列表
     */
    public List<DatasyncInstance> listByIds(List<String> jobIds) {
        if (CollectionUtils.isEmpty(jobIds)) {
            return new ArrayList<>();
        }

        // 最终结果集
        List<DatasyncInstance> resultList = new ArrayList<>();

        // 每批处理的大小，避免IN子句过大
        final int batchSize = 500;

        // 分批处理
        for (int i = 0; i < jobIds.size(); i += batchSize) {
            // 获取当前批次的ID集合
            List<String> batchIds = jobIds.subList(i, Math.min(i + batchSize, jobIds.size()));

            // 构建SQL语句
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT * FROM ").append(DatasyncInstance.ENTITY_NAME);
            sqlBuilder.append(" WHERE dispatch_id IN (");

            // 为每个ID添加占位符
            String placeholders = String.join(",", Collections.nCopies(batchIds.size(), "?"));
            sqlBuilder.append(placeholders);
            sqlBuilder.append(") AND has_delete = ?");

            // 准备参数，先添加所有的ID
            Object[] args = new Object[batchIds.size() + 1];
            for (int j = 0; j < batchIds.size(); j++) {
                args[j] = batchIds.get(j);
            }
            // 最后添加has_delete参数
            args[batchIds.size()] = YNEnum.NO.getIntCharCode();

            // 执行查询并添加到结果集
            List<DatasyncInstance> batchResult = xcfJdbcTemplate.queryForEntities(sqlBuilder.toString(), args, DatasyncInstance.class);
            if (CollectionUtils.isNotEmpty(batchResult)) {
                resultList.addAll(batchResult);
            }
        }

        return resultList;
    }

    /**
     * 根据dispatchID获取记录
     * @param dispatchId XXL-JOB表记录ID
     * @return 同步定义表记录
     */
    public DatasyncInstance getByDispatchId(String dispatchId) {
        HashMap<String, Object> conditions = new HashMap<>();
        conditions.put("dispatch_id", dispatchId);
        Map<String, Object> map = DBUtils.queryList(DatasyncInstance.ENTITY_NAME, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntity(sql, args, DatasyncInstance.class);
    }


    /**
     * 更新任务的触发状态和更新时间
     * @param id 任务ID
     * @param triggerStatus 触发状态
     * @param updateTime 更新时间
     * @return 是否成功
     */
    public boolean updateTriggerStatusAndTime(String id, int triggerStatus, Date updateTime) {
        String sql = "UPDATE " + DatasyncInstance.ENTITY_NAME + " SET trigger_status = ?, update_time = ? WHERE id = ?";
        return xcfJdbcTemplate.update(sql, triggerStatus, updateTime, id) > 0;
    }

    /**
     * 根据 UpdateDatasyncTask 更新 DatasyncInstance
     * 这个方法是一个示例，您可能需要根据 UpdateDatasyncTask 中的实际字段来更新 DatasyncInstance
     * 这里仅更新 instanceName 和 updateTime 作为示例
     * @param datasyncTask 更新任务的数据传输对象
     * @return 是否成功
     */
    public boolean update(UpdateDatasyncTask datasyncTask) {
        if (datasyncTask == null || datasyncTask.getId() == null) {
            return false; // 或者抛出异常
        }
        // 这里仅作为示例，实际需要更新的字段取决于 UpdateDatasyncTask 和 DatasyncInstance 的设计
        // 您可能需要从 UpdateDatasyncTask 获取更多字段来更新 DatasyncInstance
        String sql = "UPDATE " + DatasyncInstance.ENTITY_NAME +
                " SET instance_name = ?," +
                " update_time = ?," +
                " schedule_conf = ?," +
                " executor_route_strategy = ?," +
                " executor_block_strategy = ?," +
                " executor_timeout = ?," +
                " executor_fail_retry_count = ?" +
                " WHERE id = ?";

        // 假设 UpdateDatasyncTask 中有 instanceName 字段
        return xcfJdbcTemplate.update(sql, datasyncTask.getInstanceName(), new Date(), datasyncTask.getSchedulerExpr(), datasyncTask.getRouteStrategy(), datasyncTask.getBlockStrategy(), datasyncTask.getExecutorTimeout(), datasyncTask.getExecutorFailRetryCount(), datasyncTask.getId()) > 0;
    }
}
