package com.xmcares.platform.admin.developer.common.config;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DeveloperConfiguration implements InitializingBean {

    private static DeveloperConfiguration hookConfig = null;
    public static DeveloperConfiguration getInstance() {
        return hookConfig;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        hookConfig = this;
    }

    @Autowired
    private DeveloperProperties properties;

    public DeveloperProperties getProperties() {
        return properties;
    }
}
