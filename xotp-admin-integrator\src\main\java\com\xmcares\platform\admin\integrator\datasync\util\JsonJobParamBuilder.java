package com.xmcares.platform.admin.integrator.datasync.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

/**
 * 简化版JSON参数构建器
 * 构建简单的JSON格式：{"jobId":"123", "syncType":"db2db", "filePath":"xxx"}
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class JsonJobParamBuilder {

    private String jobId;
    private String syncType;
    private String filePath;

    /**
     * 设置任务ID（对应原有的DATASYNC_INSTANCE_ID）
     */
    public JsonJobParamBuilder setJobId(String jobId) {
        this.jobId = jobId;
        return this;
    }

    /**
     * 设置同步类型
     */
    public JsonJobParamBuilder setSyncType(String syncType) {
        this.syncType = syncType;
        return this;
    }

    /**
     * 设置文件路径
     */
    public JsonJobParamBuilder setFilePath(String filePath) {
        this.filePath = filePath;
        return this;
    }

    /**
     * 构建最终的JSON字符串
     */
    public String build() {
        JSONObject json = new JSONObject();

        if (StringUtils.isNotEmpty(jobId)) {
            json.put("jobId", jobId);
        }
        if (StringUtils.isNotEmpty(syncType)) {
            json.put("syncType", syncType);
        }
        if (StringUtils.isNotEmpty(filePath)) {
            json.put("filePath", filePath);
        }

        return json.toJSONString();
    }

}
