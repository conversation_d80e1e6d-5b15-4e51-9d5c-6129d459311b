package com.xmcares.platform.admin.metadata.common.model;

import com.xmcares.platform.admin.common.validation.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/9/14 10:18
 */
@ApiModel(value = "Controller-Result-Model", description = "Controller 返回实体")
public class Result<T> {
    @NotNull(message = "状态不为空", groups = Update.class)
    @ApiModelProperty(value = "success")
    private boolean success = true;

    @ApiModelProperty(value = "message")
    private String message = "none";

    @ApiModelProperty(value = "data")
    private T data;

    public Result() {
    }

    public Result(@NotNull(message = "状态不为空", groups = Update.class) boolean success, String message, T data) {
        this.success = success;
        this.message = message;
        this.data = data;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
