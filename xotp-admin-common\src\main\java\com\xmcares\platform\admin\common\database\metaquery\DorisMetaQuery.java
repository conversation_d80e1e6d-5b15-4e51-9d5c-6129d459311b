package com.xmcares.platform.admin.common.database.metaquery;

import javax.sql.DataSource;

/**
 * Doris v1.2+ 元数据查询, jdbc url加上 useInformationSchema=true
 * Doris 1.2以上版本 引入了标准的 information_schema，支持类似 MySQL 的元数据查询
 * <AUTHOR>
 * @since 1.0.0
 */
public class DorisMetaQuery extends MySqlMetaQuery {

    public DorisMetaQuery(DataSource dataSource) {
        super(dataSource);
    }

    public DorisMetaQuery(DataSource dataSource, String schema) {
        super(dataSource, schema);
    }
}
