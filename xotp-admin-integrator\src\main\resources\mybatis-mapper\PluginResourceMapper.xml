<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmcares.platform.admin.integrator.plugin.mapper.PluginResourceMapper">
  <resultMap id="BaseResultMap" type="com.xmcares.platform.admin.integrator.plugin.model.PluginResource">
    <!--@mbg.generated-->
    <!--@Table xotp.bdp_meta_plugin_resource-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="properties" jdbcType="LONGVARCHAR" property="properties" />
    <result column="bytes" jdbcType="INTEGER" property="bytes" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, create_user, create_time, update_user, update_time, `path`, remark, properties,
    bytes
  </sql>



</mapper>
