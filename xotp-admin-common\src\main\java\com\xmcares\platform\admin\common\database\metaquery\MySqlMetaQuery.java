package com.xmcares.platform.admin.common.database.metaquery;

import com.xmcares.platform.admin.common.database.DataSourceBasedMetaQuery;
import com.xmcares.platform.admin.common.database.metainfo.ColumnInfo;
import com.xmcares.platform.admin.common.database.metainfo.TableInfo;
import com.xmcares.platform.admin.common.errors.MetadataException;
import com.xmcares.platform.admin.common.jdbc.JdbcUtils;
import org.apache.commons.dbutils.handlers.BeanHandler;
import org.apache.commons.dbutils.handlers.BeanListHandler;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.List;

/**
 * MySQL元数据查询
 * <AUTHOR>
 * @since 1.0.0
 */
public class MySqlMetaQuery extends DataSourceBasedMetaQuery {

    /**
     * 获取表信息,“as name”、 “as comment” 便于TableInfo对象映射
     */
    private static final String SQL_TABLES = "SELECT table_name AS name, table_comment AS comment " +
            "FROM information_schema.tables WHERE table_schema = ?";
    private static final String SQL_TABLE_COLUMNS = "SELECT column_name AS name , data_type AS type, column_comment AS comment " +
            "FROM information_schema.columns WHERE table_schema = ? AND table_name = ?";

    public MySqlMetaQuery(DataSource dataSource) {
        super(dataSource);
    }

    public MySqlMetaQuery(DataSource dataSource, String schema) {
        super(dataSource, schema);
    }

    @Override
    public TableInfo getTableInfo(String tableName) {
        String sql = SQL_TABLES + " AND table_name = ?";
        try {
            return JdbcUtils.executeQuery(this.dataSource, sql, new BeanHandler<>(TableInfo.class), this.schema, tableName);
        } catch (SQLException e) {
            throw new MetadataException(String.format("获取数据库[%s]表[%s]TableInfo失败", this.schema, tableName), e);
        }
    }

    @Override
    public List<TableInfo> getTableInfos() {
        try {
            return JdbcUtils.executeQuery(this.dataSource, SQL_TABLES, new BeanListHandler<>(TableInfo.class), this.schema);
        } catch (SQLException e) {
            throw new MetadataException(String.format("获取数据库[%s]所有表TableInfo失败", this.schema), e);
        }
    }

    @Override
    public List<ColumnInfo> getColumnInfos(String tableName) {
        try {
            return JdbcUtils.executeQuery(this.dataSource, SQL_TABLE_COLUMNS, new BeanListHandler<>(ColumnInfo.class), this.schema, tableName);
        } catch (SQLException e) {
            throw new MetadataException(String.format("获取数据库[%s]表[%s]列ColumnInfo失败", this.schema, tableName), e);
        }
    }
}
