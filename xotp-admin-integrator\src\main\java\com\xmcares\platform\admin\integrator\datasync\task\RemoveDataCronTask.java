package com.xmcares.platform.admin.integrator.datasync.task;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import org.slf4j.Logger;

import java.util.Iterator;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/18 10:34
 */
public class RemoveDataCronTask<T extends IRemoveData> {

    private final Logger logger;
    private final String taskName;
    private final IRemoveDataService<T> service;
    private final int runSleep;
    private final int maxTimer;

    public RemoveDataCronTask(Logger logger, String taskName, IRemoveDataService<T> service, int runSleep, int maxTimer) {
        this.logger = logger;
        this.taskName = taskName;
        this.service = service;
        this.runSleep = runSleep;
        this.maxTimer = maxTimer;
    }

    public void execute() {
        logger.info("开始执行定时任务：{}", taskName);
        long begin = System.currentTimeMillis();
        Pagination pagination = new Pagination();
        pagination.setPageNo(1);
        pagination.setPageSize(10);
        Page<T> findResult = service.findNeedDeletePage(pagination);
        if (findResult.getTotal() <= 0 ) {
            logger.info("任务[{}]运行结束， 未找到需要删除的数据！", taskName);
            return;
        }
        int sum = findResult.getTotal();
        int deleteSum = 0;
        Iterator<T> handler = findResult.getData().iterator();
        logger.info("本次任务[{}]需要自动删除的数量为：{}", taskName, sum);
        while (true) {
            if (handler.hasNext()) {
                if (handlerRemoveDatasync(handler.next())) {
                    deleteSum ++;
                }
                handler.remove();
                try {
                    Thread.sleep(runSleep);
                } catch (InterruptedException e) {
                    //do nothing
                }
            }
            findResult = service.findNeedDeletePage(pagination);
            if (findResult.getTotal() > 0) {
                if (System.currentTimeMillis() - begin > maxTimer) {
                    logger.info("任务【{}】执行已经超出时间限制，还剩下{}条需要删除的记录将等待下一次删除, 本次任务执行总耗时：{}", taskName, sum, (System.currentTimeMillis() - begin));
                    return;
                }
                sum = findResult.getTotal();
                logger.info("本次任务【{}】还需要自动删除的数据同步定义信息数量为：{}", taskName, sum);
                handler = findResult.getData().iterator();
            } else {
                logger.info("任务【{}】执行已完成，总共删除了 {} 条同步信息， 任务执行总耗时：{}", taskName, deleteSum, (System.currentTimeMillis() - begin));
                return;
            }
        }
    }

    private boolean handlerRemoveDatasync(T datasync) {
        try {
            return service.removeData(datasync.findId());
        } catch (Exception e) {
            logger.error("删除【" + taskName + "】【" + datasync.findId() + "::" + datasync.findName() + "】失败",  e);
            return false;
        }
    }

}
