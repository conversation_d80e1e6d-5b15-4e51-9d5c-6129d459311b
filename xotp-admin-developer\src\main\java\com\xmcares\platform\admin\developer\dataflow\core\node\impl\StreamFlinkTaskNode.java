package com.xmcares.platform.admin.developer.dataflow.core.node.impl;

import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.developer.common.enums.DeveloperDispatchType;
import com.xmcares.platform.admin.developer.dataflow.core.node.BaseTaskNode;
import com.xmcares.platform.admin.developer.dataflow.vo.DevDataflowNodeVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/30 10:51
 **/
public class StreamFlinkTaskNode extends BaseTaskNode {
    private static final String KEY_MAIN_CLASS = "mainClass";
    private static final String KEY_PARALLELISM = "parallelism";
    private static final String KEY_RESOURCE_ID = "resourceId";

    @Override
    public void validate() throws BusinessException {
        if (CollectionUtils.isNotEmpty(nodeInfo().getPreIds()) || CollectionUtils.isNotEmpty(nodeInfo().getPostIds())) {
            throw new BusinessException("FlinkStream类型的节点【" + nodeInfo().getLabel() + "】不允许拉线！");
        }
        Map<String, Object> handler = Optional.of(nodeInfo().getParams()).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(DevDataflowNodeVo.DevDataflowNodeParam::getKey, DevDataflowNodeVo.DevDataflowNodeParam::getValue));
        if (!handler.containsKey(KEY_SCHEDULER_TYPE) || ObjectUtils.isEmpty(handler.get(KEY_SCHEDULER_TYPE)) || StringUtils.isEmpty(String.valueOf(handler.get(KEY_SCHEDULER_TYPE)))) {
            throw new BusinessException(String.format("节点【%s】未设置调度类型", nodeInfo().getLabel()));
        }
        if (!DeveloperDispatchType.STREAM.getXxlJobDispatchType().equalsIgnoreCase(String.valueOf(handler.get(KEY_SCHEDULER_TYPE)))) {
            throw new BusinessException(String.format("节点【%s】调度类型请设置成STREAM", nodeInfo().getLabel()));
        }
        if (!handler.containsKey(KEY_MAIN_CLASS) || ObjectUtils.isEmpty(handler.get(KEY_MAIN_CLASS)) || StringUtils.isEmpty(String.valueOf(handler.get(KEY_MAIN_CLASS)))) {
            throw new BusinessException(String.format("节点【%s】未设置Flink应用程序的启动类", nodeInfo().getLabel()));
        }
        if (!handler.containsKey(KEY_PARALLELISM) || ObjectUtils.isEmpty(handler.get(KEY_PARALLELISM)) || !NumberUtils.isCreatable(String.valueOf(handler.get(KEY_PARALLELISM)))) {
            throw new BusinessException(String.format("节点【%s】未设置Flink应用程序的并行度", nodeInfo().getLabel()));
        }
        if (!handler.containsKey(KEY_RESOURCE_ID) || ObjectUtils.isEmpty(handler.get(KEY_RESOURCE_ID)) || StringUtils.isEmpty(String.valueOf(handler.get(KEY_RESOURCE_ID)))) {
            throw new BusinessException(String.format("节点【{}】未上传对应的资源", nodeInfo().getLabel()));
        }
    }

}
