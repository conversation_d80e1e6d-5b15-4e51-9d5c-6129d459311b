package com.xmcares.platform.admin.asset.model.vo;

import com.xmcares.platform.admin.asset.model.MetaDatatableColumn;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/4 15:50
 */
public class DatatableColumnSyncQueryVO {

    String datasourceId;
    String datatableId;

    List<UnSyncDatatableColumn> unSyncList;

    List<MetaDatatableColumn> syncList;

    public static class UnSyncDatatableColumn {
        private String name;
        private String remark;

        private String type;

        private String length;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getLength() {
            return length;
        }

        public void setLength(String length) {
            this.length = length;
        }
    }

    public List<UnSyncDatatableColumn> getUnSyncList() {
        return unSyncList;
    }

    public void setUnSyncList(List<UnSyncDatatableColumn> unSyncList) {
        this.unSyncList = unSyncList;
    }

    public List<MetaDatatableColumn> getSyncList() {
        return syncList;
    }

    public void setSyncList(List<MetaDatatableColumn> syncList) {
        this.syncList = syncList;
    }

    public String getDatasourceId() {
        return datasourceId;
    }

    public void setDatasourceId(String datasourceId) {
        this.datasourceId = datasourceId;
    }

    public String getDatatableId() {
        return datatableId;
    }

    public void setDatatableId(String datatableId) {
        this.datatableId = datatableId;
    }
}
