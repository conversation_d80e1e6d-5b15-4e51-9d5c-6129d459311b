package com.xmcares.platform.admin.metadata.database.service;

import com.xmcares.platform.admin.common.datasource.DataSourceGroup;
import com.xmcares.platform.admin.common.datasource.DataSourceType;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.util.HashUtils;
import com.xmcares.platform.admin.metadata.database.model.DatasourceModel;
import com.xmcares.platform.admin.metadata.database.repository.DatasourceModelRepository;
import com.xmcares.platform.admin.metadata.database.repository.DatasourceRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/2/18 16:37
 */
@Component
@PropertySource(value = "classpath:application.properties", encoding = "utf-8")
public class DatasourceModelService {

    @Autowired
    DatasourceModelRepository dsModelRepository;

    @Autowired
    DatasourceRepository datasourceRepository;

    @Autowired
    DatasourceModelRepository datasourceModelRepository;


    public List<DatasourceModel> listDataModel(DatasourceModel query) {
        return dsModelRepository.queryList(query);
    }

    public Map<String, List<DatasourceModel>> categoryList(DatasourceModel query) {
        List<DatasourceModel> datasourceModelList = dsModelRepository.queryList(query);
        if(datasourceModelList!=null && !datasourceModelList.isEmpty()) {
            Map<String, List<DatasourceModel>> cartManagerGroup = datasourceModelList.stream().collect(Collectors.groupingBy(DatasourceModel::getCategory));
            return cartManagerGroup;
        }
        return null;
    }


    public Boolean add(DatasourceModel datasourceModel) {
        //1. 保证类型唯一
        String category = datasourceModel.getCategory();
        DataSourceGroup group = DataSourceGroup.groupOf(category);
        if (group == null) {
            throw new BusinessException(String.format("不支持的数据源分组[%s]", category));
        }
        DataSourceType type = group.itsTypeOf(datasourceModel.getType());
        if (type == null) {
            throw new BusinessException(String.format("不支持的[%s]数据源类型[%s]", category, datasourceModel.getType()));
        }
        datasourceModel.setCategory(type.getGroup().name());
        if (checkTypeExist(type.getTypeName())) {
            throw new BusinessException("该类型已经存在！");
        }
        //2. 新增
        return datasourceModelRepository.addDataSourceModel(datasourceModel);
    }

    public Boolean update(DatasourceModel datasourceModel) {
        //1. 获取旧的记录
        DatasourceModel oldDatasourceModel = datasourceModelRepository.get(datasourceModel.getId());
        if (oldDatasourceModel == null) {
            throw new BusinessException("需要更新的数据源模型不存在");
        }
        //2. 类型不允许修改
        datasourceModel.setType(oldDatasourceModel.getType());
        datasourceModel.setCategory(oldDatasourceModel.getCategory());
        //3. 验证是否已经被使用
        if (byUse(oldDatasourceModel.getId())) {
            if (StringUtils.isNotEmpty(datasourceModel.getTemplate())) {
                int newHash = HashUtils.murmur32(datasourceModel.getTemplate().getBytes());
                int oldHash = HashUtils.murmur32(oldDatasourceModel.getTemplate().getBytes());
                if (newHash != oldHash) {
                    throw new BusinessException("已经被使用的数据源模型不允许修改");
                }
            } else {
                datasourceModel.setTemplate(oldDatasourceModel.getTemplate());
            }
        }
        //4. 更新
        return datasourceModelRepository.updateDatasourceModel(datasourceModel);
    }



    public int delete(@NotBlank(message = "id不能为空") String id) {
        if (byUse(id)) {
            throw new BusinessException("已经被使用的数据源模型不允许删除");
        }
        return datasourceModelRepository.removeDatasourceModel(id);
    }

    public List<String> findSupportTypes() {
        return datasourceModelRepository.findSupportTypes();
    }

    public boolean checkTypeExist(String type) {
        return datasourceModelRepository.getByType(type) != null;
    }

    public boolean byUse(String id) {

        // 1. 数据源表中验证
//        Datasource query = new Datasource();
//        query.setModelId(id);
//        if (datasourceRepository.count(query) > 0) {
//            return true;
//        }

        // 2. 集成模型表中验证
//        DatasyncModel datasyncModelQuery = new DatasyncModel();
//        datasyncModelQuery.setDatasourceModelId(id);
//        if (datasyncModelRepository.count(datasyncModelQuery) > 0) {
//            return true;
//        }

        return false;
    }

    /**
     * 获取只有ID 和 Type 的数据源模型
     * @return
     */
    public List<DatasourceModel> listBySimple() {
        return datasourceModelRepository.querySimpleList();
    }
}
