package com.xmcares.platform.admin.integrator.datasync.task;

import com.xmcares.framework.fsclient.FSTemplate;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.integrator.IntegratorProperties;
import com.xmcares.platform.admin.integrator.common.util.ConstantUtils;
import com.xmcares.platform.admin.integrator.datasync.repository.DatasyncWriteModelMapperRepository;
import com.xmcares.platform.admin.metadata.database.model.DevDataflowResourceWriteMapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/20 10:08
 **/
@Component
public class DatasyncWriteMapperCronTask {

    private static final String TASK_CHANE_NAME = "Datax写插件资源同步任务";

    private DatasyncWriteModelMapperRepository writeModelMapperRepository;
    private FSTemplate fsTemplate;
    private IntegratorProperties properties;
    /** 记录文件在文件服务器上的位置 */
    private final String recordPath;

    public DatasyncWriteMapperCronTask(DatasyncWriteModelMapperRepository repository, FSTemplate template, IntegratorProperties properties) {
        this.writeModelMapperRepository = repository;
        this.fsTemplate = template;
        this.properties = properties;
        recordPath = buildRecordPath();
    }

    @XxlJob(value = "DatasyncWriteMapperHandler")
    public void execute() {
        XxlJobHelper.log("开始执行任务-{}-", TASK_CHANE_NAME);
        try {
            // 1. 判断是否需要同步， 表中存在字段 sync 0：未同步 1：已同步
            List<DevDataflowResourceWriteMapper> findResult = writeModelMapperRepository.selectAllByUnSync();
            if (CollectionUtils.isNotEmpty(findResult)) {
                int count = findResult.size();
                XxlJobHelper.log("正在执行任务-{}-， 本次需要同步的数量-{}-", TASK_CHANE_NAME, count);
                for (DevDataflowResourceWriteMapper mapper : findResult) {
                    if (YNEnum.such(mapper.getDeleted()).equals(YNEnum.YES)) {
                        if (doDelete(mapper)) {
                            count --;
                        }
                    } else {
                        count --;
                    }
                }
                if (count != findResult.size()) {
                    doUpload();
                    if (count <= 0) {
                        XxlJobHelper.log("执行任务-{}-已完美完成", TASK_CHANE_NAME);
                    } else {
                        XxlJobHelper.log("执行任务-{}-未完美完成， 离计划的处理数量-{}-，还有-{}-未成功同步！等待下次处理。",
                                TASK_CHANE_NAME, findResult.size(), count);
                    }
                }
            }
        } catch (Exception e) {
            XxlJobHelper.log("任务-{}-执行异常", TASK_CHANE_NAME);
            XxlJobHelper.log(e);
            XxlJobHelper.handleFail("任务执行失败");
            return;
        } finally {
            XxlJobHelper.log("任务-{}-执行结束", TASK_CHANE_NAME);
        }
        XxlJobHelper.handleSuccess();
    }

    private String buildRecordPath() {
        return properties.getFileServerRoot() + ConstantUtils.FILE_DATAX_PLUGIN_DIR + "/writer" + "writer_record.properties";
    }

    private void doUpload() throws Exception{
        List<DevDataflowResourceWriteMapper> findResult = writeModelMapperRepository.selectAll();
        File dir = new File(properties.getLocalTmpRoot());
        if (!dir.exists()) {
            dir.mkdir();
        }
        File tmp = File.createTempFile("DatasyncWrite", ".tmp", dir.getCanonicalFile());
        if (CollectionUtils.isNotEmpty(findResult)) {
            List<String> lines = findResult.stream().map(DevDataflowResourceWriteMapper::toLine).collect(Collectors.toList());
            FileUtils.writeLines(tmp, "UTF-8", lines);
        }
        try (FileInputStream fileInputStream = new FileInputStream(tmp)){
            fsTemplate.saveFile(new FileDesc.FileDescImpl(null, recordPath), fileInputStream);
        }
        if (CollectionUtils.isNotEmpty(findResult)) {
            writeModelMapperRepository.updateToSyncByIds(findResult.stream().map(DevDataflowResourceWriteMapper::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 1. 删除文件服务器上的文件
     * 2. 删除记录
     * @param mapper
     */
    private boolean doDelete(DevDataflowResourceWriteMapper mapper) {
        try {
            fsTemplate.deleteFile(mapper.getPath());
        } catch (Exception e) {
            XxlJobHelper.log("正在执行任务-{}-，删除写插件-{}-异常", TASK_CHANE_NAME, mapper.getPath());
            XxlJobHelper.log(e);
        }
        try {
            writeModelMapperRepository.delete(mapper.getId());
            return true;
        } catch (Exception e) {
            XxlJobHelper.log("正在执行任务-{}-，删除写插件-{}-记录异常", TASK_CHANE_NAME, mapper.getPath());
            XxlJobHelper.log(e);
            return false;
        }
    }

}
