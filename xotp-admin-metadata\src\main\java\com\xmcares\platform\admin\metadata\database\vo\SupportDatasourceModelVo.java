package com.xmcares.platform.admin.metadata.database.vo;

import java.util.Objects;

/**
 * <AUTHOR> chenYG
 * @date : 2022/4/28 10:54
 */
public class SupportDatasourceModelVo {

    /** 数据源模型ID */
    private final String id;
    /** 数据源模型名称 */
    private final String type;
    /** 支持的模式 0: 仅能读 1: 仅能写 2: 能读又能写 */
    private final String supportMode;

    public SupportDatasourceModelVo(String id, String type, String supportMode) {
        this.id = id;
        this.type = type;
        this.supportMode = supportMode;
    }

    public String getId() {
        return id;
    }

    public String getType() {
        return type;
    }

    public String getSupportMode() {
        return supportMode;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SupportDatasourceModelVo that = (SupportDatasourceModelVo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(type, that.type) &&
                Objects.equals(supportMode, that.supportMode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, type, supportMode);
    }
}
