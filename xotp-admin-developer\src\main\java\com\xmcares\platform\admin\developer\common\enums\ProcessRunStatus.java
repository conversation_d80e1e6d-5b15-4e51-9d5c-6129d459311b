package com.xmcares.platform.admin.developer.common.enums;
/** 运行状态信息 */
public enum ProcessRunStatus {

    /** 运行状态信息 */
    UN("0", "未运行"),
    RUN("1","运行中"),
    STOP("2","已暂停")

    ;

    private String code;
    private String title;

    ProcessRunStatus(String code, String title) {
        this.code = code;
        this.title = title;
    }

    public String getCode() {
        return code;
    }

    public String getTitle() {
        return title;
    }
}
