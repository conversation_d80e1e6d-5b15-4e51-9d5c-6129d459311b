package com.xmcares.platform.admin.lifecycle.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(value = AsstArchiveResults.TABLE, description = "数据资产归档档案（结果）")
public class AsstArchiveResults {
    public static final String TABLE = "bdp_asst_archive_results";


    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "调度ID")
    private String archive_scheduler_id;
    @ApiModelProperty(value = "归档表ID")
    private String archive_datatable_id;
    @ApiModelProperty(value = "库名（冗余）")
    private String dataware_name;
    @ApiModelProperty(value = "数据表名（冗余）")
    private String datatable_name;
    @ApiModelProperty(value = "近线归档数据量")
    private Integer nearline_archive_data_count;
    @ApiModelProperty(value = "离线归档数据量")
    private Integer offline_archive_data_count;
    @ApiModelProperty(value = "近线执行状态")
    private String nearline_status;
    @ApiModelProperty(value = "近线数据大小")
    private Float nearline_data_size;
    @ApiModelProperty(value = "近线数据源名")
    private String nearline_datasource_name;
    @ApiModelProperty(value = "近线数据源表名")
    private String nearline_datatable_name;
    @ApiModelProperty(value = "离线执行状态")
    private String offline_status;
    @ApiModelProperty(value = "离线归档文件大小")
    private Float offline_file_size;
    @ApiModelProperty(value = "离线归档文件位置")
    private String offline_file_path;
    @ApiModelProperty(value = "归档执行时间")
    private Date check_time;
    @ApiModelProperty(value = "create_time")
    private Date create_time;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getArchive_scheduler_id() {
        return archive_scheduler_id;
    }

    public void setArchive_scheduler_id(String archive_scheduler_id) {
        this.archive_scheduler_id = archive_scheduler_id;
    }

    public String getArchive_datatable_id() {
        return archive_datatable_id;
    }

    public void setArchive_datatable_id(String archive_datatable_id) {
        this.archive_datatable_id = archive_datatable_id;
    }

    public String getDataware_name() {
        return dataware_name;
    }

    public void setDataware_name(String dataware_name) {
        this.dataware_name = dataware_name;
    }

    public String getDatatable_name() {
        return datatable_name;
    }

    public void setDatatable_name(String datatable_name) {
        this.datatable_name = datatable_name;
    }

    public Integer getNearline_archive_data_count() {
        return nearline_archive_data_count;
    }

    public void setNearline_archive_data_count(Integer nearline_archive_data_count) {
        this.nearline_archive_data_count = nearline_archive_data_count;
    }

    public Integer getOffline_archive_data_count() {
        return offline_archive_data_count;
    }

    public void setOffline_archive_data_count(Integer offline_archive_data_count) {
        this.offline_archive_data_count = offline_archive_data_count;
    }

    public String getNearline_status() {
        return nearline_status;
    }

    public void setNearline_status(String nearline_status) {
        this.nearline_status = nearline_status;
    }

    public Float getNearline_data_size() {
        return nearline_data_size;
    }

    public void setNearline_data_size(Float nearline_data_size) {
        this.nearline_data_size = nearline_data_size;
    }

    public String getNearline_datasource_name() {
        return nearline_datasource_name;
    }

    public void setNearline_datasource_name(String nearline_datasource_name) {
        this.nearline_datasource_name = nearline_datasource_name;
    }

    public String getNearline_datatable_name() {
        return nearline_datatable_name;
    }

    public void setNearline_datatable_name(String nearline_datatable_name) {
        this.nearline_datatable_name = nearline_datatable_name;
    }

    public String getOffline_status() {
        return offline_status;
    }

    public void setOffline_status(String offline_status) {
        this.offline_status = offline_status;
    }

    public Float getOffline_file_size() {
        return offline_file_size;
    }

    public void setOffline_file_size(Float offline_file_size) {
        this.offline_file_size = offline_file_size;
    }

    public String getOffline_file_path() {
        return offline_file_path;
    }

    public void setOffline_file_path(String offline_file_path) {
        this.offline_file_path = offline_file_path;
    }

    public Date getCheck_time() {
        return check_time;
    }

    public void setCheck_time(Date check_time) {
        this.check_time = check_time;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }
}
