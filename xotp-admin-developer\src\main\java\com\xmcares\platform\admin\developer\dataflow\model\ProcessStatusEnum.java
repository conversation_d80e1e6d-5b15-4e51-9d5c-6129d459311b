/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2021
 * Author： huangyh
 * Date：2022/6/15
 */
package com.xmcares.platform.admin.developer.dataflow.model;

/**
 * 数据开发实例状态枚举
 * <AUTHOR>
 * @since 1.0.0
 */
public enum ProcessStatusEnum {

    NOT_RUNNING("0"), //未运行
    RUNNING("1"), //运行中
    PAUSED("2") //已暂停
    ;

    private String value;

    ProcessStatusEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
