{
  "plugin_name": "Rabbitm<PERSON>",
  <#-- 如果有transform, 则消费transform的输出 -->
  <#if fieldMappings?? && fieldMappings?has_content>
    "plugin_input": "${sink.plugin_input!'default_transform_output'}",
  <#else>
    "plugin_input": "${sink.plugin_input!'default_source_output'}",
  </#if>
  <#-- --- 在此处直接进行内联解析 --- -->
  <#-- 1. 优先从 serverHost 和 serverport 取值 -->
  <#if destDatasource.serverHost?has_content>
    <#assign host = destDatasource.serverHost>
  <#else>
  <#-- 检查URL是否存在，并移除协议前缀 -->
    <#if destDatasource.url?has_content>
      <#if destDatasource.url?contains("://")>
        <#assign clean_url = destDatasource.url?substring(destDatasource.url?index_of("://") + 3)>
      <#else>
        <#assign clean_url = destDatasource.url>
      </#if>

    <#-- 分离 host -->
      <#assign colon_index = clean_url?last_index_of(":")>
      <#if colon_index != -1>
        <#assign host = clean_url?substring(0, colon_index)>
      <#else>
        <#assign host = clean_url>
      </#if>
    <#else>
    <#-- 处理url为空的边界情况 -->
      <#assign host = "">
    </#if>
  </#if>
  <#-- 2. 优先从 serverport 取值 -->
  <#if destDatasource.serverport?has_content>
    <#assign port = destDatasource.serverport?string>
  <#else>
  <#-- 从URL中解析端口 -->
    <#if destDatasource.url?has_content>
      <#if destDatasource.url?contains("://")>
        <#assign clean_url = destDatasource.url?substring(destDatasource.url?index_of("://") + 3)>
      <#else>
        <#assign clean_url = destDatasource.url>
      </#if>

      <#assign colon_index = clean_url?last_index_of(":")>
      <#if colon_index != -1>
        <#assign port = clean_url?substring(colon_index + 1)>
      <#else>
        <#assign port = "5672"> <#-- 默认端口 -->
      </#if>
    <#else>
      <#assign port = "5672"> <#-- 默认端口 -->
    </#if>
  </#if>
  "host": "${host}",
  "port": "${port}",
  <#-- --- 解析结束，下面是其他参数 --- -->
  "virtual_host": "${destDatasource.virtual_host!'/'}",
  "username": "${destDatasource.username}",
  "password": "${destDatasource.password}",
  "queue_name": "${dest.topic}",
  "format": "${dest.format!'json'}",
  "durable": ${(dest.durable)!true?string('true', 'false')},
  "exclusive": ${(dest.exclusive)!false?string('true', 'false')},
  "auto_delete": ${(dest.auto_delete)!false?string('true', 'false')}
}
