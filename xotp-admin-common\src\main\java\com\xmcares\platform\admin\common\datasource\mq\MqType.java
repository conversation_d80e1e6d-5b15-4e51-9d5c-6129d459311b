/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/21
 */
package com.xmcares.platform.admin.common.datasource.mq;

import com.xmcares.framework.commons.util.JacksonJsonUtils;
import com.xmcares.platform.admin.common.datasource.DataSourceGroup;
import com.xmcares.platform.admin.common.datasource.DataSourceOptions;
import com.xmcares.platform.admin.common.datasource.DataSourceType;
import com.xmcares.platform.admin.common.datasource.mq.mqtt.MqttDataSource;
import com.xmcares.platform.admin.common.datasource.mq.mqtt.MqttProperties;
import com.xmcares.platform.admin.common.datasource.mq.pulsar.PulsarDataSource;
import com.xmcares.platform.admin.common.datasource.mq.pulsar.PulsarProperties;
import com.xmcares.platform.admin.common.datasource.mq.rabbitmq.RabbitMqDataSource;
import com.xmcares.platform.admin.common.datasource.mq.rabbitmq.RabbitMqProperties;
import com.xmcares.platform.admin.common.datasource.mq.rocketmq.RocketMqDataSource;
import com.xmcares.platform.admin.common.datasource.mq.rocketmq.RocketMqProperties;
import com.xmcares.platform.admin.common.datasource.mq.ximc.XimcDataSource;
import com.xmcares.platform.admin.common.datasource.mq.ximc.XimcProperties;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public enum MqType implements DataSourceType<MqDataSource> {
    ROCKETMQ(DataSourceGroup.MQ, "rocketmq") {
        @Override
        public RocketMqDataSource createDataSource(DataSourceOptions options) {
            RocketMqProperties props = JacksonJsonUtils.convert(options, RocketMqProperties.class);
            props.setNamesrvAddr(options.getUrl());
            props.setAccessKey(options.getUsername());
            props.setSecretKey(options.getPassword());
            //用于标记同一个客户端实例
            String identity = options.getName();
            return new RocketMqDataSource(identity, props);
        }
    },
    RABBITMQ(DataSourceGroup.MQ, "rabbitmq") {
        @Override
        public MqDataSource createDataSource(DataSourceOptions options) {
            RabbitMqProperties props = JacksonJsonUtils.convert(options, RabbitMqProperties.class);
            String url = options.getUrl();
            String[] splits = StringUtils.split(url, ":");
            if (splits != null && splits.length > 0) {
                props.setHost(splits[0]);
                if (splits.length > 1) {
                    props.setPort(Integer.parseInt(splits[1]));
                }
            }
            props.setUsername(options.getUsername());
            props.setPassword(options.getPassword());
            return new RabbitMqDataSource(options.getName(), props);
        }
    }/*,
    KAFKA(DataSourceGroup.MQ, "kafka") {
        @Override
        public MqDataSource createDataSource(DataSourceOptions options) {
            return null;
        }
    }*/,
    XIMC(DataSourceGroup.MQ, "ximc") {
        @Override
        public MqDataSource createDataSource(DataSourceOptions options) {
            XimcProperties props = JacksonJsonUtils.convert(options, XimcProperties.class);
            return new XimcDataSource(options.getName(), props);
        }
    },
    MQTT(DataSourceGroup.MQ, "mqtt") {
        @Override
        public MqDataSource createDataSource(DataSourceOptions options) {
            MqttProperties props = JacksonJsonUtils.convert(options, MqttProperties.class);
            if (StringUtils.isNotEmpty(options.getUrl())) {
                props.setBrokerUrl(options.getUrl());
            }
            return new MqttDataSource(options.getName(), props);
        }
    },

    PULSAR(DataSourceGroup.MQ, "pulsar") {
        @Override
        public MqDataSource createDataSource(DataSourceOptions options) {
            PulsarProperties props = JacksonJsonUtils.convert(options, PulsarProperties.class);
            if (StringUtils.isNotEmpty(options.getUrl())) {
                props.setServiceUrl(options.getUrl());
            }
            return new PulsarDataSource(options.getName(), props);
        }
    };

    private static final Logger logger = LoggerFactory.getLogger(MqType.class);

    private final DataSourceGroup group ;
    private final String typeName;

    MqType(DataSourceGroup group, String typeName) {
        this.group = group;
        this.typeName = typeName;
    }

    public static MqType fromTypeName(String typeName) {
        if (typeName != null && !typeName.isEmpty()) {
            for (MqType candidate : values()) {
                if (candidate.equalsTypeName(typeName)) {
                    return candidate;
                }
            }
        }
        return null;
    }


    public String getTypeName() {
        return this.typeName;
    }

    public DataSourceGroup getGroup() {
        return this.group;
    }

    public boolean equalsTypeName(String typeName) {
        return typeName != null && typeName.equalsIgnoreCase(this.typeName);
    }


    public abstract MqDataSource createDataSource(DataSourceOptions options);



}
