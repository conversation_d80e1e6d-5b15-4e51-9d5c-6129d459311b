/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2025
 * Author： lius
 * Date：2025/5/27
 */
package com.xmcares.platform.admin.dataservice.message.service;

import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.framework.sharing.reference.resource.AppResource;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.dataservice.dataset.vo.DataModelPageQueryVO;
import com.xmcares.platform.admin.dataservice.message.repository.MessageTopicRepository;
import com.xmcares.platform.admin.dataservice.message.vo.MessageTopicAuthAppVO;
import com.xmcares.platform.admin.dataservice.message.vo.MessageTopicVO;
import com.xmcares.platform.admin.metadata.common.model.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description 消息主题服务
 * <AUTHOR>
 * @Date 2025/5/27
 */
@Service
public class MessageTopicService {

    @Autowired
    private MessageTopicRepository messageTopicRepository;

    /**
     * 创建或更新消息主题
     *
     * @param messageTopicVO 参数
     * @return Result<MessageTopicVO>
     */
    public Boolean save(MessageTopicVO messageTopicVO) {
        MessageTopicVO messageTopic = new MessageTopicVO();
        messageTopic.setId(messageTopicVO.getId());
        messageTopic.setTopicName(messageTopicVO.getTopicName());
        messageTopic.setDatasourceId(messageTopicVO.getDatasourceId());
        messageTopic.setRemark(messageTopicVO.getRemark());
        messageTopic.setCreateUser(UserContextHolder.getUserContext().getUsername());
        messageTopic.setCreateTime(new Date());
        messageTopic.setUpdateUser(UserContextHolder.getUserContext().getUsername());
        messageTopic.setUpdateTime(new Date());

        MessageTopicVO createdTopic = messageTopicRepository.createOrUpdateMessageTopic(messageTopic);
        return createdTopic != null;
    }


    /**
     * 分页查询消息主题
     *
     * @param messageTopicVO 参数
     * @param pagination     页码信息
     * @return Result<Page < MessageTopicVO>>
     */
    public Page<MessageTopicVO> messageTopicPageQuery(MessageTopicVO messageTopicVO, Pagination pagination) {
        List<MessageTopicVO> messageTopics = messageTopicRepository.messageTopicPageQuery(messageTopicVO, new Page<>(pagination.getPageNo() - 1, pagination.getPageSize()));
        int total = messageTopicRepository.countDevDataflow(messageTopicVO);
        Page<MessageTopicVO> page = new Page<>();
        page.setData(messageTopics);
        page.setTotal(total);
        page.setPageNo(pagination.getPageNo());
        page.setPageSize(pagination.getPageSize());
        return page;
    }


    /**
     * 删除消息主题
     *
     * @param topicId 主题ID列表
     * @return boolean
     */
    public boolean deleteMessageTopics(String topicId) {
        int deletedIds = messageTopicRepository.deleteMessageTopics(topicId);
        return deletedIds > 0;
    }

    /**
     * 根据ID获取消息主题
     *
     * @param topicId 主题ID
     * @return Result<MessageTopicVO>
     */
    public Result<MessageTopicVO> getMessageTopicById(String topicId) {
        MessageTopicVO topic = messageTopicRepository.getMessageTopicById(topicId);
        boolean success = topic != null;
        String message = success ? "查询成功" : "主题未找到";
        return new Result<>(success, message, topic);
    }

    /**
     * 检查主题名称唯一性
     *
     * @param topicName 主题名称
     * @return Result<Boolean>
     */
    public Result<Boolean> nameUniqueCheck(String topicName) {
        boolean isUnique = messageTopicRepository.nameUniqueCheck(topicName);
        return new Result<>(true, "", isUnique);
    }


    /**
     * 客户授权
     */
    public boolean appAuthService(List<String> appIdList, List<String> topicIdList) {
        boolean allSuccess = true;

        for (String topicId : topicIdList) {
            // 清空指定 topicId 的所有 appId
            boolean deleteSuccess = messageTopicRepository.deleteAppAuthByTopicId(topicId);
            // 如果删除失败，记录为 allSuccess 为 false
            if (!deleteSuccess) {
                allSuccess = false;
            }
            // 遍历 appIdList 并插入新授权信息
            for (String appId : appIdList) {
                boolean insertSuccess = messageTopicRepository.insertAppAuth(appId, topicId);
                if (!insertSuccess) {
                    allSuccess = false; // 如果某个插入失败，设置为 false
                }
            }
        }
        return allSuccess;
    }

    /**
     * 主题授权
     */
    public boolean appAuthTopic(List<String> appIdList, List<String> topicIdList) {
        boolean allSuccess = true;

        for (String appId : appIdList) {
            // 清空指定 topicId 的所有 appId
            boolean deleteSuccess = messageTopicRepository.deleteTopicAuthByAppId(appId);
            // 如果删除失败，记录为 allSuccess 为 false
            if (!deleteSuccess) {
                allSuccess = false;
            }
            // 遍历 appIdList 并插入新授权信息
            for (String topicId : topicIdList) {
                boolean insertSuccess = messageTopicRepository.insertAppAuth(appId, topicId);
                if (!insertSuccess) {
                    allSuccess = false; // 如果某个插入失败，设置为 false
                }
            }
        }
        return allSuccess;
    }

    /**
     * 主题已授权应用列表
     *
     * @param topicId
     * @return
     */
    public List<MessageTopicAuthAppVO> getAppResourceByRelService(String topicId) {
        return messageTopicRepository.getAppResourceByTopicId(topicId);
    }

    /**
     * 应用已授权主题列表
     *
     * @param appId
     * @return
     */
    public List<MessageTopicAuthAppVO> getTopicResourceByRelService(String appId) {
        return messageTopicRepository.getTopicResourceByAppId(appId);
    }
}
