package com.xmcares.platform.admin.integrator.datasync.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xmcares.platform.admin.integrator.datasync.model.Datasync;
import org.springframework.beans.BeanUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> huangyh
 * @date : 2022/3/29 11:07
 */
public class DisplayAllDataSync implements Serializable {

    /** ID */
    private String id;
    /** 创建时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /** 更新时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /** 创建人 */
    private String createUser;
    /** 同步任务名称 */
    private String intgName;
    /** 数据来源集成方式 0:内置 1:插件 */
    private String orginType;
    /** 数据来源集成模型ID */
    private String orginIntgModelId;
    /** 数据来源数据源名称 */
    private String orginDatasourceName;
    /** 数据来源数据源ID */
    private String orginDatasourceId;
    /** 数据来源插件路径 */
    private String orginPluginPath;
    /** 数据来源基础信息实际Json数据 */
    private String orginBaseJson;
    /** 数据来源进阶信息实际Json数据 */
    private String orginAdvJson;
    /** 数据来源高级信息实际Json数据 */
    private String orginHighJson;
    /** 数据来源列信息实际Json数据 */
    private String orginColumnJson;
    /** 数据来源运行模式 */
    private String orginRunSchema;
    /** 数据去向集成方式 0:内置 1：插件 */
    private String destType;
    /** 数据去向集成模型ID */
    private String destIntgModelId;
    /** 数据去向数据源名称 */
    private String destDatasourceName;
    /** 数据去向数据源ID */
    private String destDatasourceId;
    /** 数据去向插件路径 */
    private String destPluginPath;
    /** 数据去向基础信息实际Json数据 */
    private String destBaseJson;
    /** 数据去向进阶信息实际Json数据 */
    private String destAdvJson;
    /** 数据去向高级信息实际Json数据 */
    private String destHighJson;
    /** 数据去向列信息实际Json数据 */
    private String destColumnJson;

    /** 调度参数 */
    private String schedulerExpr;
    /** 路由策略 */
    private String routeStrategy;
    /** 阻塞策略 */
    private String blockStrategy;
    /** 执行超时时间 */
    private int executorTimeout;
    /** 执行失败重试次数 */
    private int executorFailRetryCount;


    public static DisplayAllDataSync createBaseFrom(Datasync baseInfo) {
        DisplayAllDataSync result = new DisplayAllDataSync();
        BeanUtils.copyProperties(baseInfo, result);
        return result;
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getIntgName() {
        return intgName;
    }

    public void setIntgName(String intgName) {
        this.intgName = intgName;
    }

    public String getOrginRunSchema() {
        return orginRunSchema;
    }

    public void setOrginRunSchema(String orginRunSchema) {
        this.orginRunSchema = orginRunSchema;
    }

    public String getOrginType() {
        return orginType;
    }

    public void setOrginType(String orginType) {
        this.orginType = orginType;
    }

    public String getOrginIntgModelId() {
        return orginIntgModelId;
    }

    public void setOrginIntgModelId(String orginIntgModelId) {
        this.orginIntgModelId = orginIntgModelId;
    }

    public String getOrginDatasourceName() {
        return orginDatasourceName;
    }

    public void setOrginDatasourceName(String orginDatasourceName) {
        this.orginDatasourceName = orginDatasourceName;
    }

    public String getOrginDatasourceId() {
        return orginDatasourceId;
    }

    public void setOrginDatasourceId(String orginDatasourceId) {
        this.orginDatasourceId = orginDatasourceId;
    }

    public String getOrginPluginPath() {
        return orginPluginPath;
    }

    public void setOrginPluginPath(String orginPluginPath) {
        this.orginPluginPath = orginPluginPath;
    }

    public String getOrginBaseJson() {
        return orginBaseJson;
    }

    public void setOrginBaseJson(String orginBaseJson) {
        this.orginBaseJson = orginBaseJson;
    }

    public String getOrginAdvJson() {
        return orginAdvJson;
    }

    public void setOrginAdvJson(String orginAdvJson) {
        this.orginAdvJson = orginAdvJson;
    }


    public String getOrginHighJson() {
        return orginHighJson;
    }

    public void setOrginHighJson(String orginHighJson) {
        this.orginHighJson = orginHighJson;
    }

    public String getOrginColumnJson() {
        return orginColumnJson;
    }

    public void setOrginColumnJson(String orginColumnJson) {
        this.orginColumnJson = orginColumnJson;
    }

    public String getDestType() {
        return destType;
    }

    public void setDestType(String destType) {
        this.destType = destType;
    }

    public String getDestIntgModelId() {
        return destIntgModelId;
    }

    public void setDestIntgModelId(String destIntgModelId) {
        this.destIntgModelId = destIntgModelId;
    }

    public String getDestDatasourceName() {
        return destDatasourceName;
    }

    public void setDestDatasourceName(String destDatasourceName) {
        this.destDatasourceName = destDatasourceName;
    }

    public String getDestDatasourceId() {
        return destDatasourceId;
    }

    public void setDestDatasourceId(String destDatasourceId) {
        this.destDatasourceId = destDatasourceId;
    }

    public String getDestPluginPath() {
        return destPluginPath;
    }

    public void setDestPluginPath(String destPluginPath) {
        this.destPluginPath = destPluginPath;
    }

    public String getDestBaseJson() {
        return destBaseJson;
    }

    public void setDestBaseJson(String destBaseJson) {
        this.destBaseJson = destBaseJson;
    }

    public String getDestAdvJson() {
        return destAdvJson;
    }

    public void setDestAdvJson(String destAdvJson) {
        this.destAdvJson = destAdvJson;
    }

    public String getDestHighJson() {
        return destHighJson;
    }

    public void setDestHighJson(String destHighJson) {
        this.destHighJson = destHighJson;
    }

    public String getDestColumnJson() {
        return destColumnJson;
    }

    public void setDestColumnJson(String destColumnJson) {
        this.destColumnJson = destColumnJson;
    }

    public String getSchedulerExpr() {
        return schedulerExpr;
    }

    public void setSchedulerExpr(String schedulerExpr) {
        this.schedulerExpr = schedulerExpr;
    }

    public String getRouteStrategy() {
        return routeStrategy;
    }

    public void setRouteStrategy(String routeStrategy) {
        this.routeStrategy = routeStrategy;
    }

    public String getBlockStrategy() {
        return blockStrategy;
    }

    public void setBlockStrategy(String blockStrategy) {
        this.blockStrategy = blockStrategy;
    }

    public int getExecutorTimeout() {
        return executorTimeout;
    }

    public void setExecutorTimeout(int executorTimeout) {
        this.executorTimeout = executorTimeout;
    }

    public int getExecutorFailRetryCount() {
        return executorFailRetryCount;
    }

    public void setExecutorFailRetryCount(int executorFailRetryCount) {
        this.executorFailRetryCount = executorFailRetryCount;
    }

}
