package com.xmcares.platform.admin.developer.dataflow.vo;

import com.xmcares.platform.admin.developer.common.enums.DeveloperTaskType;
import com.xmcares.platform.admin.developer.dataflow.model.DevDataflowProcess;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件内容集合
 */
public class DevDataflowFileVo implements Serializable {

    /** 文件基本信息 */
    public static class DevDataflowFileHeader implements Serializable {
        /** 定义ID */
        private String id;
        /** 任务名称 */
        private String name;

        public static DevDataflowFileHeader createBy(DevDataflowProcess header) {
            DevDataflowFileHeader result = new DevDataflowFileHeader();
            result.setId(header.getId());
            result.setName(header.getName());
            return result;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    /** 文件中的任务信息 */
    public static class DevDataflowFileBody implements Serializable {

        /** 任务ID */
        private String id;
        /** 任务名称 */
        private String name;
        /** 任务类型 */
        private String taskType;
        /** 任务参数列表 */
        private List<DevDataflowFileParam> params;

        public static List<DevDataflowFileBody> createAll(List<DevDataflowNodeVo> nodes) {
            return nodes.stream().map(DevDataflowFileBody::createBy).collect(Collectors.toList());
        }

        private static DevDataflowFileBody createBy(DevDataflowNodeVo node) {
            DevDataflowFileBody result = new DevDataflowFileBody();
            result.setId(node.getId());
            result.setName(node.getLabel());
            result.setTaskType(node.getType());
            result.setParams(DevDataflowFileParam.createAll(node.getParams()));
            return result;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getTaskType() {
            return taskType;
        }

        public void setTaskType(String taskType) {
            this.taskType = taskType;
        }

        public List<DevDataflowFileParam> getParams() {
            return params;
        }

        public void setParams(List<DevDataflowFileParam> params) {
            this.params = params;
        }
    }

    /** 参数信息 */
    public static class DevDataflowFileParam implements Serializable {

        /** 参数标识符 */
        private String key;
        /** 参数值 */
        private Object value;

        public DevDataflowFileParam() {
        }

        public DevDataflowFileParam(String key, Object value) {
            this.key = key;
            this.value = value;
        }

        public static List<DevDataflowFileParam> createAll(List<DevDataflowNodeVo.DevDataflowNodeParam> param) {
            if (CollectionUtils.isEmpty(param)) { return new ArrayList<>(); }
            return param.stream().map(DevDataflowFileParam::createBy).collect(Collectors.toList());
        }

        private static DevDataflowFileParam createBy(DevDataflowNodeVo.DevDataflowNodeParam param) {
            DevDataflowFileParam result = new DevDataflowFileParam();
            result.setKey(param.getKey());
            result.setValue(param.getValue());
            return result;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public Object getValue() {
            return value;
        }

        public void setValue(Object value) {
            this.value = value;
        }
    }


    public static DevDataflowFileVo createBy(DevDataflowProcess header, List<DevDataflowNodeVo> nodes) {
        DevDataflowFileVo result = new DevDataflowFileVo();
        result.setHeader(DevDataflowFileHeader.createBy(header));
        result.setGlobalParams(DevDataflowFileParam.createAll(nodes.stream().filter(node ->
                node.getType().equals(DeveloperTaskType.SYS_GLOBAL_PARAM.name())).findFirst()
                .orElse(new DevDataflowNodeVo()).getParams()));
        result.setBodies(DevDataflowFileBody.createAll(nodes.stream().filter(node ->
                !node.getType().equals(DeveloperTaskType.SYS_GLOBAL_PARAM.name())).collect(Collectors.toList())));
        return result;
    }


    /** 文件内容基本信息 */
    private DevDataflowFileHeader header;
    /** 用户设置的全局参数 */
    private List<DevDataflowFileParam> globalParams;
    /** 任务列表 */
    private List<DevDataflowFileBody> bodies;

    public DevDataflowFileHeader getHeader() {
        return header;
    }

    public void setHeader(DevDataflowFileHeader header) {
        this.header = header;
    }

    public List<DevDataflowFileParam> getGlobalParams() {
        return globalParams;
    }

    public void setGlobalParams(List<DevDataflowFileParam> globalParams) {
        this.globalParams = globalParams;
    }

    public List<DevDataflowFileBody> getBodies() {
        return bodies;
    }

    public void setBodies(List<DevDataflowFileBody> bodies) {
        this.bodies = bodies;
    }
}
