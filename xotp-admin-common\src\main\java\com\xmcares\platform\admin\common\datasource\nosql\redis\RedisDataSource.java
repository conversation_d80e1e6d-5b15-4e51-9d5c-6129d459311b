/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/16
 */
package com.xmcares.platform.admin.common.datasource.nosql.redis;

import com.xmcares.platform.admin.common.datasource.DataSource;

/**
 * TODO
 * <AUTHOR>
 * @since 1.0.0
 */
public class RedisDataSource implements DataSource {
    @Override
    public AvailableStatus testAvailable() {
        return null;
    }

    @Override
    public String getName() {
        return "";
    }

    @Override
    public void close() throws Exception {

    }
}
