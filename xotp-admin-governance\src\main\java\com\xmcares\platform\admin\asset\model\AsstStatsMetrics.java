package com.xmcares.platform.admin.asset.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/27 09:29
 */
@ApiModel(value = AsstStatsMetrics.TABLE, description = "数据资产统计指标记录")
public class AsstStatsMetrics {
    public static final String TABLE = "bdp_asst_stats_metrics";

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "调度ID")
    private String statsSchedulerId;

    @ApiModelProperty(value = "目标id")
    private String statsTargets;

    @ApiModelProperty(value = "指标名")
    private String metricName;

    @ApiModelProperty(value = "指标标签")
    private String metricLabels;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "统计日")
    private Date metricDate;

    @ApiModelProperty(value = "统计值")
    private String metricValue;


    @ApiModelProperty(value = "统计执行状态（0：失败，1:成功）")
    private String stats_status;

    @ApiModelProperty(value = "统计执行报告")
    private String stats_report;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "统计执行时间")
    private Date statsTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "create_time")
    private Date createTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;

    public String getStatsTargets() {
        return statsTargets;
    }

    public void setStatsTargets(String statsTargets) {
        this.statsTargets = statsTargets;
    }

    public static String getTABLE() {
        return TABLE;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatsSchedulerId() {
        return statsSchedulerId;
    }

    public void setStatsSchedulerId(String statsSchedulerId) {
        this.statsSchedulerId = statsSchedulerId;
    }

    public String getMetricName() {
        return metricName;
    }

    public void setMetricName(String metricName) {
        this.metricName = metricName;
    }

    public String getMetricLabels() {
        return metricLabels;
    }

    public void setMetricLabels(String metricLabels) {
        this.metricLabels = metricLabels;
    }

    public Date getMetricDate() {
        return metricDate;
    }

    public void setMetricDate(Date metricDate) {
        this.metricDate = metricDate;
    }

    public String getMetricValue() {
        return metricValue;
    }

    public void setMetricValue(String metricValue) {
        this.metricValue = metricValue;
    }

    public String getStats_status() {
        return stats_status;
    }

    public void setStats_status(String stats_status) {
        this.stats_status = stats_status;
    }

    public String getStats_report() {
        return stats_report;
    }

    public void setStats_report(String stats_report) {
        this.stats_report = stats_report;
    }

    public Date getStatsTime() {
        return statsTime;
    }

    public void setStatsTime(Date statsTime) {
        this.statsTime = statsTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
