/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2023/6/4
 */
package com.xmcares.platform.admin.common.datasource.mq.rocketmq.core;

import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.client.ClientConfig;
import org.apache.rocketmq.client.Validators;
import org.apache.rocketmq.client.consumer.PullResult;
import org.apache.rocketmq.client.consumer.rebalance.AllocateMessageQueueAveragely;
import org.apache.rocketmq.client.consumer.store.OffsetStore;
import org.apache.rocketmq.client.consumer.store.ReadOffsetType;
import org.apache.rocketmq.client.consumer.store.RemoteBrokerOffsetStore;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.impl.CommunicationMode;
import org.apache.rocketmq.client.impl.MQClientManager;
import org.apache.rocketmq.client.impl.consumer.*;
import org.apache.rocketmq.client.impl.factory.MQClientInstance;
import org.apache.rocketmq.common.MixAll;
import org.apache.rocketmq.common.ServiceState;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.filter.ExpressionType;
import org.apache.rocketmq.common.filter.FilterAPI;
import org.apache.rocketmq.common.help.FAQUrl;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.message.MessageQueue;
import org.apache.rocketmq.common.protocol.NamespaceUtil;
import org.apache.rocketmq.common.protocol.body.ConsumerRunningInfo;
import org.apache.rocketmq.common.protocol.heartbeat.ConsumeType;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.common.protocol.heartbeat.SubscriptionData;
import org.apache.rocketmq.common.sysflag.PullSysFlag;
import org.apache.rocketmq.remoting.RPCHook;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 参考官方消息者{@link DefaultMQPullConsumerImpl}，重新定义消费者，直接从RocketMQ拉取并返回消息。
 * 自主拉取队列，提交offset，并行拉取等
 * @see org.apache.rocketmq.client.consumer.DefaultMQPullConsumer
 * <AUTHOR>
 * @since 1.1.0
 */
public class CustomMQConsumer extends ClientConfig implements MQConsumerInner{

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final String groupName;

    private final MessageModel messageModel = MessageModel.CLUSTERING;
    private MQClientInstance mqClientFactory;

    private CustomRebalanceImpl rebalanceImpl;

    private OffsetStore offsetStore;

    private PullAPIWrapper pullAPIWrapper;

    private RPCHook rpcHook;

    private volatile ServiceState serviceState = ServiceState.CREATE_JUST;

    /**
     * 启动时间，内部使用
     */
    private long startTimestamp;


    private long startOffsetBeforeHours = 12;

    /**
     * 存储负载均衡分配的队列
     */
    private final Map<String/*topic*/, Set<MessageQueue>> dividedQueues = new ConcurrentHashMap<>();


    public CustomMQConsumer(String groupName) {
        this.groupName = groupName;
    }

    public CustomMQConsumer(String groupName, RPCHook rpcHook) {
        this.groupName = groupName;
        this.rpcHook = rpcHook;
    }


    @Override
    public Set<SubscriptionData> subscriptions() {
        Set<SubscriptionData> subSet = new HashSet<SubscriptionData>();
        subSet.addAll(this.rebalanceImpl.getSubscriptionInner().values());
        return subSet;
    }

    /**
     * 同组消费者并行消费，使用{@link RebalanceImpl#doRebalance(boolean)}
     * 1）定时重新平衡会触发{@link RebalanceService#run()}
     * 2) 当新客户端接入到服务端时，服务端回调触发{@link MQClientInstance#rebalanceImmediately()}
     */
    @Override
    public void doRebalance() {
        this.rebalanceImpl.doRebalance(false);
    }

    /**
     * 重要：当负载队列刷新时，重新计算consumer实例分配的queue
     * @param topic
     * @param mqAll
     * @param mqDivided
     */
    protected void messageQueueChanged(String topic, Set<MessageQueue> mqAll, Set<MessageQueue> mqDivided) {
        Set<MessageQueue> messageQueues = dividedQueues.computeIfAbsent(topic, key -> new HashSet<>());

        //移除上次分配的不在本次分配的队列
        Iterator<MessageQueue> iterator = messageQueues.iterator();
        while (iterator.hasNext()) {
            MessageQueue preQueue = iterator.next();
            if (!mqDivided.contains(preQueue)) {
                //【重要】：移除偏移量缓存
                this.offsetStore.removeOffset(preQueue);
                iterator.remove();
                logger.info("移除客户端[{}]未被分配(负载均衡重新分配)的主题[{}]队列:[{}]", this.groupName, topic, preQueue);
            }
        }
        //添加本次新分配的队列
        for (MessageQueue divided : mqDivided) {
            //如果不存在，才put进去
            if (!messageQueues.contains(divided)) {
                messageQueues.add(divided);
                //【重要】：设置初始消费偏移
                long offset = this.rebalanceImpl.computePullFromWhere(divided);
                logger.info("客户端[{}]主题[{}]队列分配:[{}],[offset={}]", this.getInstanceName(), topic, divided, offset);
                this.offsetStore.updateOffset(divided, offset, true);

            }
        }
    }

    /**
     * 后台定时[{@link #getPersistConsumerOffsetInterval()}]
     * 调用{@link MQClientInstance#start()}持久化（提交服务端）offset
     */
    @Override
    public void persistConsumerOffset() {
        try {
            this.isRunning();
            Set<MessageQueue> mqs = new HashSet<>();
            Collection<Set<MessageQueue>> values = this.getDividedQueues().values();
            if (values != null && !values.isEmpty()) {
                for (Set<MessageQueue> value : values) {
                    mqs.addAll(value);
                }
            }
            this.offsetStore.persistAll(mqs);
        } catch (Exception e) {
            logger.error("客户端[ " + this.getInstanceName() + "] persistConsumerOffset exception", e);
        }
    }

    /**
     * 当主题路由信息发生变化时（如主题队列增加/减少），从主题订阅信息{@link RebalanceImpl#getSubscriptionInner()}更新队列
     * 更新均衡器主题队列信息{@link RebalanceImpl#getTopicSubscribeInfoTable()}
     * </br>
     * 后台每PollNameServerInterval时间{@link this#getPollNameServerInterval()}请求服务端{@link MQClientInstance#updateTopicRouteInfoFromNameServer()}
     * @param topic 主题
     * @param queues 主题队列s
     */
    @Override
    public void updateTopicSubscribeInfo(String topic, Set<MessageQueue> queues) {
        Map<String, SubscriptionData> subTable = this.rebalanceImpl.getSubscriptionInner();
        if (subTable != null) {
            if (subTable.containsKey(topic)) {
                this.rebalanceImpl.getTopicSubscribeInfoTable().put(topic, queues);
            }
        }
    }

    @Override
    public boolean isSubscribeTopicNeedUpdate(String topic) {
        Map<String, SubscriptionData> subTable = this.rebalanceImpl.getSubscriptionInner();
        if (subTable != null) {
            if (subTable.containsKey(topic)) {
                return !this.rebalanceImpl.getTopicSubscribeInfoTable().containsKey(topic);
            }
        }
        return false;
    }

    @Override
    public ConsumerRunningInfo consumerRunningInfo() {
        ConsumerRunningInfo info = new ConsumerRunningInfo();
        Properties prop = MixAll.object2Properties(this);
        prop.put(ConsumerRunningInfo.PROP_CONSUMER_START_TIMESTAMP, String.valueOf(this.startTimestamp));
        info.setProperties(prop);
        info.getSubscriptionSet().addAll(this.subscriptions());
        return info;
    }

    /**
     * 启动：1）初始化{@link MQClientInstance};2)初始化{@link CustomRebalanceImpl}
     * @throws MQClientException 启动失败
     */
    public synchronized void start() throws MQClientException {
        switch (this.serviceState) {
            case CREATE_JUST:
                this.serviceState = ServiceState.START_FAILED;
                this.startTimestamp = System.currentTimeMillis();

                this.checkConfig();

                initMQClientFactory();

                initRebalanceImpl();

                this.pullAPIWrapper = new PullAPIWrapper(mqClientFactory, this.groupName, isUnitMode());
                //this.pullAPIWrapper.registerFilterMessageHook(filterMessageHookList);

                this.offsetStore = new RemoteBrokerOffsetStore(this.mqClientFactory, this.groupName);
                this.offsetStore.load();

                this.mqClientFactory.start();
                logger.info("the consumer [{}] start OK", this.getInstanceName());
                this.serviceState = ServiceState.RUNNING;

                break;
            case RUNNING:
            case START_FAILED:
            case SHUTDOWN_ALREADY:
                throw new MQClientException("The PullConsumer service state not OK, maybe started once, "
                        + this.serviceState
                        + FAQUrl.suggestTodo(FAQUrl.CLIENT_SERVICE_NOT_OK),
                        null);
            default:
                break;
        }

    }

    public synchronized void shutdown() {
        switch (this.serviceState) {
            case CREATE_JUST:
                break;
            case RUNNING:
                this.persistConsumerOffset();
                this.mqClientFactory.unregisterConsumer(this.groupName);
                this.mqClientFactory.shutdown();
                logger.info("the consumer [{}] shutdown OK", this.getInstanceName());
                this.serviceState = ServiceState.SHUTDOWN_ALREADY;
                break;
            case SHUTDOWN_ALREADY:
                break;
            default:
                break;
        }
    }

    /**
     * 添加主题消息
     * @param topic 主题名
     * @throws MQClientException 异常
     */
    public void registerTopicIfAbsent(String topic) throws MQClientException {
        topic = withNamespace(topic);
        ConcurrentMap<String, SubscriptionData> subsDataMap = this.rebalanceImpl.getSubscriptionInner();
        if (!subsDataMap.containsKey(topic)) {
            SubscriptionData subsData = getSubscriptionData(topic);
            subsDataMap.put(topic, subsData);
            try {//备注：需主动更新主题路由
                this.mqClientFactory.updateTopicRouteInfoFromNameServer(topic);
            } catch (Exception e) {
                throw new MQClientException("subscribe exception", e);
            }

        }
    }

    public PullResult pullQueueMessage(SubscriptionData subscription, MessageQueue mq, long startOffset,
                                       int batchSize, boolean block, long timeout)
            throws MQClientException, RemotingException, MQBrokerException, InterruptedException {
        long offset = startOffset;
        if (offset <=-1 ) {
            offset = CustomMQConsumer.this.getQueueOffset(mq);
        }
        PullResult pullResult = CustomMQConsumer.this.pullSyncImpl(mq, subscription, offset, batchSize, block, timeout);
        if (logger.isDebugEnabled()) {
            logger.debug("Pulled: {}, tag{}, offset[{}:{}], count: {}", mq,
                    subscription.getTagsSet().toArray(), offset, pullResult.getNextBeginOffset(),
                    CollectionUtils.isEmpty(pullResult.getMsgFoundList())? 0 : pullResult.getMsgFoundList().size());
        }
        return pullResult;
    }

    /**
     * 更新消息队列偏移量
     * @param queue 消息队列
     * @param offset 新偏移量
     * @param increaseOnly 是否只增加
     */
    public void updateQueueOffset(MessageQueue queue, long offset, boolean increaseOnly) {
        if (logger.isDebugEnabled()) {
            logger.debug("客户端[ {} ]代理更新队列 {} 偏移量: {}", this.getInstanceName(), queue, offset);
        }
        this.offsetStore.updateOffset(queue, offset, increaseOnly);
    }

    /**
     * 获取消息队列偏移量
     * @param queue 队列
     * @return offset
     */
    public long getQueueOffset(MessageQueue queue) throws MQClientException {
        return this.offsetStore.readOffset(queue, ReadOffsetType.MEMORY_FIRST_THEN_STORE);
    }


    public SubscriptionData getSubscriptionData(String topic) throws MQClientException {
        return this.getSubscriptionData(topic, null);
    }
    public SubscriptionData getSubscriptionData(String topic, String tag) throws MQClientException {
        try {
            return FilterAPI.buildSubscriptionData(this.groupName, topic, tag);
        } catch (Exception e) {
            throw new MQClientException("parse subscription error", e);
        }
    }


    private void initMQClientFactory() throws MQClientException {
        this.mqClientFactory = MQClientManager.getInstance().getAndCreateMQClientInstance(this, this.rpcHook);
        boolean registerOK = mqClientFactory.registerConsumer(this.groupName, this);
        if (!registerOK) {
            this.serviceState = ServiceState.CREATE_JUST;
            throw new MQClientException("The consumer group[" + groupName
                    + "] has been created before, specify another name please." + FAQUrl.suggestTodo(FAQUrl.GROUP_NAME_DUPLICATE_URL),
                    null);
        }
    }

    private void initRebalanceImpl() {
        AllocateMessageQueueAveragely allocateStrategy = new AllocateMessageQueueAveragely();
        this.rebalanceImpl = new CustomRebalanceImpl(this);
        this.rebalanceImpl.setMessageModel(this.messageModel);
        this.rebalanceImpl.setAllocateMessageQueueStrategy(allocateStrategy);
        this.rebalanceImpl.setmQClientFactory(this.mqClientFactory);
    }

    private void isRunning() throws MQClientException {
        if (this.serviceState != ServiceState.RUNNING) {
            throw new MQClientException("The consumer is not in running status, "
                    + this.serviceState
                    + FAQUrl.suggestTodo(FAQUrl.CLIENT_SERVICE_NOT_OK),
                    null);
        }
    }

    private void checkConfig() throws MQClientException {
        // check consumerGroup
        Validators.checkGroup(this.groupName);

        // consumerGroup
        if (this.groupName.equals(MixAll.DEFAULT_CONSUMER_GROUP)) {
            throw new MQClientException(
                    "consumerGroup can not equal "
                            + MixAll.DEFAULT_CONSUMER_GROUP
                            + ", please specify another one."
                            + FAQUrl.suggestTodo(FAQUrl.CLIENT_PARAMETER_CHECK_URL),
                    null);
        }

        // messageModel
        if (null == this.messageModel) {
            throw new MQClientException(
                    "messageModel is null"
                            + FAQUrl.suggestTodo(FAQUrl.CLIENT_PARAMETER_CHECK_URL),
                    null);
        }

    }


    private PullResult pullSyncImpl(MessageQueue mq, SubscriptionData subscriptionData,
                                    long offset, int maxNums, boolean block, long timeout)
            throws MQClientException, RemotingException, MQBrokerException, InterruptedException {
        this.isRunning();

        if (null == mq) {
            throw new MQClientException("mq is null", null);
        }

        if (offset < 0) {
            throw new MQClientException("offset < 0", null);
        }

        if (maxNums <= 0) {
            throw new MQClientException("maxNums <= 0", null);
        }

        //this.subscriptionAutomatically(mq.getTopic());

        int sysFlag = PullSysFlag.buildSysFlag(false, block, true, false);

        boolean isTagType = ExpressionType.isTagType(subscriptionData.getExpressionType());
        PullResult pullResult = this.pullAPIWrapper.pullKernelImpl(
                mq,
                subscriptionData.getSubString(),
                subscriptionData.getExpressionType(),
                isTagType ? 0L : subscriptionData.getSubVersion(),
                offset,
                maxNums,
                sysFlag,
                0,
                this.getBrokerSuspendMaxTimeMillis(),
                timeout,
                CommunicationMode.SYNC,
                null
        );
        this.pullAPIWrapper.processPullResult(mq, pullResult, subscriptionData);
        //If namespace is not null , reset Topic without namespace.
        this.resetTopic(pullResult.getMsgFoundList());
        return pullResult;
    }


    private void resetTopic(List<MessageExt> msgList) {
        if (null == msgList || msgList.isEmpty()) {
            return;
        }
        //If namespace not null , reset Topic without namespace.
        for (MessageExt messageExt : msgList) {
            if (null != this.getNamespace()) {
                messageExt.setTopic(NamespaceUtil.withoutNamespace(messageExt.getTopic(), this.getNamespace()));
            }
        }

    }


    ///////////////////////////////////////////////////////////////////////////
    // getter / setter
    ///////////////////////////////////////////////////////////////////////////

    public MQClientInstance getMqClientFactory() {
        return mqClientFactory;
    }

    @Override
    public String groupName() {
        return this.groupName;
    }

    @Override
    public MessageModel messageModel() {
        return this.messageModel;
    }

    @Override
    public ConsumeType consumeType() {
        return ConsumeType.CONSUME_ACTIVELY;
    }

    @Override
    public ConsumeFromWhere consumeFromWhere() {
        return ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET;
    }

    public long consumeOffsetBeforeHours() {
        return this.startOffsetBeforeHours;
    }

    public void setStartOffsetBeforeHours(long startOffsetBeforeHours) {
        this.startOffsetBeforeHours = startOffsetBeforeHours;
    }

    public long getBrokerSuspendMaxTimeMillis() {
        return 0;
    }


    public OffsetStore getOffsetStore() {
        return this.offsetStore;
    }

    public Map<String, Set<MessageQueue>> getDividedQueues() {
        return this.dividedQueues;
    }

    /**
     * 使用computeIfAbsent时注意：
     * JDK8 ConcurrentMap.computeIfAbsent存在BUG
     * See Also: <a href="https://bugs.openjdk.java.net/browse/JDK-8161372">JDK-8161372</a>
     */
    public Set<MessageQueue> getDividedQueuesOf(String topic) {
        return dividedQueues.computeIfAbsent(topic, key -> new HashSet<>());
    }

}
