package com.xmcares.platform.admin.asset.web;

import com.xmcares.platform.admin.asset.model.MetaCatalog;
import com.xmcares.platform.admin.asset.model.MetaDatatable;
import com.xmcares.platform.admin.asset.model.MetaDatatableColumn;
import com.xmcares.platform.admin.asset.model.MetaDataware;
import com.xmcares.platform.admin.asset.model.vo.DatatableAllQueryVO;
import com.xmcares.platform.admin.asset.model.vo.DatatableColumnSyncQueryVO;
import com.xmcares.platform.admin.asset.model.vo.DatatableSyncQueryVO;
import com.xmcares.platform.admin.asset.model.vo.TreeQueryVO;
import com.xmcares.platform.admin.asset.repository.AssetCatalogRepository;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/25 09:26
 */
@Api(value = "资产目录服务")
@Validated
@RestController
@RequestMapping("/asset/category")
public class AssetCatalogController {

    @Autowired
    AssetCatalogRepository assetCatalogRepository;

    @ApiOperation("查询仓库")
    @GetMapping("/dataware/list-query")
    @ResponseBody
    public List<MetaDataware> datawareListQuery() {
        return assetCatalogRepository.datawareListQuery();
    }

    @ApiOperation("新增仓库")
    @PostMapping("/dataware/add")
    @ResponseBody
    public String datawareAdd(
            @RequestBody MetaDataware metaDataware
    ) {
        return assetCatalogRepository.datawareAdd(metaDataware);
    }

    @ApiOperation("修改仓库")
    @PostMapping("/dataware/update")
    @ResponseBody
    public String datawareUpdate(
            @RequestBody MetaDataware metaDataware
    ) {
        return assetCatalogRepository.datawareUpdate(metaDataware);
    }

    @ApiOperation("删除仓库")
    @PostMapping("/dataware/delete")
    @ResponseBody
    public String datawareDelete(
            @RequestParam(name = "id") String id
    ) {
        return assetCatalogRepository.datawareDelete(id);
    }

    @ApiOperation("查询类目树")
    @GetMapping("/tree-query")
    @ResponseBody
    public List<TreeQueryVO> treeQuery(
            String datawareId,
            String name
    ) {
        return assetCatalogRepository.treeQuery(name, datawareId);
    }

    @ApiOperation("新增类目")
    @PostMapping("/add")
    @ResponseBody
    public String add(
            @RequestBody MetaCatalog metaCatalog
    ) {
        return assetCatalogRepository.add(metaCatalog);
    }

    @ApiOperation("修改类目")
    @PostMapping("/update")
    @ResponseBody
    public String update(
            @RequestBody MetaCatalog metaCatalog
    ) {
        return assetCatalogRepository.update(metaCatalog);
    }


    @ApiOperation("删除类目")
    @PostMapping("/delete")
    @ResponseBody
    public String delete(
            @RequestParam(name = "id") String id
    ) {
        return assetCatalogRepository.delete(id);
    }


    @ApiOperation("移动表至类目")
    @PostMapping("/datatable/move")
    @ResponseBody
    public String datatableMove(
            @RequestParam(name = "datatableId") String datatableId,
            @RequestParam(name = "categoryId") String categoryId
    ) {
        return assetCatalogRepository.datatableMove(datatableId, categoryId);
    }


    @ApiOperation("查询仓库表同步信息")
    @GetMapping("/datatable-sync/query")
    @ResponseBody
    public DatatableSyncQueryVO datatableSyncQuery(
            @RequestParam(name = "datawareId") String datawareId
    ) {
        return assetCatalogRepository.datatableSyncQuery(datawareId);
    }


    @ApiOperation("保存仓库表同步信息")
    @PostMapping("/datatable-sync/save")
    @ResponseBody
    public String datatableSyncSave(
            @RequestBody DatatableSyncQueryVO datatableSyncQueryVO
    ) {
        return assetCatalogRepository.datatableSyncSave(datatableSyncQueryVO);
    }

    @ApiOperation("查询表基础信息以及字段信息")
    @GetMapping("/datatable/all-query")
    @ResponseBody
    public DatatableAllQueryVO datatableAllQuery(
            @RequestParam(name = "datatableId") String datatableId
    ) {
        return assetCatalogRepository.datatableAllQuery(datatableId);
    }

    @ApiOperation("更新表基础信息")
    @PostMapping("/datatable/update")
    @ResponseBody
    public String datatableUpdate(
            @RequestBody MetaDatatable metaDatatable
    ) {
        return assetCatalogRepository.datatableUpdate(metaDatatable);
    }

    @ApiOperation("更新表字段信息")
    @PostMapping("/datatable/column/update")
    @ResponseBody
    public String datatableColumnUpdate(
            @RequestBody MetaDatatableColumn metaDatatableColumn
    ) {
        return assetCatalogRepository.datatableColumnUpdate(metaDatatableColumn);
    }

    @ApiOperation("查询表字段同步信息")
    @GetMapping("/datatable/column-sync/query")
    @ResponseBody
    public DatatableColumnSyncQueryVO datatableColumnSyncQuery(
            @RequestParam(name = "datatableId") String datatableId
    ) {
        return assetCatalogRepository.datatableColumnSyncQuery(datatableId);
    }

    @ApiOperation("查询表已经同步的字段信息")
    @GetMapping("/datatable/column-synced/query")
    @ResponseBody
    public List<MetaDatatableColumn> datatableColumnSyncedQuery(
            @RequestParam(name = "datasourceId") String datasourceId,
            @RequestParam(name = "datatableId") String datatableId
    ) {
        return assetCatalogRepository.datatableColumnSyncedQuery(datasourceId, datatableId);
    }

    @ApiOperation("保存表字段同步信息")
    @PostMapping("/datatable/column-sync/save")
    @ResponseBody
    public String datatableColumnSyncSave(
            @RequestBody DatatableColumnSyncQueryVO datatableColumnSyncQueryVO
    ) {
        return assetCatalogRepository.datatableColumnSyncSave(datatableColumnSyncQueryVO);
    }


    @ApiOperation("查询表记录")
    @GetMapping("/datatable/record/query")
    @ResponseBody
    public List<Map<String, Object>> datatableRecordQuery(
            @RequestParam(name = "datatableId") String datatableId,
            @RequestParam(name = "pageNo") Integer pageNo,
            @RequestParam(name = "pageSize") Integer pageSize
    ) {
        int i = pageNo - 1;
        return assetCatalogRepository.datatableRecordQuery(datatableId, i < 0 ? 0 : i, pageSize);
    }
}
