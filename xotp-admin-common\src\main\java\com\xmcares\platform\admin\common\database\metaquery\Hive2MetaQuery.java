/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/24
 */
package com.xmcares.platform.admin.common.database.metaquery;

import com.xmcares.platform.admin.common.database.DataSourceBasedMetaQuery;
import com.xmcares.platform.admin.common.database.metainfo.ColumnInfo;
import com.xmcares.platform.admin.common.database.metainfo.TableInfo;
import com.xmcares.platform.admin.common.errors.MetadataException;
import com.xmcares.platform.admin.common.jdbc.JdbcUtils;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * Hive2.x 的元数据查询实现
 * <AUTHOR>
 * @since 1.0.0
 */
public class Hive2MetaQuery extends DataSourceBasedMetaQuery {

    /**
     * 获取表信息,“as name”、 “as comment” 便于TableInfo对象映射
     */
    private static final String SQL_TABLES = "SELECT table_name AS name, table_comment AS comment " +
            "FROM information_schema.tables WHERE table_schema = ?";
    private static final String SQL_TABLE_COLUMNS = "SELECT column_name AS name , data_type AS type, column_comment AS comment " +
            "FROM information_schema.columns WHERE table_schema = ? AND table_name = ?";

    public Hive2MetaQuery(DataSource dataSource) {
        super(dataSource);
    }

    public Hive2MetaQuery(DataSource dataSource, String schema) {
        super(dataSource, schema);
    }

    @Override
    public List<TableInfo> getTableInfos() {
        try {
            return JdbcUtils.executeQuery(this.dataSource, "SHOW TABLES", rs -> {
                List<TableInfo> result = new ArrayList<>();
                while (rs.next()) {
                    String tableName = rs.getString(1);
                    TableInfo info = getTableInfo(tableName);
                    result.add(info);
                }
                return result;
            });
        } catch (SQLException e) {
            throw new MetadataException(String.format("获取数据库[%s]所有表TableInfo失败", this.schema), e);
        }
    }

    @Override
    public TableInfo getTableInfo(String tableName) {
        TableInfo info = new TableInfo();
        info.setName(tableName);

        try (Connection conn = this.dataSource.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("DESCRIBE EXTENDED " + tableName)
        ) {
            while (rs.next()) {
                String colName = rs.getString(1);
                if (colName != null && colName.trim().startsWith("# Detailed Table Information")) {
                    if (rs.next()) {
                        String json = rs.getString(1);
                        if (json != null && json.contains("comment")) {
                            int i = json.indexOf("comment");
                            int end = json.indexOf(",", i);
                            String comment = json.substring(i, end);
                            comment = comment.split(":")[1].replaceAll("\"", "").trim();
                            info.setComment(comment);
                        }
                    }
                    break;
                }
            }

        } catch (SQLException e) {
            throw new MetadataException(String.format("获取数据库[%s]表[%s]TableInfo失败", this.schema, tableName), e);
        }
        return info;
    }

    @Override
    public List<ColumnInfo> getColumnInfos(String tableName) {
        List<ColumnInfo> columns = new ArrayList<>();
        try (Connection conn = this.dataSource.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("DESCRIBE " + tableName)) {

            while (rs.next()) {
                String name = rs.getString(1);
                String type = rs.getString(2);
                String comment = rs.getString(3);

                if (name == null || name.trim().isEmpty() || name.trim().startsWith("#")) {
                    continue;
                }

                ColumnInfo column = new ColumnInfo();
                column.setName(name.trim());
                column.setType(type != null ? type.trim() : "");
                column.setComment(comment != null ? comment.trim() : "");
                columns.add(column);
            }

        } catch (SQLException e) {
            throw new MetadataException(String.format("获取数据库[%s]表[%s]列ColumnInfo失败", this.schema, tableName), e);
        }

        return columns;
    }
}
