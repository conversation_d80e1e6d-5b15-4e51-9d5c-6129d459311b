package com.xmcares.platform.admin.integrator.datasync.vo;

import com.xmcares.platform.admin.common.validation.Update;
import com.xmcares.platform.admin.integrator.datasync.model.Datasync;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/17 17:26
 */
public class UpdateDatasyncTask implements Serializable {
    /** ID */
    @NotNull(message = "ID不允许为空", groups = Update.class)
    @ApiModelProperty(value = "主键", notes = "修改时不允许为空")
    private String id;
    /** 同步任务名称 */
    private String instanceName;
    /** 调度参数 */
    @ApiModelProperty(value = "调度参数")
    @NotNull(message = "调度参数")
    private String schedulerExpr;
    /** 路由策略 */
    @ApiModelProperty(value = "路由策略")
    @NotNull(message = "路由策略")
    private String routeStrategy;
    /** 阻塞策略 */
    @ApiModelProperty(value = "阻塞策略")
    @NotNull(message = "阻塞策略")
    private String blockStrategy;
    /** 执行超时时间 */
    @ApiModelProperty(value = "执行超时时间")
    @NotNull(message = "执行超时时间")
    private int executorTimeout;
    /** 执行失败重试次数 */
    @ApiModelProperty(value = "执行失败重试次数")
    @NotNull(message = "执行失败重试次数")
    private int executorFailRetryCount;

    public static Datasync toDatasync(UpdateDatasyncTask datasyncTask) {
        Datasync result = new Datasync();
        result.setSchedulerExpr(datasyncTask.getSchedulerExpr());
        result.setRouteStrategy(datasyncTask.getRouteStrategy());
        result.setBlockStrategy(datasyncTask.getBlockStrategy());
        result.setExecutorTimeout(datasyncTask.getExecutorTimeout());
        result.setExecutorFailRetryCount(datasyncTask.getExecutorFailRetryCount());
        return result;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public String getSchedulerExpr() {
        return schedulerExpr;
    }

    public void setSchedulerExpr(String schedulerExpr) {
        this.schedulerExpr = schedulerExpr;
    }

    public String getRouteStrategy() {
        return routeStrategy;
    }

    public void setRouteStrategy(String routeStrategy) {
        this.routeStrategy = routeStrategy;
    }

    public String getBlockStrategy() {
        return blockStrategy;
    }

    public void setBlockStrategy(String blockStrategy) {
        this.blockStrategy = blockStrategy;
    }

    public int getExecutorTimeout() {
        return executorTimeout;
    }

    public void setExecutorTimeout(int executorTimeout) {
        this.executorTimeout = executorTimeout;
    }

    public int getExecutorFailRetryCount() {
        return executorFailRetryCount;
    }

    public void setExecutorFailRetryCount(int executorFailRetryCount) {
        this.executorFailRetryCount = executorFailRetryCount;
    }
}
