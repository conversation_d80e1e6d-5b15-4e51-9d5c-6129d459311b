package com.xmcares.platform.admin.quality.model.vo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/16 10:18
 */
public class QualitySummaryRuleSchedulerCountVO implements Serializable {

    private List<DatatableScheduelrCount> datatableScheduelrCounts;
    private Integer totalScheduleCount;

    public static class DatatableScheduelrCount {
        private String datawareId;
        private String datatableId;
        private Integer scheduleCount;

        public String getDatawareId() {
            return datawareId;
        }

        public void setDatawareId(String datawareId) {
            this.datawareId = datawareId;
        }

        public String getDatatableId() {
            return datatableId;
        }

        public void setDatatableId(String datatableId) {
            this.datatableId = datatableId;
        }

        public Integer getScheduleCount() {
            return scheduleCount;
        }

        public void setScheduleCount(Integer scheduleCount) {
            this.scheduleCount = scheduleCount;
        }
    }

    public List<DatatableScheduelrCount> getDatatableScheduelrCounts() {
        return datatableScheduelrCounts;
    }

    public void setDatatableScheduelrCounts(List<DatatableScheduelrCount> datatableScheduelrCounts) {
        this.datatableScheduelrCounts = datatableScheduelrCounts;
    }

    public Integer getTotalScheduleCount() {
        return totalScheduleCount;
    }

    public void setTotalScheduleCount(Integer totalScheduleCount) {
        this.totalScheduleCount = totalScheduleCount;
    }
}
