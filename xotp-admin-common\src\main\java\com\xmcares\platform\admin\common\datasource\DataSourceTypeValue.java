/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/6/17
 */
package com.xmcares.platform.admin.common.datasource;

/**
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class DataSourceTypeValue {
    private final String category;
    private final String type;

    public DataSourceTypeValue(String category, String type) {
        this.category = category;
        this.type = type;
    }

    public String getCategory() {
        return category;
    }

    public String getType() {
        return type;
    }
}
