{
          "name": "elasticsearchwriter",
          "parameter": {
            "endpoint": ${destDatasource.endpoint},
            "accessId": ${destDatasource.accessId},
            "accessKey": ${destDatasource.accessKey},
            "index": ${destDatasource.index},
            "type": ${destDatasource.type},
            "cleanup": ${destDatasource.cleanup},
            "settings": {
              "index" :{"number_of_shards": 1, "number_of_replicas": 0}
            },
            "discovery": ${destDatasource.discovery},
            "batchSize": ${destDatasource.batchSize},
            "splitter": ${destDatasource.splitter},
            "column": [
<#list destDatasource.columns as column>
    ${column}<#if column_has_next>,</#if>
</#list>
            ]
          }
        }
      }