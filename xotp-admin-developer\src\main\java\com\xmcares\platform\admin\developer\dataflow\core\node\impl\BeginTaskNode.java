package com.xmcares.platform.admin.developer.dataflow.core.node.impl;


import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.developer.common.enums.DeveloperDispatchType;
import com.xmcares.platform.admin.developer.dataflow.core.node.BaseTaskNode;
import com.xmcares.platform.admin.developer.dataflow.vo.DevDataflowNodeVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.quartz.CronExpression;

import java.util.ArrayList;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/30 11:08
 **/
public class BeginTaskNode extends BaseTaskNode {



    @Override
    public void validate() throws BusinessException {
        // 1. 开始节点不允许有前置节点
        if (CollectionUtils.isNotEmpty(nodeInfo().getPreIds())) {
            throw new BusinessException("节点【" + nodeInfo().getLabel() + "】不允许存在前置节点！");
        }
        if (CollectionUtils.isEmpty(nodeInfo().getPostIds())) {
            throw new BusinessException("节点【" + nodeInfo().getLabel() + "】未设置后置节点，该节点无异议！");
        }
        // 2. 开始节点的必要参数校验
        Map<String, Object> handler = Optional.of(nodeInfo().getParams()).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(DevDataflowNodeVo.DevDataflowNodeParam::getKey, DevDataflowNodeVo.DevDataflowNodeParam::getValue));
        if (!handler.containsKey(KEY_SCHEDULER_TYPE) || ObjectUtils.isEmpty(handler.get(KEY_SCHEDULER_TYPE)) || StringUtils.isEmpty((String)handler.get(KEY_SCHEDULER_TYPE))) {
            throw new BusinessException(String.format("节点【%s】未设置调度类型", nodeInfo().getLabel()));
        }
        if (!DeveloperDispatchType.CRON.getXxlJobDispatchType().equalsIgnoreCase(handler.get(KEY_SCHEDULER_TYPE).toString())) {
            throw new BusinessException(String.format("节点【%s】调度类型请设置成CRON", nodeInfo().getLabel()));
        }
        if (!handler.containsKey(KEY_SCHEDULER_OPTION) || ObjectUtils.isEmpty(handler.get(KEY_SCHEDULER_OPTION)) || StringUtils.isEmpty((String)handler.get(KEY_SCHEDULER_OPTION))) {
            throw new BusinessException(String.format("节点【%s】未设置调度参数", nodeInfo().getLabel()));
        }

        if (!CronExpression.isValidExpression(handler.get(KEY_SCHEDULER_OPTION).toString())) {
            throw new BusinessException(String.format("节点【%s】的调度参数配置不正确！", nodeInfo().getLabel()));
        }
        if (!handler.containsKey(KEY_SCHEDULER_RETRY_COUNT) || ObjectUtils.isEmpty(handler.get(KEY_SCHEDULER_RETRY_COUNT)) || !NumberUtils.isCreatable(String.valueOf(handler.get(KEY_SCHEDULER_RETRY_COUNT)))) {
            throw new BusinessException(String.format("节点【%s】未设置调度重试次数", nodeInfo().getLabel()));
        }
    }

}

