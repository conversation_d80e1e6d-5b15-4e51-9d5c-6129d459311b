package com.xmcares.platform.admin.integrator.datasync.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xmcares.framework.fsclient.FSTemplate;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.platform.admin.common.datasource.mq.MqType;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.integrator.IntegratorProperties;
import com.xmcares.platform.admin.integrator.common.util.ConstantUtils;
import com.xmcares.platform.admin.integrator.common.util.DataxFtlUtils;
import com.xmcares.platform.admin.integrator.common.util.IDataxFtlDatasource;
import com.xmcares.platform.admin.integrator.datasync.dto.DatasyncDto;
import com.xmcares.platform.admin.integrator.datasync.error.ScheduleFailureException;
import com.xmcares.platform.admin.integrator.datasync.vo.DataxTempVo;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import com.xmcares.platform.admin.metadata.database.service.DatasourceService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR> chenYG
 * @date : 2022/3/29 16:58
 */
@Component
public class DataxFileRepository {




    private static final Logger LOG = LoggerFactory.getLogger(DataxFileRepository.class);

    private final String rootPath;
    private final FSTemplate fsTemplate;

    private final DatasourceService datasourceService;

    private final Configuration tempConfig;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    public DataxFileRepository(FSTemplate fsTemplate, @Qualifier(value = "tmplConfiguration") Configuration tempConfig, IntegratorProperties properties, DatasourceService datasourceService) {
        this.fsTemplate = fsTemplate;
        this.tempConfig = tempConfig;
        this.rootPath = properties.getFileServerRoot() + ConstantUtils.FILE_DATAX_JOB_DIR;
        this.datasourceService = datasourceService;
    }

    public String buildTempPath(String id, String code) {
        return this.rootPath + "/" + id + "_" + code + ConstantUtils.FILE_DATAX_TEMP_SUFFIX;
    }

    public String upload(DatasyncDto saveDatasync, String code) {
        String tempPath = this.rootPath + "/" + saveDatasync.getId() + "_" + code + ConstantUtils.FILE_DATAX_TEMP_SUFFIX;
        // 设置模板数据
        Map<String, Object> dataSource = DataxFtlUtils.buildDataxFtlDatasource(saveDatasync, new IDataxFtlDatasource() {
            @Override
            public Map<String, Object> fromDatasource() {
                return checkAndGetDataSource("数据来源", saveDatasync.getOrginDatasourceId());
            }
            @Override
            public Map<String, Object> toDatasource() {
                return checkAndGetDataSource("数据去向", saveDatasync.getDestDatasourceId());
            }
        });
        // 上传文件
        addFile(saveDatasync.getIntgName(), dataSource, tempPath);
        return tempPath;
    }

    public void upload(String filePath, String context) {
        try (ByteArrayInputStream input = new ByteArrayInputStream(context.getBytes(StandardCharsets.UTF_8))) {
            FileDesc desc = new FileDesc.FileDescImpl();
            fsTemplate.saveFile(new FileDesc.FileDescImpl(null, filePath), input);
        } catch (Exception e) {
            throw new ScheduleFailureException(
                    String.format("保存Datax数据同步任务[%s]文件异常", filePath), e);
        }
    }

    public DataxTempVo reader(DatasyncDto saveDatasync) {
        // 设置模板数据
        Map<String, Object> dataSource = DataxFtlUtils.buildDataxFtlDatasource(saveDatasync, new IDataxFtlDatasource() {
            @Override
            public Map<String, Object> fromDatasource() {
                return checkAndGetDataSource("数据来源", saveDatasync.getOrginDatasourceId());
            }
            @Override
            public Map<String, Object> toDatasource() {
                return checkAndGetDataSource("数据去向", saveDatasync.getDestDatasourceId());
            }
        });
        return new DataxTempVo(reader(saveDatasync.getIntgName(), dataSource), dataSource);
    }

    private String reader(String name, Map<String, Object> dataSource) {
        Template template;
        try {
            template = tempConfig.getTemplate(ConstantUtils.DATAX_TEMP_MAIN_NAME);
        } catch (IOException e) {
            throw new ScheduleFailureException("获取datax同步配置文件模板失败", e);
        }
        // 如果是ximc类型，需要手动加上pulsar配置
        if (MqType.XIMC.equalsTypeName(MapUtil.getStr(dataSource, "readerPlugin",""))) {
            // TODO: 先写死
            JSONObject pulsarConfig = new JSONObject();
            pulsarConfig.put("pulsarServiceUrl", "pulsar://*************:31834");
            pulsarConfig.put("pulsarTopic", "persistent://public/default/xopt_topic");
            pulsarConfig.put("pulsarSubscription", "datax-pulsar-sub");
            dataSource.put("pulsarConfig", pulsarConfig);
        }
        try (ByteArrayOutputStream output = new ByteArrayOutputStream();
             Writer writer = new OutputStreamWriter(output)
        ) {
            template.process(dataSource, writer);
            return new String(output.toByteArray(), StandardCharsets.UTF_8);
        } catch (TemplateException e) {
            throw new ScheduleFailureException(
                    String.format("处理Datax数据同步任务[%s]模板异常", name), e);
        } catch (Exception e) {
            throw new ScheduleFailureException(
                    String.format("保存Datax数据同步任务[%s]文件异常", name), e);
        }
    }


    private Map<String, Object> checkAndGetDataSource(String sourceType, String dsId) {
        Map<String, Object> source = null;
        Assert.notNull(dsId, "请设置" + sourceType + "的ID");
        // 修改原始feign调用服务
        // source = datasourceClient.getDatasourceMeta(dsId);
        Datasource datasource = datasourceService.getDatasource(dsId);
        // TODO: 测试转换
        source = new java.util.HashMap<>();
        source = BeanUtil.beanToMap(datasource);
//        BeanUtils.copyProperties(datasource, source);
        // 使用Jackson ObjectMapper将对象转换为Map
//        Map<String, Object> source = objectMapper.convertValue(datasource, Map.class);
        // TODO
        Assert.notNull(source, "未找到" + sourceType + "的数据源信息");
        return source;
    }

    /**
     * 上传datax配置文件
     * @param dataSource 数据源
     * @param filePath 文件路径
     */
    private void addFile(String name, Map<String, Object> dataSource, String filePath) {
        Template template;
        try {
            template = tempConfig.getTemplate(ConstantUtils.DATAX_TEMP_MAIN_NAME);
        } catch (IOException e) {
            throw new ScheduleFailureException("获取datax同步配置文件模板失败", e);
        }
        try (ByteArrayOutputStream output = new ByteArrayOutputStream();
             Writer writer = new OutputStreamWriter(output)
        ) {
            template.process(dataSource, writer);
            try (ByteArrayInputStream input = new ByteArrayInputStream(output.toByteArray())) {
                fsTemplate.saveFile(new FileDesc.FileDescImpl(null, filePath), input);
            }
        } catch (TemplateException e) {
            throw new ScheduleFailureException(
                    String.format("处理Datax数据同步任务[%s]模板异常", name), e);
        } catch (Exception e) {
            throw new ScheduleFailureException(
                    String.format("保存Datax数据同步任务[%s]文件异常", name), e);
        }
    }

    public void removeFile(String filePath) {
        try {
            fsTemplate.deleteFile(filePath);
        } catch (Exception e) {
            LOG.error("删除文件【" + filePath + "】失败，失败原因：", e);
        }
    }

    public String readerContext(String filePath) {
        try (ByteArrayOutputStream output = new ByteArrayOutputStream();){
            // 标准化路径，将所有的反斜杠替换为正斜杠，解决混合路径分隔符的问题
            filePath = StringUtils.replace(filePath, "\\", "/");
            fsTemplate.loadFile(filePath, output);
            return new String(output.toByteArray(), StandardCharsets.UTF_8);
        } catch (Exception ioException) {
            throw new BusinessException("读取文件失败, 可能文件不存在导致");
        }
    }
}
