package com.xmcares.platform.admin.quality.service;

import com.xmcares.platform.admin.quality.model.QltyRuleMetrics;
import com.xmcares.platform.admin.quality.model.QltyRuleScheduler;
import com.xmcares.platform.admin.quality.model.XxlJobLog;
import com.xmcares.platform.admin.quality.model.vo.QualitySummaryDatatableScoreTopsQueryVO;
import com.xmcares.platform.admin.quality.model.vo.QualitySummaryDatawareScoreQueryVO;
import com.xmcares.platform.admin.quality.model.vo.QualitySummaryRuleSchedulerCountVO;
import com.xmcares.platform.admin.quality.model.vo.QualitySummaryRuleStateCountVO;
import com.xmcares.platform.admin.quality.repository.QualitySummaryRepository;
import com.xmcares.platform.admin.quality.repository.XbdpQltyController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/25 09:10
 */
@Service
public class QualitySummaryService {
    private static final Logger LOG = LoggerFactory.getLogger(QualitySummaryService.class);

    @Autowired
    QualitySummaryRepository qualitySummaryRepository;

    @Autowired
    XbdpQltyController xbdpQltyController;


    DecimalFormat floadValueFormat = new DecimalFormat("#.00");

    public QualitySummaryDatawareScoreQueryVO datawareScoreQuery(String[] datawareIds, String[] ruleIds, String ruleLevel, String beginDate, String endDate) {
        List<QltyRuleMetrics> qltyRuleMetrics = qualitySummaryRepository.queryQltyRuleMetricsByDataware(datawareIds, ruleIds, ruleLevel, beginDate, endDate);

        QualitySummaryDatawareScoreQueryVO datawareScoreQueryVO = new QualitySummaryDatawareScoreQueryVO();
        datawareScoreQueryVO.setDatawareDimSocre(new ArrayList<Map<String, Object>>());
        datawareScoreQueryVO.setDatawareAllSocre(new ArrayList<Map<String, Object>>());


        for (QltyRuleMetrics qltyRuleMetric : qltyRuleMetrics) {
            qltyRuleMetric.setMetric_value(floadValueFormat.format(Double.valueOf(qltyRuleMetric.getMetric_value())));
            if (qltyRuleMetric.getRule_id().equals(ruleIds[0])) {
                datawareScoreQueryVO.getDatawareDimSocre().add(new HashMap<String, Object>() {{
                    put("date",qltyRuleMetric.getMetric_date());
                    put("datawareId", qltyRuleMetric.getDataware_id());
                    put("datawareName", qltyRuleMetric.getDataware_name());
                    put("dimCode", qltyRuleMetric.getDim_code());
                    put("score", qltyRuleMetric.getMetric_value());
                }});
            }
            if (qltyRuleMetric.getRule_id().equals(ruleIds[1])) {
                datawareScoreQueryVO.getDatawareAllSocre().add(new HashMap<String, Object>() {{
                    put("date", qltyRuleMetric.getMetric_date());
                    put("datawareId", qltyRuleMetric.getDataware_id());
                    put("datawareName", qltyRuleMetric.getDataware_name());
                    put("score", qltyRuleMetric.getMetric_value());
                }});
            }
        }

        return datawareScoreQueryVO;
    }

    public ArrayList<QualitySummaryDatatableScoreTopsQueryVO> datatableScoreTopsQuery(String[] datawareIds, String ruleId, String ruleLevel, Integer topN, String order) {
        List<QltyRuleMetrics> qltyRuleMetrics = qualitySummaryRepository.queryQltyRuleMetricsTopN(datawareIds, ruleId, ruleLevel, topN, order);
        ArrayList<QualitySummaryDatatableScoreTopsQueryVO> datatableScoreTopsQueryVOS = new ArrayList<QualitySummaryDatatableScoreTopsQueryVO>();
        for (QltyRuleMetrics qltyRuleMetric : qltyRuleMetrics) {
            qltyRuleMetric.setMetric_value(floadValueFormat.format(Double.valueOf(qltyRuleMetric.getMetric_value())));
            QualitySummaryDatatableScoreTopsQueryVO datatableScoreTopsQueryVO = new QualitySummaryDatatableScoreTopsQueryVO();
            datatableScoreTopsQueryVO.setDatawareId(qltyRuleMetric.getDataware_id());
            datatableScoreTopsQueryVO.setDatawareName(qltyRuleMetric.getDataware_name());
            datatableScoreTopsQueryVO.setDatatableId(qltyRuleMetric.getDatatable_id());
            datatableScoreTopsQueryVO.setDatatableName(qltyRuleMetric.getDatatable_name());
            datatableScoreTopsQueryVO.setScore(qltyRuleMetric.getMetric_value());
            datatableScoreTopsQueryVOS.add(datatableScoreTopsQueryVO);
        }
        return datatableScoreTopsQueryVOS;
    }


    public QualitySummaryRuleStateCountVO ruleStateCount(String[] datawareIds, String beginDate, String endDate) {
        return qualitySummaryRepository.ruleStateCount(datawareIds, beginDate, endDate);
    }

    public QualitySummaryRuleSchedulerCountVO ruleScheduleCount(String[] datawareIds, String beginDate, String endDate) {


        List<QltyRuleScheduler> qltyRuleSchedulers = qualitySummaryRepository.queryDatatableScheduler(datawareIds);

        QualitySummaryRuleSchedulerCountVO qualitySummaryRuleSchedulerCountVO = new QualitySummaryRuleSchedulerCountVO();
        qualitySummaryRuleSchedulerCountVO.setDatatableScheduelrCounts(new ArrayList<QualitySummaryRuleSchedulerCountVO.DatatableScheduelrCount>());
        Integer allCount = 0;
        for (QltyRuleScheduler qltyRuleScheduler : qltyRuleSchedulers) {

            int logCount = xbdpQltyController.countJobLogSizeByJobId(String.valueOf(qltyRuleScheduler.getDispatch_id()), beginDate, endDate);
            allCount += logCount;

            QualitySummaryRuleSchedulerCountVO.DatatableScheduelrCount datatableScheduelrCount = new QualitySummaryRuleSchedulerCountVO.DatatableScheduelrCount();
            datatableScheduelrCount.setDatawareId(qltyRuleScheduler.getDataware_id());
            datatableScheduelrCount.setDatatableId(qltyRuleScheduler.getDatatable_id());
            datatableScheduelrCount.setScheduleCount(logCount);
            qualitySummaryRuleSchedulerCountVO.getDatatableScheduelrCounts().add(datatableScheduelrCount);

        }
        qualitySummaryRuleSchedulerCountVO.setTotalScheduleCount(allCount);

        return qualitySummaryRuleSchedulerCountVO;
    }
}
