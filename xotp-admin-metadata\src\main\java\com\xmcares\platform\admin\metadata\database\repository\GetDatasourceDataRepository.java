package com.xmcares.platform.admin.metadata.database.repository;

import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import com.xmcares.platform.admin.metadata.database.service.DatasourceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/9/2 09:33
 */
@Repository
public class GetDatasourceDataRepository {

    @Autowired
    DatasourceService datasourceService;

    //@Autowired
    //ServiceModelService serviceModelService;

    private static final Logger LOG = LoggerFactory.getLogger(GetDatasourceDataRepository.class);
    private static final HashMap<String, Connection> cacheConnection = new HashMap<>();

    public List<Map<String, Object>> getData(String datasourceType, String datasourceOptions, String tableName, String sql) {
        HashMap<String, String> options = com.alibaba.fastjson.JSON.parseObject(datasourceOptions, HashMap.class);
        switch (datasourceType.toLowerCase()) {
            case "oracle":
            case "hive":
            case "mysql":
                return readJDBCData(datasourceType, options, sql);
            default:
                break;
        }
        return null;
    }

    public List<Map<String, Object>> readJDBCData(String datasourceType, HashMap<String, String> datasourceOptions, String sql) {
        switch (datasourceType.toLowerCase()) {
            case "oracle":
                datasourceOptions.put("driver", "oracle.jdbc.driver.OracleDriver");
                break;
            case "hive":
                datasourceOptions.put("driver", "org.apache.hive.jdbc.HiveDriver");
                break;
            case "mysql":
                datasourceOptions.put("driver", "com.mysql.cj.jdbc.Driver");
                break;
            default:
                break;
        }

        Connection con = null;
        try {
            Class.forName(datasourceOptions.get("driver"));
            String url = datasourceOptions.get("url");
            String username = datasourceOptions.get("username");
            String password = datasourceOptions.get("password");
            String connKey = url + username + password;
            con = cacheConnection.get(connKey);
            if (con == null || con.isClosed()) {
                con = DriverManager.getConnection(url, username, password);
                cacheConnection.put(connKey, con);
            }
            con = DriverManager.getConnection(datasourceOptions.get("url"), datasourceOptions.get("username"), datasourceOptions.get("password"));
            ResultSet resultSet = con.prepareStatement(sql).executeQuery();
            ArrayList<Map<String, Object>> resultList = new ArrayList<>();
            while (resultSet.next()) {
                HashMap<String, Object> resultLine = new HashMap<>();

                ResultSetMetaData metaData = resultSet.getMetaData();
                int columnCount = metaData.getColumnCount();
                for (int i = 0; i < columnCount; i++) {
                    String columnName = metaData.getColumnName(i + 1);
                    Object object = resultSet.getObject(i + 1);
                    resultLine.put(columnName, object);
                }

                resultList.add(resultLine);
            }
            return resultList;
        } catch (ClassNotFoundException | SQLException e) {
            LOG.warn(e.toString());
            return null;
        }
    }


    /**
     * 调用外部服务metadata的datasourceService
     *
     * @param datasourceId 数据源id
     * @return 数据源实体
     */
    public Datasource getDatasourceMeta(String datasourceId) {
        return datasourceService.getDatasource(datasourceId);
    }

}
