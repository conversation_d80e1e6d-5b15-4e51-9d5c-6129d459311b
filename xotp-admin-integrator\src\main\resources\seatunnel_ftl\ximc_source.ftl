{
  "plugin_name": "XIMC",
  "plugin_output": "${source.plugin_output!'default_source_output'}",
  "url": "${orginDatasource.serverHost}:${orginDatasource.serverPort}",
  "username": "${orgin.username}",
  "password": "${orgin.password}",
  "topic": "${orgin.topic}",
  "format": "${orgin.format!'json'}",
  "schema": {
    "fields": {
    <#-- schema字段从orginColumns动态生成 -->
    <#list orginColumns as column>
      "${column.title}": "${column.type}"<#if column_has_next>,</#if>
    </#list>
    }
  }
}
