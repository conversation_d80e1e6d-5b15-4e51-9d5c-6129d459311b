package com.xmcares.platform.admin.asset.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/27 10:11
 */
@ApiModel(value = MetaDataware.TABLE, description = "数据仓库")
public class MetaDataware {
    public static final String TABLE = "bdp_meta_dataware";

    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "库名")
    private String name;

    @ApiModelProperty(value = "资产编码：DW-开头")
    private String code;

    @ApiModelProperty(value = "库别名")
    private String alias;

    @ApiModelProperty(value = "类型：TAB、IMG、LOG......")
    private String type;

    @ApiModelProperty(value = "排序")
    private Integer sort_no;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "数据源类型（冗余）")
    private String datasource_type;

    @ApiModelProperty(value = "数据源ID")
    private String datasource_id;

    @ApiModelProperty(value = "数据源配置（冗余）")
    private String datasource_options;

    @ApiModelProperty(value = "是否删除：0:否；1:是")
    private String deleted;

    @ApiModelProperty(value = "create_user")
    private String create_user;

    @ApiModelProperty(value = "create_time")
    private Date create_time;

    @ApiModelProperty(value = "update_user")
    private String update_user;

    @ApiModelProperty(value = "update_time")
    private Date update_time;

    public static String getTABLE() {
        return TABLE;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getSort_no() {
        return sort_no;
    }

    public void setSort_no(Integer sort_no) {
        this.sort_no = sort_no;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDatasource_type() {
        return datasource_type;
    }

    public void setDatasource_type(String datasource_type) {
        this.datasource_type = datasource_type;
    }

    public String getDatasource_id() {
        return datasource_id;
    }

    public void setDatasource_id(String datasource_id) {
        this.datasource_id = datasource_id;
    }

    public String getDatasource_options() {
        return datasource_options;
    }

    public void setDatasource_options(String datasource_options) {
        this.datasource_options = datasource_options;
    }

    public String getDeleted() {
        return deleted;
    }

    public void setDeleted(String deleted) {
        this.deleted = deleted;
    }

    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_user() {
        return update_user;
    }

    public void setUpdate_user(String update_user) {
        this.update_user = update_user;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }
}
