package com.xmcares.platform.admin.lifecycle.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value = "LifecycleArchiveSchedulerAddInVO", description = "新增归档调度信息")
public class LifecycleArchiveSchedulerUpdateInVO implements Serializable {

    @ApiModelProperty(value = "调度ID")
    private String id;

    @ApiModelProperty(value = "调度名称")
    private String name;
    @ApiModelProperty(value = "调度任务表达式")
    private String cronExpr;
    @ApiModelProperty(value = "调度路由策略")
    private String routeStrategy;
    @ApiModelProperty(value = "调度阻塞策略")
    private String blockStrategy;
    @ApiModelProperty(value = "调度超时时间")
    private String executorTimeout;

    @ApiModelProperty(value = "调度执行重试次数")
    private String executorRetryCount;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCronExpr() {
        return cronExpr;
    }

    public void setCronExpr(String cronExpr) {
        this.cronExpr = cronExpr;
    }

    public String getRouteStrategy() {
        return routeStrategy;
    }

    public void setRouteStrategy(String routeStrategy) {
        this.routeStrategy = routeStrategy;
    }

    public String getBlockStrategy() {
        return blockStrategy;
    }

    public void setBlockStrategy(String blockStrategy) {
        this.blockStrategy = blockStrategy;
    }

    public String getExecutorTimeout() {
        return executorTimeout;
    }

    public void setExecutorTimeout(String executorTimeout) {
        this.executorTimeout = executorTimeout;
    }

    public String getExecutorRetryCount() {
        return executorRetryCount;
    }

    public void setExecutorRetryCount(String executorRetryCount) {
        this.executorRetryCount = executorRetryCount;
    }
}
