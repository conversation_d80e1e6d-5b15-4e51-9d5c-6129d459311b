package com.xmcares.platform.admin.lifecycle.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.error.BusinessException;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.asset.model.MetaDatatable;
import com.xmcares.platform.admin.lifecycle.model.AsstArchiveDatatable;
import com.xmcares.platform.admin.lifecycle.model.AsstArchiveResults;
import com.xmcares.platform.admin.lifecycle.model.AsstArchiveScheduler;
import com.xmcares.platform.admin.lifecycle.model.vo.*;
import com.xmcares.platform.admin.quality.model.XxlJobGroup;
import com.xmcares.platform.admin.quality.model.XxlJobInfo;
import com.xmcares.platform.admin.quality.model.XxlJobLog;
import com.xmcares.platform.admin.quality.repository.XbdpQltyController;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class ArchiveRepository {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
    @Resource
    XcfJdbcTemplate xcfJdbcTemplate;

    @Resource
    XbdpQltyController xbdpQltyController;

    private static final Logger LOG = LoggerFactory.getLogger(ArchiveRepository.class);

    public Page<LifecycleArchiveQueryVO> listQuery(
            String datawareId,
            String datatableId,
            String datatableReduced,
            Integer pageNo,
            Integer pageSize
    ) {

        try {

            List<LifecycleArchiveQueryVO> lifecycleArchiveQueryTempVOS = xcfJdbcTemplate.queryForEntities(
                    " select " +
                            " " + AsstArchiveScheduler.TABLE + ".name schedulerName , " +
                            " " + AsstArchiveScheduler.TABLE + ".id schedulerId , " +
                            " " + AsstArchiveScheduler.TABLE + ".started started , " +
                            " " + AsstArchiveScheduler.TABLE + ".cron_expr schedulerCronExpr , " +
                            " " + AsstArchiveScheduler.TABLE + ".route_strategy schedulerRouteStrategy , " +
                            " " + AsstArchiveScheduler.TABLE + ".block_strategy schedulerBlockStrategy , " +
                            " " + AsstArchiveScheduler.TABLE + ".executor_timeout schedulerExecutorTimeout , " +
                            " " + AsstArchiveScheduler.TABLE + ".executor_retry_count schedulerExecutorRetryCount , " +
                            " " + MetaDatatable.TABLE + ".remark remark , " +
                            " " + AsstArchiveDatatable.TABLE + ".update_time updateTime , " +
                            " " + AsstArchiveDatatable.TABLE + ".archive_type archiveType , " +
                            " " + AsstArchiveDatatable.TABLE + ".id id , " +
                            " " + AsstArchiveDatatable.TABLE + ".datatable_id datatableId , " +
                            " " + AsstArchiveDatatable.TABLE + ".archive_range_expr archiveRangeExpr , " +
                            " " + AsstArchiveDatatable.TABLE + ".dataware_id datawareId , " +
                            " " + AsstArchiveDatatable.TABLE + ".datatable_name datatableName , " +
                            " " + AsstArchiveDatatable.TABLE + ".datatable_reduced datatableReduced , " +
                            " " + AsstArchiveDatatable.TABLE + ".nearlined nearlined , " +
                            " " + AsstArchiveDatatable.TABLE + ".nearline_datasource_id nearlineDatasourceId , " +
                            " " + AsstArchiveDatatable.TABLE + ".nearline_datasource_name nearlineDatasourceName , " +
                            " " + AsstArchiveDatatable.TABLE + ".nearline_datatable_name nearlineDatatableName , " +
                            " " + AsstArchiveDatatable.TABLE + ".offlined offlined , " +
                            " " + AsstArchiveDatatable.TABLE + ".offline_file_name offlineFileName , " +
                            " " + AsstArchiveDatatable.TABLE + ".offline_file_lifedays offlineFileLifedays  " +
                            " from  " + AsstArchiveScheduler.TABLE + " " +
                            " left join " + AsstArchiveDatatable.TABLE + " on " + AsstArchiveDatatable.TABLE + ".archive_scheduler_id=" + AsstArchiveScheduler.TABLE + ".id " +
                            " left join " + MetaDatatable.TABLE + " on " + AsstArchiveDatatable.TABLE + ".datatable_id=" + MetaDatatable.TABLE + ".id " +
                            " where true " +
                            ((datawareId != null) ? (" and " + AsstArchiveDatatable.TABLE + ".dataware_id='" + datawareId + "' ") : "") +
                            ((datatableId != null) ? (" and " + AsstArchiveDatatable.TABLE + ".datatable_id='" + datatableId + "'") : "") +
                            ((datatableReduced != null) ? (" and " + AsstArchiveDatatable.TABLE + ".datatable_reduced='" + datatableReduced + "'") : "") +
                            " limit " + (pageNo * pageSize) + " , " + pageSize
                    ,
                    new Object[]{},
                    LifecycleArchiveQueryVO.class
            );

            Integer total = xcfJdbcTemplate.queryForObject(
                    " select " +
                            " IFNULL(( " +
                            " select " +
                            " count(*) total " +
                            " from  " + AsstArchiveScheduler.TABLE + " " +
                            " left join " + AsstArchiveDatatable.TABLE + " on " + AsstArchiveDatatable.TABLE + ".archive_scheduler_id=" + AsstArchiveScheduler.TABLE + ".id " +
                            " left join " + MetaDatatable.TABLE + " on " + AsstArchiveDatatable.TABLE + ".datatable_id=" + MetaDatatable.TABLE + ".id " +
                            " where true " +
                            ((datawareId != null) ? (" and " + AsstArchiveDatatable.TABLE + ".dataware_id='" + datawareId + "' ") : "") +
                            ((datatableId != null) ? (" and " + AsstArchiveDatatable.TABLE + ".datatable_id='" + datatableId + "'") : "") +
                            ((datatableReduced != null) ? (" and " + AsstArchiveDatatable.TABLE + ".datatable_reduced='" + datatableReduced + "'") : "") +
                            " ),0 ) total"
                    ,
                    new Object[]{},
                    Integer.class
            );

            ArrayList<LifecycleArchiveQueryVO> lifecycleArchiveQueryResultVOS = new ArrayList<>();
            lifecycleArchiveQueryResultVOS.addAll(lifecycleArchiveQueryTempVOS);
            Page<LifecycleArchiveQueryVO> lifecycleArchiveQueryVOPage = new Page<>();
            lifecycleArchiveQueryVOPage.setData(lifecycleArchiveQueryResultVOS);
            lifecycleArchiveQueryVOPage.setPageNo(pageNo);
            lifecycleArchiveQueryVOPage.setPageSize(pageSize);
            lifecycleArchiveQueryVOPage.setTotal(total);

            return lifecycleArchiveQueryVOPage;
        } catch (Exception e) {
            LOG.warn("listQuery", e);
            throw new BusinessException("listQuery", e);
        }
    }


    public LifecycleArchiveGetVO get(
            String id
    ) {
        try {
            LifecycleArchiveGetVO lifecycleArchiveGetVO = xcfJdbcTemplate.queryForEntity(
                    " select " +
                            " " + AsstArchiveScheduler.TABLE + ".name schedulerName , " +
                            " " + AsstArchiveScheduler.TABLE + ".id schedulerId , " +
                            " " + AsstArchiveScheduler.TABLE + ".cron_expr schedulerCronExpr , " +
                            " " + AsstArchiveScheduler.TABLE + ".route_strategy schedulerRouteStrategy , " +
                            " " + AsstArchiveScheduler.TABLE + ".block_strategy schedulerBlockStrategy , " +
                            " " + AsstArchiveScheduler.TABLE + ".executor_timeout schedulerExecutorTimeout , " +
                            " " + AsstArchiveScheduler.TABLE + ".executor_retry_count schedulerExecutorRetryCount , " +
                            " " + AsstArchiveDatatable.TABLE + ".id id , " +
                            " " + AsstArchiveDatatable.TABLE + ".datatable_id datatableId , " +
                            " " + AsstArchiveDatatable.TABLE + ".dataware_id datawareId , " +
                            " " + AsstArchiveDatatable.TABLE + ".datatable_name datatableName , " +
                            " " + AsstArchiveDatatable.TABLE + ".archive_type archiveType , " +
                            " " + AsstArchiveDatatable.TABLE + ".datatable_reduced datatableReduced , " +
                            " " + AsstArchiveDatatable.TABLE + ".nearlined nearlined , " +
                            " " + AsstArchiveDatatable.TABLE + ".nearline_datasource_id nearlineDatasourceId , " +
                            " " + AsstArchiveDatatable.TABLE + ".nearline_datasource_name nearlineDatasourceName , " +
                            " " + AsstArchiveDatatable.TABLE + ".nearline_datatable_name nearlineDatatableName , " +
                            " " + AsstArchiveDatatable.TABLE + ".offlined offlined , " +
                            " " + AsstArchiveDatatable.TABLE + ".offline_file_name offlineFileName , " +
                            " " + AsstArchiveDatatable.TABLE + ".offline_file_lifedays offlineFileLifedays ,  " +
                            " " + AsstArchiveDatatable.TABLE + ".archive_range_expr archiveRangeExpr  " +
                            " from  " + AsstArchiveDatatable.TABLE + " " +
                            " left join " + AsstArchiveScheduler.TABLE + " on " + AsstArchiveDatatable.TABLE + ".archive_scheduler_id=" + AsstArchiveScheduler.TABLE + ".id " +
                            " where " +
                            " id='" + id + "' "
                    ,
                    new Object[]{},
                    LifecycleArchiveGetVO.class
            );
            return lifecycleArchiveGetVO;
        } catch (Exception e) {
            LOG.warn("get", e);
            throw new BusinessException("get", e);
        }
    }

    @Transactional
    public String schedulerAdd(
            LifecycleArchiveSchedulerAddInVO schedulerAddInVO
    ) {
        try {
            AsstArchiveScheduler asstArchiveScheduler = new AsstArchiveScheduler();

            asstArchiveScheduler.setId(SnowflakeGenerator.getNextId().toString());
            //asstArchiveScheduler.setArchive_datatable_id(schedulerAddInVO.getArchiveDatatableId());
            asstArchiveScheduler.setName(schedulerAddInVO.getName());
            asstArchiveScheduler.setCron_expr(schedulerAddInVO.getCronExpr());
            String routeStrategy = schedulerAddInVO.getRouteStrategy();
            asstArchiveScheduler.setRoute_strategy(routeStrategy == null ? "FIRST" : routeStrategy);
            String blockStrategy = schedulerAddInVO.getBlockStrategy();
            asstArchiveScheduler.setBlock_strategy(blockStrategy == null ? "SERIAL_EXECUTION" : blockStrategy);
            asstArchiveScheduler.setExecutor_timeout(schedulerAddInVO.getExecutorTimeout() != null ? Integer.valueOf(schedulerAddInVO.getExecutorTimeout()) : 300);
            asstArchiveScheduler.setExecutor_retry_count(schedulerAddInVO.getExecutorRetryCount() != null ? Integer.valueOf(schedulerAddInVO.getExecutorRetryCount()) : 1);
            asstArchiveScheduler.setStarted("0");
            asstArchiveScheduler.setCreate_user("xbdp");
            Date nowTime = new Date();
            asstArchiveScheduler.setCreate_time(nowTime);

            XxlJobInfo xxlJobInfo = new XxlJobInfo();
            XxlJobGroup xbdp_archive_executor = xbdpQltyController.findByAppname("hookbox-service");

            JSONObject executorParam = JSON.parseObject("{}");
            //executorParam.put("archive_datatable_id", schedulerAddInVO.getArchiveDatatableId());
            executorParam.put("archive_scheduler_id", asstArchiveScheduler.getId());


            xxlJobInfo.setJobGroup(xbdp_archive_executor.getId());
            xxlJobInfo.setExecutorParam(executorParam.toJSONString());
            xxlJobInfo.setJobDesc("AsstArchiveScheduler" + ":" + asstArchiveScheduler.getName());
            xxlJobInfo.setAuthor(asstArchiveScheduler.getCreate_user());
            xxlJobInfo.setMisfireStrategy("DO_NOTHING");
            //xxlJobInfo.setAlarmEmail("<EMAIL>");
            xxlJobInfo.setScheduleType("CRON");
            xxlJobInfo.setScheduleConf(asstArchiveScheduler.getCron_expr());
            xxlJobInfo.setGlueType("BEAN");
            xxlJobInfo.setExecutorHandler("XbdpAsstArchiveJobHandler");
            String route_strategy = asstArchiveScheduler.getRoute_strategy();
            xxlJobInfo.setExecutorRouteStrategy(route_strategy == null ? "FIRST" : route_strategy);
            String block_strategy = asstArchiveScheduler.getBlock_strategy();
            xxlJobInfo.setExecutorBlockStrategy(block_strategy == null ? "SERIAL_EXECUTION" : block_strategy);
            xxlJobInfo.setExecutorTimeout(asstArchiveScheduler.getExecutor_timeout());
            xxlJobInfo.setExecutorFailRetryCount(asstArchiveScheduler.getExecutor_retry_count());
            xxlJobInfo.setGlueRemark("GLUE代码初始化");
            xxlJobInfo.setAddTime(nowTime);
            xxlJobInfo.setUpdateTime(nowTime);
            xxlJobInfo.setJobTaskType("ArchiveJob");
            xxlJobInfo.setAuthor("xbdp");

            int jobInfoId = xbdpQltyController.saveJobInfo(xxlJobInfo);
            asstArchiveScheduler.setDispatch_id(String.valueOf(jobInfoId));

            Map<String, Object> map = DBUtils.insertSqlAndObjects(asstArchiveScheduler, AsstArchiveScheduler.class, AsstArchiveScheduler.TABLE);
            String sql = DBUtils.getSql(map);
            Object[] args = DBUtils.getObjects(map);
            return String.valueOf(xcfJdbcTemplate.update(sql, args) == 1);
        } catch (Exception e) {
            LOG.warn("schedulerAdd", e);
            throw new BusinessException("schedulerAdd", e);
        }
    }

    @Transactional
    public String schedulerUpdate(
            LifecycleArchiveSchedulerUpdateInVO schedulerUpdateInVO
    ) {
        try {
            AsstArchiveScheduler asstArchiveSchedulerOld = xcfJdbcTemplate.queryForEntity(
                    " select " +
                            " * " +
                            " from  " + AsstArchiveScheduler.TABLE + " " +
                            " where " +
                            " id='" + schedulerUpdateInVO.getId() + "' "
                    ,
                    new Object[]{},
                    AsstArchiveScheduler.class
            );

            asstArchiveSchedulerOld.setName(schedulerUpdateInVO.getName());
            asstArchiveSchedulerOld.setCron_expr(schedulerUpdateInVO.getCronExpr());
            String routeStrategy = schedulerUpdateInVO.getRouteStrategy();
            asstArchiveSchedulerOld.setRoute_strategy(routeStrategy == null ? "FIRST" : routeStrategy);
            String blockStrategy = schedulerUpdateInVO.getBlockStrategy();
            asstArchiveSchedulerOld.setBlock_strategy(blockStrategy == null ? "SERIAL_EXECUTION" : blockStrategy);
            asstArchiveSchedulerOld.setExecutor_timeout(Integer.valueOf(schedulerUpdateInVO.getExecutorTimeout()));
            asstArchiveSchedulerOld.setExecutor_retry_count(Integer.valueOf(schedulerUpdateInVO.getExecutorRetryCount()));
            Date nowTime = new Date();
            asstArchiveSchedulerOld.setUpdate_user("xbdp");
            asstArchiveSchedulerOld.setUpdate_time(nowTime);

            XxlJobInfo xxlJobInfo = xbdpQltyController.loadById(Integer.valueOf(asstArchiveSchedulerOld.getDispatch_id()));

            JSONObject executorParam = JSON.parseObject("{}");
            //executorParam.put("archive_datatable_id", asstArchiveSchedulerOld.getArchive_datatable_id());
            executorParam.put("archive_scheduler_id", asstArchiveSchedulerOld.getId());

            xxlJobInfo.setExecutorParam(executorParam.toJSONString());
            xxlJobInfo.setJobDesc("AsstArchiveScheduler" + ":" + asstArchiveSchedulerOld.getName());
            xxlJobInfo.setAuthor(asstArchiveSchedulerOld.getCreate_user());
            xxlJobInfo.setScheduleType("CRON");
            xxlJobInfo.setScheduleConf(asstArchiveSchedulerOld.getCron_expr());
            xxlJobInfo.setMisfireStrategy("DO_NOTHING");
            String route_strategy = asstArchiveSchedulerOld.getRoute_strategy();
            xxlJobInfo.setExecutorRouteStrategy(route_strategy == null ? "FIRST" : route_strategy);
            String block_strategy = asstArchiveSchedulerOld.getBlock_strategy();
            xxlJobInfo.setExecutorBlockStrategy(block_strategy == null ? "SERIAL_EXECUTION" : block_strategy);
            xxlJobInfo.setExecutorTimeout(asstArchiveSchedulerOld.getExecutor_timeout());
            xxlJobInfo.setExecutorFailRetryCount(asstArchiveSchedulerOld.getExecutor_retry_count());
            xxlJobInfo.setGlueRemark("GLUE代码初始化");
            xxlJobInfo.setAddTime(nowTime);
            xxlJobInfo.setUpdateTime(nowTime);
            xxlJobInfo.setJobTaskType("ArchiveJob");
            xxlJobInfo.setAuthor("xbdp");
            xbdpQltyController.update(xxlJobInfo);

            Map<String, Object> maps = DBUtils.updateSqlAndObjects("id", asstArchiveSchedulerOld, AsstArchiveScheduler.class, AsstArchiveScheduler.TABLE);
            String sql = DBUtils.getSql(maps);
            Object[] objs = DBUtils.getObjects(maps);
            return String.valueOf(xcfJdbcTemplate.update(sql, objs) == 1);
        } catch (Exception e) {
            LOG.warn("schedulerUpdate", e);
            throw new BusinessException("schedulerUpdate", e);
        }
    }


    @Transactional
    public String schedulerDelete(
            String id
    ) {
        try {
            AsstArchiveScheduler asstArchiveSchedulerOld = xcfJdbcTemplate.queryForEntity(
                    " select " +
                            " * " +
                            " from  " + AsstArchiveScheduler.TABLE + " " +
                            " where " +
                            " id='" + id + "' "
                    ,
                    new Object[]{},
                    AsstArchiveScheduler.class
            );
            Long jobId = Long.valueOf(asstArchiveSchedulerOld.getDispatch_id());
            xbdpQltyController.pause(jobId.intValue());
            xbdpQltyController.delete(jobId);
            String sql = DBUtils.deleteSql(AsstArchiveScheduler.TABLE, "id");
            return String.valueOf(xcfJdbcTemplate.update(sql, id) == 1);
        } catch (Exception e) {
            LOG.warn("schedulerDelete", e);
            throw new BusinessException("schedulerDelete", e);
        }
    }


    @Transactional
    public String schedulerClose(
            String id
    ) {

        try {
            AsstArchiveScheduler asstArchiveSchedulerOld = xcfJdbcTemplate.queryForEntity(
                    " select " +
                            " * " +
                            " from  " + AsstArchiveScheduler.TABLE + " " +
                            " where " +
                            " id='" + id + "' "
                    ,
                    new Object[]{},
                    AsstArchiveScheduler.class
            );
            xcfJdbcTemplate.update(
                    " UPDATE " + AsstArchiveScheduler.TABLE + " " +
                            " set started='0' " +
                            " where id='" + asstArchiveSchedulerOld.getId() + "' "
            );
            Long jobId = Long.valueOf(asstArchiveSchedulerOld.getDispatch_id());
            xbdpQltyController.pause(jobId.intValue());
            return "true";
        } catch (Exception e) {
            LOG.warn("schedulerClose", e);
            throw new BusinessException("schedulerClose", e);
        }
    }


    @Transactional
    public String schedulerOpen(
            String id
    ) {
        try {
            AsstArchiveScheduler asstArchiveSchedulerOld = xcfJdbcTemplate.queryForEntity(
                    " select " +
                            " * " +
                            " from  " + AsstArchiveScheduler.TABLE + " " +
                            " where " +
                            " id='" + id + "' "
                    ,
                    new Object[]{},
                    AsstArchiveScheduler.class
            );
            xcfJdbcTemplate.update(
                    " UPDATE " + AsstArchiveScheduler.TABLE + " " +
                            " set started='1' " +
                            " where id='" + asstArchiveSchedulerOld.getId() + "' "
            );
            Long jobId = Long.valueOf(asstArchiveSchedulerOld.getDispatch_id());
            xbdpQltyController.start(jobId.intValue());
            return "true";
        } catch (Exception e) {
            LOG.warn("schedulerOpen", e);
            throw new BusinessException("schedulerOpen", e);
        }
    }


    public String add(
            LifecycleArchiveAddInVO lifecycleArchiveAddInVO
    ) {
        try {
            AsstArchiveDatatable asstArchiveDatatable = new AsstArchiveDatatable();
            asstArchiveDatatable.setId(SnowflakeGenerator.getNextId().toString());
            asstArchiveDatatable.setDatatable_id(lifecycleArchiveAddInVO.getDatatableId());
            asstArchiveDatatable.setDataware_id(lifecycleArchiveAddInVO.getDatawareId());
            asstArchiveDatatable.setDatatable_name(lifecycleArchiveAddInVO.getDatatableName());
            asstArchiveDatatable.setArchive_scheduler_id(lifecycleArchiveAddInVO.getArchiveSchedulerId());
            asstArchiveDatatable.setArchive_range_expr(lifecycleArchiveAddInVO.getArchiveRangeExpr());
            asstArchiveDatatable.setDatatable_reduced(lifecycleArchiveAddInVO.getDatatableReduced());
            asstArchiveDatatable.setNearlined(lifecycleArchiveAddInVO.getNearlined());
            asstArchiveDatatable.setNearline_datasource_id(lifecycleArchiveAddInVO.getNearlineDatasourceId());
            asstArchiveDatatable.setNearline_datasource_name(lifecycleArchiveAddInVO.getNearlineDatasourceName());
            asstArchiveDatatable.setNearline_datatable_name(lifecycleArchiveAddInVO.getNearlineDatatableName());
            asstArchiveDatatable.setOfflined(lifecycleArchiveAddInVO.getOfflined());
            asstArchiveDatatable.setOffline_file_name(lifecycleArchiveAddInVO.getOfflineFileName());
            asstArchiveDatatable.setOffline_file_lifedays(StringUtils.isNotBlank(lifecycleArchiveAddInVO.getOfflineFileLifedays()) ? Integer.valueOf(lifecycleArchiveAddInVO.getOfflineFileLifedays()) : null);
            asstArchiveDatatable.setCreate_user("xbdp");
            asstArchiveDatatable.setCreate_time(new Date());
            asstArchiveDatatable.setArchive_type(lifecycleArchiveAddInVO.getArchiveType());

            Map<String, Object> map = DBUtils.insertSqlAndObjects(asstArchiveDatatable, AsstArchiveDatatable.class, AsstArchiveDatatable.TABLE);
            String sql = DBUtils.getSql(map);
            Object[] args = DBUtils.getObjects(map);
            return String.valueOf(xcfJdbcTemplate.update(sql, args) == 1);
        } catch (Exception e) {
            LOG.warn("add", e);
            throw new BusinessException("add", e);
        }
    }

    public String update(
            LifecycleArchiveUpdateInVO lifecycleArchiveUpdateInVO
    ) {
        try {
            AsstArchiveDatatable asstArchiveDatatableOld = xcfJdbcTemplate.queryForEntity(
                    " select " +
                            " * " +
                            " from  " + AsstArchiveDatatable.TABLE + " " +
                            " where " +
                            " id='" + lifecycleArchiveUpdateInVO.getId() + "' "
                    ,
                    new Object[]{},
                    AsstArchiveDatatable.class
            );
            asstArchiveDatatableOld.setDatatable_id(lifecycleArchiveUpdateInVO.getDatatableId());
            asstArchiveDatatableOld.setDataware_id(lifecycleArchiveUpdateInVO.getDatawareId());
            asstArchiveDatatableOld.setDatatable_name(lifecycleArchiveUpdateInVO.getDatatableName());
            asstArchiveDatatableOld.setArchive_scheduler_id(lifecycleArchiveUpdateInVO.getArchiveSchedulerId());
            asstArchiveDatatableOld.setArchive_range_expr(lifecycleArchiveUpdateInVO.getArchiveRangeExpr());
            asstArchiveDatatableOld.setDatatable_reduced(lifecycleArchiveUpdateInVO.getDatatableReduced());
            asstArchiveDatatableOld.setNearlined(lifecycleArchiveUpdateInVO.getNearlined());
            asstArchiveDatatableOld.setNearline_datasource_id(lifecycleArchiveUpdateInVO.getNearlineDatasourceId());
            asstArchiveDatatableOld.setNearline_datasource_name(lifecycleArchiveUpdateInVO.getNearlineDatasourceName());
            asstArchiveDatatableOld.setNearline_datatable_name(lifecycleArchiveUpdateInVO.getNearlineDatatableName());
            asstArchiveDatatableOld.setOfflined(lifecycleArchiveUpdateInVO.getOfflined());
            asstArchiveDatatableOld.setOffline_file_name(lifecycleArchiveUpdateInVO.getOfflineFileName());
            asstArchiveDatatableOld.setOffline_file_lifedays(StringUtils.isNotBlank(lifecycleArchiveUpdateInVO.getOfflineFileLifedays()) ? Integer.valueOf(lifecycleArchiveUpdateInVO.getOfflineFileLifedays()) : null);
            asstArchiveDatatableOld.setUpdate_user("xbdp");
            asstArchiveDatatableOld.setUpdate_time(new Date());
            asstArchiveDatatableOld.setArchive_type(lifecycleArchiveUpdateInVO.getArchiveType());

            Map<String, Object> maps = DBUtils.updateSqlAndObjects("id", asstArchiveDatatableOld, AsstArchiveDatatable.class, AsstArchiveDatatable.TABLE);
            String sql = DBUtils.getSql(maps);
            Object[] objs = DBUtils.getObjects(maps);
            return String.valueOf(xcfJdbcTemplate.update(sql, objs) == 1);
        } catch (Exception e) {
            LOG.warn("update", e);
            throw new BusinessException("update", e);
        }
    }


    public String delete(
            String id
    ) {
        try {
            String sql = DBUtils.deleteSql(AsstArchiveDatatable.TABLE, "id");
            return String.valueOf(xcfJdbcTemplate.update(sql, id) == 1);
        } catch (Exception e) {
            LOG.warn("delete", e);
            throw new BusinessException("delete", e);
        }
    }


    public String start(
            String id,
            String archiveType
    ) {
        try {
            AsstArchiveDatatable asstArchiveDatatable = xcfJdbcTemplate.queryForEntity(
                    " select " +
                            " * " +
                            " from  " + AsstArchiveDatatable.TABLE + " " +
                            " where " +
                            " id='" + id + "' "
                    ,
                    new Object[]{},
                    AsstArchiveDatatable.class
            );
            if (archiveType.equals("nearlined")) {
                xcfJdbcTemplate.update(
                        " UPDATE " + AsstArchiveDatatable.TABLE + " " +
                                " set nearlined='1'  " +
                                " where id='" + asstArchiveDatatable.getId() + "' "
                );
            } else if (archiveType.equals("offlined")) {
                xcfJdbcTemplate.update(
                        " UPDATE " + AsstArchiveDatatable.TABLE + " " +
                                " set offlined='1' " +
                                " where id='" + asstArchiveDatatable.getId() + "' "
                );
            } else {
                throw new Exception("unknown archiveType:" + archiveType);
            }
            return "true";
        } catch (Exception e) {
            LOG.warn("start", e);
            throw new BusinessException("start", e);
        }
    }

    public String disable(
            String id,
            String archiveType
    ) {
        try {
            AsstArchiveDatatable asstArchiveDatatable = xcfJdbcTemplate.queryForEntity(
                    " select " +
                            " * " +
                            " from  " + AsstArchiveDatatable.TABLE + " " +
                            " where " +
                            " id='" + id + "' "
                    ,
                    new Object[]{},
                    AsstArchiveDatatable.class
            );
            if (archiveType.equals("nearlined")) {
                xcfJdbcTemplate.update(
                        " UPDATE " + AsstArchiveDatatable.TABLE + " " +
                                " set nearlined='0'  " +
                                " where id='" + asstArchiveDatatable.getId() + "' "
                );
            } else if (archiveType.equals("offlined")) {
                xcfJdbcTemplate.update(
                        " UPDATE " + AsstArchiveDatatable.TABLE + " " +
                                " set offlined='0' " +
                                " where id='" + asstArchiveDatatable.getId() + "' "
                );
            } else {
                throw new Exception("unknown archiveType:" + archiveType);
            }
            return "true";
        } catch (Exception e) {
            LOG.warn("disable", e);
            throw new BusinessException("disable", e);
        }
    }


    public Page<LifecycleArchiveRecordQueryVO> recordQuery(
            String datawareId,
            String datatableId,
            String archiveId,
            String beginTimer,
            String endTimer,
            Integer pageNo,
            Integer pageSize
    ) {

        try {
            List<LifecycleArchiveRecordQueryVO> lifecycleArchiveRecordQueryVOS = xcfJdbcTemplate.queryForEntities(
                    " select " +
                            " " + AsstArchiveScheduler.TABLE + ".name archiveSchedulerName , " +
                            " " + AsstArchiveScheduler.TABLE + ".id archiveSchedulerId , " +
                            " " + AsstArchiveScheduler.TABLE + ".dispatch_id dispatchId , " +
                            " " + AsstArchiveDatatable.TABLE + ".archive_type archiveType , " +
                            " " + AsstArchiveResults.TABLE + ".id id , " +
                            " " + AsstArchiveResults.TABLE + ".archive_datatable_id archiveDatatableId , " +
                            " " + AsstArchiveResults.TABLE + ".dataware_name datawareName , " +
                            " " + AsstArchiveResults.TABLE + ".datatable_name datatableName , " +
                            " " + AsstArchiveResults.TABLE + ".nearline_archive_data_count nearlineArchiveDataCount , " +
                            " " + AsstArchiveResults.TABLE + ".offline_archive_data_count offlineArchiveDataCount , " +
                            " " + AsstArchiveResults.TABLE + ".nearline_status nearlineStatus , " +
                            " " + AsstArchiveResults.TABLE + ".nearline_data_size nearlineDataSize , " +
                            " " + AsstArchiveResults.TABLE + ".nearline_datasource_name nearlineDatasourceName , " +
                            " " + AsstArchiveResults.TABLE + ".nearline_datatable_name nearlineDatatableName , " +
                            " " + AsstArchiveResults.TABLE + ".offline_status offlineStatus , " +
                            " " + AsstArchiveResults.TABLE + ".offline_file_size offlineFileSize , " +
                            " " + AsstArchiveResults.TABLE + ".offline_file_path offlineFilePath , " +
                            " " + AsstArchiveResults.TABLE + ".check_time archiveTime  " +
                            " from  " + AsstArchiveResults.TABLE + " " +
                            " left join " + AsstArchiveScheduler.TABLE + " on " + AsstArchiveResults.TABLE + ".archive_scheduler_id=" + AsstArchiveScheduler.TABLE + ".id " +
                            " left join " + AsstArchiveDatatable.TABLE + " on " + AsstArchiveResults.TABLE + ".archive_datatable_id=" + AsstArchiveDatatable.TABLE + ".id " +
                            " where true " +
                            ((datawareId != null) ? (" and " + AsstArchiveDatatable.TABLE + ".dataware_id='" + datawareId + "' ") : "  ") +
                            ((datatableId != null) ? (" and " + AsstArchiveDatatable.TABLE + ".datatable_id='" + datatableId + "'") : "  ") +
                            ((archiveId != null) ? (" and " + AsstArchiveResults.TABLE + ".archive_datatable_id='" + archiveId + "'") : "  ") +
                            " and " + AsstArchiveResults.TABLE + ".check_time>='" + beginTimer + "'" +
                            " and " + AsstArchiveResults.TABLE + ".check_time<='" + endTimer + "'" +
                            " limit " + (pageNo * pageSize) + " , " + pageSize
                    ,
                    new Object[]{},
                    LifecycleArchiveRecordQueryVO.class
            );

            List<String> archiveResultIds = lifecycleArchiveRecordQueryVOS.stream().map(b -> "\"".concat(b.getId()).concat("\"")).collect(Collectors.toList());
            List<String> dispatchIds = lifecycleArchiveRecordQueryVOS.stream().map(b -> b.getDispatchId()).collect(Collectors.toList());
            List<XxlJobLog> jobLogByArchiveResultIds = xbdpQltyController.findJobLogByArchiveResultIds(dispatchIds, archiveResultIds);

            if (jobLogByArchiveResultIds != null){
                HashMap<String, XxlJobLog> asstArchiveResultsIdsMap = jobLogByArchiveResultIds.stream().map(b -> {
                    HashMap<String, XxlJobLog> stringXxlJobLogHashMap = new HashMap<>();
                    String handleMsg = b.getHandleMsg();
                    if (handleMsg != null) {
                        JSONArray asstArchiveResultsIds = JSON.parseObject(handleMsg).getJSONArray("asstArchiveResultsIds");
                        if (asstArchiveResultsIds != null) {
                            asstArchiveResultsIds.stream().map(id -> String.valueOf(id)).forEach(idStr -> {
                                stringXxlJobLogHashMap.put(idStr, b);
                            });
                        }
                    }
                    return stringXxlJobLogHashMap;
                }).reduce(new HashMap<String, XxlJobLog>(), (m1, m2) -> {
                    m1.putAll(m2);
                    return m1;
                });

                for (LifecycleArchiveRecordQueryVO lifecycleArchiveRecordQueryVO : lifecycleArchiveRecordQueryVOS) {
                    XxlJobLog xxlJobLog = asstArchiveResultsIdsMap.get(lifecycleArchiveRecordQueryVO.getId());
                    if (xxlJobLog != null) {
                        lifecycleArchiveRecordQueryVO.setXxlLogId(String.valueOf(xxlJobLog.getId()));
                        lifecycleArchiveRecordQueryVO.setXxlSchedulerName(xxlJobLog.getJobName());
                        lifecycleArchiveRecordQueryVO.setXxlSchedulerAddr(xxlJobLog.getExecutorAddress());
                        lifecycleArchiveRecordQueryVO.setXxlSchedulerTriggerTime(xxlJobLog.getTriggerTime() != null ? sdf.format(xxlJobLog.getTriggerTime()) : null);
                    }
                }
            }


            Integer total = xcfJdbcTemplate.queryForObject(
                    " select " +
                            " IFNULL(( " +
                            " select " +
                            " count(*) total " +
                            " from  " + AsstArchiveResults.TABLE + " " +
                            " left join " + AsstArchiveScheduler.TABLE + " on " + AsstArchiveResults.TABLE + ".archive_scheduler_id=" + AsstArchiveScheduler.TABLE + ".id " +
                            " left join " + AsstArchiveDatatable.TABLE + " on " + AsstArchiveResults.TABLE + ".archive_datatable_id=" + AsstArchiveDatatable.TABLE + ".id " +
                            " where true " +
                            ((datawareId != null) ? (" and " + AsstArchiveDatatable.TABLE + ".dataware_id='" + datawareId + "' ") : "  ") +
                            ((datatableId != null) ? (" and " + AsstArchiveDatatable.TABLE + ".datatable_id='" + datatableId + "'") : "  ") +
                            ((archiveId != null) ? (" and " + AsstArchiveResults.TABLE + ".archive_datatable_id='" + archiveId + "'") : "  ") +
                            " and " + AsstArchiveResults.TABLE + ".check_time>='" + beginTimer + "'" +
                            " and " + AsstArchiveResults.TABLE + ".check_time<='" + endTimer + "'" +
                            " ),0 ) total"
                    ,
                    new Object[]{},
                    Integer.class
            );


            Page<LifecycleArchiveRecordQueryVO> lifecycleArchiveRecordQueryVOPage = new Page<>();
            lifecycleArchiveRecordQueryVOPage.setData(lifecycleArchiveRecordQueryVOS);
            lifecycleArchiveRecordQueryVOPage.setPageNo(pageNo);
            lifecycleArchiveRecordQueryVOPage.setPageSize(pageSize);
            lifecycleArchiveRecordQueryVOPage.setTotal(total);

            return lifecycleArchiveRecordQueryVOPage;
        } catch (Exception e) {
            LOG.warn("recordQuery", e);
            throw new BusinessException("recordQuery", e);
        }
    }


    public String recordDelete(
            String id
    ) {
        try {
            String sql = DBUtils.deleteSql(AsstArchiveResults.TABLE, "id");
            return String.valueOf(xcfJdbcTemplate.update(sql, id) == 1);
        } catch (Exception e) {
            LOG.warn("recordDelete", e);
            throw new BusinessException("recordDelete", e);
        }
    }


}
