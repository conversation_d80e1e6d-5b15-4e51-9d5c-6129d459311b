{
  "plugin_name": "Jdbc",
  <#-- 智能判断输入源，此部分逻辑不变 -->
  <#if fieldMappings?? && fieldMappings?has_content>
  "plugin_input": "${sink.plugin_input!'default_transform_output'}",
  <#else>
  "plugin_input": "${sink.plugin_input!'default_source_output'}",
  </#if>
   <#-- 这些是通用连接参数，两种模式下都需要 -->
  "url": "${destDatasource.url}",
  "driver": "${destDatasource.driver}",
  "user": "${destDatasource.username}",
  "password": "${destDatasource.password}",
  "batch_size": "${dest.batchSize!"1024"}",
  <#-- 核心判断逻辑：根据 generate_sink_sql 决定配置模式 -->
  <#if dest.generate_sink_sql?? && dest.generate_sink_sql>
  <#-- 模式一：自动生成 SQL -->
  "generate_sink_sql": ${dest.generate_sink_sql?string},
  "database": "${dest.database}",
  "table": "${dest.table}",
  <#else>
  <#-- 模式二：手动指定 query (旧逻辑) -->
  <#if fieldMappings?? && fieldMappings?has_content>
  "query": "INSERT INTO ${dest.table} (<#list fieldMappings as mapping>${mapping.sinkField}<#if mapping_has_next>,</#if></#list>) VALUES (<#list fieldMappings as mapping>?<#if mapping_has_next>,</#if></#list>)",
  <#else>
  "query": "INSERT INTO ${dest.table} (<#list destFieldNames as field>${field}<#if field_has_next>,</#if></#list>) VALUES (<#list destFieldNames as field>?<#if field_has_next>,</#if></#list>)",
  </#if>
  </#if>
  "enable_upsert": ${dest.enable_upsert?string}
}

<#--

如何工作
通用参数: url, driver, user, password, batch_size 这些连接相关的参数被移到了 if/else 判断逻辑的外部，因为无论哪种模式都需要它们，这样可以避免重复。

核心判断: <#if dest.generate_sink_sql?? && dest.generate_sink_sql>

这行代码会检查你的数据模型中是否传入了 dest.generate_sink_sql 并且其值为 true。

?? 用于安全地检查变量是否存在，避免因变量未定义而报错。

自动生成模式 (true):

如果判断为真，模板会输出 "database": "..." 和 "table": "..." 这两个属性。

SeaTunnel Connector 会利用这两个属性，结合上游传来的数据字段，在内部自动构建 INSERT 语句。这是最简单、最推荐的用法。

手动指定 Query 模式 (false 或未提供):

如果 dest.generate_sink_sql 为 false 或你根本没有提供这个变量，模板会执行 else 部分的逻辑。

这部分逻辑和我们之前的一样，会根据是否存在字段映射 (fieldMappings) 来手动拼接出一个完整的 query 字符串。

-->

<#--
如何使用
现在，你在调用模板引擎时，对于 JDBC Sink，可以有两种选择：

选择 1：自动生成 SQL (推荐)
在你的数据模型中，进行如下设置：

Java

// Java 示例
dest.put("generate_sink_sql", true);
dest.put("database", "my_app_db");
dest.put("table", "processed_orders");
// 无需提供 destFieldNames
模板会生成如下 config：

JSON

"config": {
  "url": "...",
  "driver": "...",
  "user": "...",
  "password": "...",
  "batch_size": 1024,
  "database": "my_app_db",
  "table": "processed_orders"
}
选择 2：手动指定 Query
在你的数据模型中，进行如下设置：

Java

// Java 示例
dest.put("generate_sink_sql", false); // 或者不设置此变量
dest.put("table", "processed_orders");
// 需要提供 destFieldNames 或 fieldMappings
destFieldNames.add("id");
destFieldNames.add("price");
模板会生成如下 config：

JSON

"config": {
  "url": "...",
  "driver": "...",
  "user": "...",
  "password": "...",
  "batch_size": 1024,
  "query": "INSERT INTO processed_orders (id,price) VALUES (?,?)"
}
通过这次更新，你的 jdbc_sink.ftl 模板变得更加强大和灵活，能够同时兼顾易用性和复杂场景的需求。
-->
