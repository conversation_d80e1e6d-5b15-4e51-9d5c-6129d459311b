package com.xmcares.platform.admin.metadata;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/7/20 15:43
 **/
@ConfigurationProperties(prefix = "xbdp.metadata")
public class MetadataProperties {
    /** 文件服务器的根目录 */
    private String fileServerDir = "/xbdp/metadata";

    /** 本地临时文件的根目录 */
    private String localTmpDir = System.getProperty("user.dir")+ "/tmp/metadata";


    public String getFileServerDir() {
        return fileServerDir;
    }

    public void setFileServerDir(String fileServerDir) {
        this.fileServerDir = fileServerDir;
    }

    public String getLocalTmpDir() {
        return localTmpDir;
    }

    public void setLocalTmpDir(String localTmpDir) {
        this.localTmpDir = localTmpDir;
    }



}
