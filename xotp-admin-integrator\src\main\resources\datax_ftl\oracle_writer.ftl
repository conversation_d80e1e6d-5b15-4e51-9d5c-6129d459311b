{
"name": "oraclewriter",
"parameter": {
"username": "${destDatasource.username}",
"password": "${destDatasource.password}",
"column": [
<#list destFieldNames! as field>
    "${field}"<#if field_has_next>,</#if>
</#list>
],
<#if dest.batchSize??>"batchSize":${dest.batchSize},</#if>
<#if dest.session??>
    "session": [
    <#list dest.session?split("#") as se>
        "${se}"<#if se_has_next>,</#if>
    </#list>
    ],
</#if>
<#if dest.preSql??>
    "preSql": [
    <#list dest.preSql?split("#") as pre>
        "${pre}"<#if pre_has_next>,</#if>
    </#list>
    ],
</#if>
<#if dest.postSql??>
    "postSql": [
    <#list dest.postSql?split("#") as post>
        "${post}"<#if post_has_next>,</#if>
    </#list>
    ],
</#if>
"connection": [
{
"jdbcUrl": "${destDatasource.url}",
"table": [
"${dest.table}"
]
}
]
}
}