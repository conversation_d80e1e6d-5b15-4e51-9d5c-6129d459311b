package com.xmcares.platform.admin.metadata.common.resource;

import com.xmcares.framework.fsclient.FSTemplate;
import com.xmcares.platform.admin.common.errors.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ResourceFactory {

    private static Logger LOG = LoggerFactory.getLogger(ResourceFactory.class);

    public static <P> IResourceManager<P> create(RMEnum rm, IResourceHolder holder, P params) {
        try {
            IResourceManager<P> rmi = rm.clazz.newInstance();
            rmi.init(holder, params);
            return rmi;
        } catch (InstantiationException | IllegalAccessException e) {
            LOG.error("初始化资源管理器异常", e);
            throw  new BusinessException("创建资源管理器失败！");
        }
    }

    public static IResourceHolder defHolder(IErrorHandler handler, FSTemplate template) {
        return new DefResourceHolder(handler, template);
    }

}
