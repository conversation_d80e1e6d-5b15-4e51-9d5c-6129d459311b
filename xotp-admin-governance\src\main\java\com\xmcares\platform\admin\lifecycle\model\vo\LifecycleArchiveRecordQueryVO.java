package com.xmcares.platform.admin.lifecycle.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value = "LifecycleArchiveRecordQueryVO", description = "归档记录查询")
public class LifecycleArchiveRecordQueryVO implements Serializable {

    @ApiModelProperty(value = "调度名称")
    private String archiveSchedulerName;
    @ApiModelProperty(value = "调度ID")
    private String archiveSchedulerId;
    @ApiModelProperty(value = "jobID")
    private String dispatchId;

    @ApiModelProperty(value = "调度logid")
    private String xxlLogId;
    @ApiModelProperty(value = "调度名称")
    private String xxlSchedulerName;

    @ApiModelProperty(value = "调度地址")
    private String xxlSchedulerAddr;

    @ApiModelProperty(value = "调度触发时间")
    private String xxlSchedulerTriggerTime;

    @ApiModelProperty(value = "归档类型")
    private String archiveType;
    @ApiModelProperty(value = "archiveResultId")
    private String id;
    @ApiModelProperty(value = "归档信息ID")
    private String archiveDatatableId;
    @ApiModelProperty(value = "仓库名称")
    private String datawareName;
    @ApiModelProperty(value = "表名称")
    private String datatableName;
    @ApiModelProperty(value = "近线归档数据量")
    private String nearlineArchiveDataCount;
    @ApiModelProperty(value = "离线归档数据量")
    private String offlineArchiveDataCount;
    @ApiModelProperty(value = "近线执行状态")
    private String nearlineStatus;
    @ApiModelProperty(value = "/近线数据大小")
    private String nearlineDataSize;
    @ApiModelProperty(value = "近线归档数据源名称")
    private String nearlineDatasourceName;
    @ApiModelProperty(value = "近线归档数据表名称")
    private String nearlineDatatableName;
    @ApiModelProperty(value = "离线执行状态")
    private String offlineStatus;
    @ApiModelProperty(value = "离线归档文件大小")
    private String offlineFileSize;
    @ApiModelProperty(value = "离线归档文件位置")
    private String offlineFilePath;
    @ApiModelProperty(value = "归档执行时间")
    private String archiveTime;


    public String getArchiveSchedulerName() {
        return archiveSchedulerName;
    }

    public void setArchiveSchedulerName(String archiveSchedulerName) {
        this.archiveSchedulerName = archiveSchedulerName;
    }

    public String getArchiveSchedulerId() {
        return archiveSchedulerId;
    }

    public void setArchiveSchedulerId(String archiveSchedulerId) {
        this.archiveSchedulerId = archiveSchedulerId;
    }

    public String getArchiveDatatableId() {
        return archiveDatatableId;
    }

    public void setArchiveDatatableId(String archiveDatatableId) {
        this.archiveDatatableId = archiveDatatableId;
    }

    public String getDatawareName() {
        return datawareName;
    }

    public void setDatawareName(String datawareName) {
        this.datawareName = datawareName;
    }

    public String getDatatableName() {
        return datatableName;
    }

    public void setDatatableName(String datatableName) {
        this.datatableName = datatableName;
    }

    public String getNearlineArchiveDataCount() {
        return nearlineArchiveDataCount;
    }

    public void setNearlineArchiveDataCount(String nearlineArchiveDataCount) {
        this.nearlineArchiveDataCount = nearlineArchiveDataCount;
    }

    public String getOfflineArchiveDataCount() {
        return offlineArchiveDataCount;
    }

    public void setOfflineArchiveDataCount(String offlineArchiveDataCount) {
        this.offlineArchiveDataCount = offlineArchiveDataCount;
    }

    public String getNearlineStatus() {
        return nearlineStatus;
    }

    public void setNearlineStatus(String nearlineStatus) {
        this.nearlineStatus = nearlineStatus;
    }

    public String getNearlineDataSize() {
        return nearlineDataSize;
    }

    public void setNearlineDataSize(String nearlineDataSize) {
        this.nearlineDataSize = nearlineDataSize;
    }

    public String getNearlineDatasourceName() {
        return nearlineDatasourceName;
    }

    public void setNearlineDatasourceName(String nearlineDatasourceName) {
        this.nearlineDatasourceName = nearlineDatasourceName;
    }

    public String getNearlineDatatableName() {
        return nearlineDatatableName;
    }

    public void setNearlineDatatableName(String nearlineDatatableName) {
        this.nearlineDatatableName = nearlineDatatableName;
    }

    public String getOfflineStatus() {
        return offlineStatus;
    }

    public void setOfflineStatus(String offlineStatus) {
        this.offlineStatus = offlineStatus;
    }

    public String getOfflineFileSize() {
        return offlineFileSize;
    }

    public void setOfflineFileSize(String offlineFileSize) {
        this.offlineFileSize = offlineFileSize;
    }

    public String getOfflineFilePath() {
        return offlineFilePath;
    }

    public void setOfflineFilePath(String offlineFilePath) {
        this.offlineFilePath = offlineFilePath;
    }

    public String getArchiveTime() {
        return archiveTime;
    }

    public void setArchiveTime(String archiveTime) {
        this.archiveTime = archiveTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getArchiveType() {
        return archiveType;
    }

    public void setArchiveType(String archiveType) {
        this.archiveType = archiveType;
    }

    public String getXxlSchedulerName() {
        return xxlSchedulerName;
    }

    public void setXxlSchedulerName(String xxlSchedulerName) {
        this.xxlSchedulerName = xxlSchedulerName;
    }

    public String getXxlSchedulerAddr() {
        return xxlSchedulerAddr;
    }

    public void setXxlSchedulerAddr(String xxlSchedulerAddr) {
        this.xxlSchedulerAddr = xxlSchedulerAddr;
    }

    public String getXxlSchedulerTriggerTime() {
        return xxlSchedulerTriggerTime;
    }

    public void setXxlSchedulerTriggerTime(String xxlSchedulerTriggerTime) {
        this.xxlSchedulerTriggerTime = xxlSchedulerTriggerTime;
    }

    public String getDispatchId() {
        return dispatchId;
    }

    public void setDispatchId(String dispatchId) {
        this.dispatchId = dispatchId;
    }

    public String getXxlLogId() {
        return xxlLogId;
    }

    public void setXxlLogId(String xxlLogId) {
        this.xxlLogId = xxlLogId;
    }
}
