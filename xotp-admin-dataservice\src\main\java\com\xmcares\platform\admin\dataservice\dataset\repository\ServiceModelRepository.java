package com.xmcares.platform.admin.dataservice.dataset.repository;

import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.dataservice.dataset.vo.*;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/30 10:16
 */
@Repository
public class ServiceModelRepository {

    private static final Logger LOG = LoggerFactory.getLogger(ServiceModelRepository.class);

    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;
    public static final String TABLE_BDP_META_DATASET = "bdp_api_dataset";

    public static final String TABLE_BDP_META_DATATABLE = "bdp_meta_datatable";
    public static final String TABLE_BDP_META_DATATABLE_COLUMN = "bdp_meta_datatable_column";
    public static final String TABLE_BDP_META_DATASOURCE = "bdp_meta_datasource";


    public CreateOrCopyDataModelReturnVO createDataModelLessInfo(String datasetId, String datasourceId, String datasetName, String datasetCode, String datasetSql, String datasetParameter, String datasetMode) {
        CreateOrCopyDataModelReturnVO data = new CreateOrCopyDataModelReturnVO();
        int update = 0;
        try {
            if (datasetId == null) {
                if (nameUniqueCheck(datasetCode) == 0) {
                    Date nowDate = new Date();
                    datasetId = SnowflakeGenerator.getNextId().toString();
                    update = xcfJdbcTemplate.update(
                            "INSERT INTO " + TABLE_BDP_META_DATASET + "(id, create_time, update_time, deleted, datasource_id, dataset_name, dataset_code, dataset_published, dataset_sql, dataset_parameter, dataset_mode) VALUES (?,?,?,?,?,?,?,?,?,?,?)",
                            datasetId,
                            nowDate,
                            nowDate,
                            "0",
                            datasourceId,
                            datasetName,
                            datasetCode,
                            0,
                            datasetSql == null ? "" : datasetSql,
                            datasetParameter == null ? "" : datasetParameter,
                            datasetMode == null ? "" : datasetMode
                    );
                } else {
                    throw new Exception("datasetCode 不唯一");
                }
            } else {
                if (checkPublish(datasetId, "1") == 1) {
                    throw new Exception("服务已发布不允许修改");
                } else {
                    MetaDatasetVO datasetById = getDatasetById(datasetId);
                    if (datasetById != null) {
                        update = xcfJdbcTemplate.update(
                                "UPDATE " + TABLE_BDP_META_DATASET + " SET dataset_name = ?, datasource_id = ?, dataset_sql=?, dataset_parameter=?, dataset_mode=? WHERE id = ?",
                                datasetName, datasourceId, datasetSql == null ? datasetById.getDatasetSql() : datasetSql,
                                datasetParameter == null ? datasetById.getDatasetParameter() : datasetParameter,
                                datasetMode == null ? datasetById.getDatasetMode() : datasetMode, datasetId
                        );
                    } else {
                        throw new Exception("找不到对应的dataset");
                    }
                }
            }
            if (update != 1) {
                throw new Exception("dataset信息处理失败");
            } else {
                data.setDatasetId(datasetId);
            }
        } catch (Exception e) {
            data = null;
            LOG.warn(e.toString());
        }
        return data;
    }

    public Integer checkPublish(String datasetId, String datasetPublished) {
        try {
            return xcfJdbcTemplate.<Integer>queryForObject(
                    "SELECT " +
                            " COUNT(*) isPublish " +
                            " FROM " + TABLE_BDP_META_DATASET +
                            " WHERE id = ? AND dataset_published=?"
                    ,
                    Integer.class,
                    datasetId, datasetPublished
            );
        } catch (Exception e) {
            LOG.warn(e.toString());
            return -1;
        }
    }

    //创建数据模型
    public int createDataModel(MetaDatasetVO metaDataset, String datasetCode, String datasetName) {
        try {
            Date nowDate = new Date();
            return xcfJdbcTemplate.update(
                    " INSERT INTO " + TABLE_BDP_META_DATASET +
                            "( id, create_time, update_time, deleted, " +
                            "datasource_id, dataset_name, dataset_code, " +
                            "dataset_published, dataset_sql, dataset_parameter, dataset_mode) " +
                            " VALUES (?,?,?,?,?,?,?,?,?,?,?) ",
                    metaDataset.getId(),
                    nowDate,
                    nowDate,
                    String.valueOf(metaDataset.getDeleted()),
                    metaDataset.getDatasourceId(),
                    datasetName,
                    datasetCode,
                    "0",
                    metaDataset.getDatasetSql(),
                    metaDataset.getDatasetParameter(),
                    metaDataset.getDatasetMode()
            );
        } catch (Exception e) {
            LOG.warn(e.toString());
            return -1;
        }
    }

    //获取界面上需要的数据模型分页数据
    public List<DataModelPageQueryVO> dataModelPageQuery(Integer pageSize, Integer pageNo, String datasetName, String datasourceId, String datasetPublished) {
        List<DataModelPageQueryVO> dataModelPageQueryVO = null;
        String datasourceCondition = " bdp_meta_datasource.id = '" + datasourceId + "' ";
        if (datasourceId == null) {
            datasourceCondition = " bdp_meta_datasource.id like '%%%' ";
        }
        try {
            dataModelPageQueryVO = xcfJdbcTemplate.<DataModelPageQueryVO>queryForEntities("SELECT " +
                            " bdp_api_dataset.id dataset_id, " +
                            " bdp_api_dataset.dataset_code dataset_code, " +
                            " bdp_api_dataset.dataset_name dataset_name, " +
                            " bdp_api_dataset.datasource_id datasource_id, " +
                            " bdp_meta_datasource.`name` datasource_name, " +
                            " bdp_api_dataset.create_time create_time, " +
                            " bdp_api_dataset.dataset_sql dataset_sql, " +
                            " bdp_api_dataset.dataset_published dataset_published, " +
                            " bdp_api_dataset.dataset_parameter dataset_parameter, " +
                            " bdp_api_dataset.dataset_mode dataset_mode, " +
//                            " bdp_meta_datasource.`options`, " +
                            " bdp_meta_datasource.type datasource_type" +
                            " FROM " + TABLE_BDP_META_DATASET +
                            " LEFT JOIN bdp_meta_datasource ON bdp_api_dataset.datasource_id = bdp_meta_datasource.id " +
                            " WHERE bdp_api_dataset.dataset_name LIKE '%" + datasetName + "%' AND " + datasourceCondition  + " AND bdp_api_dataset.dataset_published LIKE  '%" + datasetPublished + "%' " +
                            " order by bdp_api_dataset.update_time desc " +
                            " limit " + ((pageNo - 1) * pageSize) + " , " + pageSize,
                    new Object[]{},
                    DataModelPageQueryVO.class

            );
        } catch (Exception e) {
            LOG.warn(e.toString());
        }
        return dataModelPageQueryVO;
    }

    public Integer dataModelTotalCount(String datasetName, String datasourceId, String datasetPublished) {
        String datasourceCondition = " bdp_meta_datasource.id = '" + datasourceId + "' ";
        if (datasourceId == null) {
            datasourceCondition = " bdp_meta_datasource.id like '%%%' ";
        }
        try {
            return xcfJdbcTemplate.<Integer>queryForObject(
                    "SELECT " +
                            " COUNT(*) total " +
                            " FROM " + TABLE_BDP_META_DATASET +
                            " LEFT JOIN bdp_meta_datasource ON bdp_api_dataset.datasource_id = bdp_meta_datasource.id " +
                            " WHERE bdp_api_dataset.dataset_name LIKE '%" + datasetName + "%' AND " + datasourceCondition + " AND bdp_api_dataset.dataset_published LIKE  '%" + datasetPublished + "%' "
                    ,
                    Integer.class
            );
        } catch (Exception e) {
            LOG.warn(e.toString());
            return -1;
        }
    }

    //删除数据模型
    public int deleteDatasetModel(String datasetId) {
        try {
            return xcfJdbcTemplate.update("DELETE FROM " + TABLE_BDP_META_DATASET + " WHERE id = ?", datasetId);
        } catch (Exception e) {
            LOG.warn(e.toString());
            return -1;
        }
    }

    //获取数据模型
    public MetaDatasetVO getDatasetById(String datasetId) {
        MetaDatasetVO metaDatasetVO = null;
        try {
            metaDatasetVO = xcfJdbcTemplate.<MetaDatasetVO>queryForEntity(
                    " SELECT " +
                            " bdp_api_dataset.id, " +
                            " bdp_api_dataset.create_time, " +
                            " bdp_api_dataset.update_time, " +
                            " bdp_api_dataset.deleted, " +
                            " bdp_api_dataset.datasource_id, " +
                            " bdp_api_dataset.dataset_name, " +
                            " bdp_api_dataset.dataset_code, " +
                            " bdp_api_dataset.dataset_sql, " +
                            " bdp_api_dataset.dataset_parameter, " +
                            " bdp_api_dataset.dataset_published, " +
                            " bdp_api_dataset.service_id, " +
                            " bdp_api_dataset.dataset_mode, " +
                            " bdp_meta_datasource.`name` datasource_name, " +
                            " bdp_meta_datasource.type datasource_type " +
                            " FROM " +
                            TABLE_BDP_META_DATASET +
                            " LEFT JOIN " + TABLE_BDP_META_DATASOURCE + " ON " + TABLE_BDP_META_DATASET + ".datasource_id = " + TABLE_BDP_META_DATASOURCE + ".id " +
                            " WHERE " + TABLE_BDP_META_DATASET + ".id = ?",
                    new Object[]{datasetId},
                    MetaDatasetVO.class
            );
        } catch (Exception e) {
            LOG.warn(e.toString());
        }
        return metaDatasetVO;
    }

    public Integer nameUniqueCheck(String datasetCode) {
        try {
            return xcfJdbcTemplate.<Integer>queryForObject(
                    "SELECT COUNT(*) FROM " + TABLE_BDP_META_DATASET + " where dataset_code = ?",
                    Integer.class,
                    datasetCode
            );
        } catch (Exception e) {
            LOG.warn(e.toString());
            return -1;
        }
    }

    @Transactional
    public PublishDatasetModelVO isPublishDatasetModel(String datasetId, String isPublish, String serviceId) {
        PublishDatasetModelVO data = new PublishDatasetModelVO();
        try {
            MetaDatasetVO metaDataset = getDatasetById(datasetId);
            if (metaDataset == null) {
                throw new Exception("无法根据datasetid查询到metaDataset");
            }
            if (
                    ((0 == metaDataset.getDatasetPublished()) && "0".equals(isPublish))
                            ||
                            ((1 == metaDataset.getDatasetPublished()) && "1".equals(isPublish))
                    ) {
                throw new Exception("发布状态重复");
            } else {
                if (metaDataset != null) {
                    if ("1".equals(isPublish)) {
                        data.setDatasetName(metaDataset.getDatasetName());
                        //TODO 此处固定写法之后根据需求可能要修改表获得根据不同服务得到的完整url
                        data.setServiceApi("/admin-service/developer/api-safe/" + metaDataset.getDatasetCode());
                        data.setServiceId(serviceId);
                    } else if ("0".equals(isPublish)) {
                        data.setServiceId(metaDataset.getServiceId());
                    }
                }

                boolean successed = xcfJdbcTemplate.update(
                        "UPDATE " + TABLE_BDP_META_DATASET + " SET dataset_published = ?, service_id = ? WHERE id = ? ",
                        Integer.valueOf(isPublish), serviceId, datasetId
                ) == 1;
                if (!successed) {
                    throw new Exception("更新dataset失败 id" + datasetId);
                }
            }
        } catch (Exception e) {
            data = null;
            LOG.warn(e.toString());
        }
        return data;
    }

    public List<DatatableVO> getDatatableById(String datasourceId) {
        List<DatatableVO> data = null;
        try {
            data = xcfJdbcTemplate.queryForEntities(
                    "SELECT id, `name`, alias, datasource_id, remark FROM " + TABLE_BDP_META_DATATABLE + " WHERE datasource_id = ?",
                    new Object[]{datasourceId},
                    DatatableVO.class
            );
        } catch (Exception e) {
            LOG.warn(e.toString());
        }
        return data;
    }

    public List<DatatableFieldVO> getDatatableFieldById(String datasourceId, String datatableId) {
        List<DatatableFieldVO> data = null;
        try {
            data = xcfJdbcTemplate.queryForEntities(
                    "SELECT id, datasource_id , datatable_id , " +
                            " datatable_name , alisa, `name`, `type`, " +
                            " `length`, accuracy, allow_empty , column_key , " +
                            " has_system , remark " +
                            " FROM " + TABLE_BDP_META_DATATABLE_COLUMN +
                            " WHERE datasource_id = ?  AND datatable_id = ?",
                    new Object[]{datasourceId, datatableId},
                    DatatableFieldVO.class
            );
        } catch (Exception e) {
            LOG.warn(e.toString());
        }
        return data;
    }

    public List<TableInfoVO> getTableInfo(String datasourceId, String datatableId) {
        List<TableInfoVO> tableInfoVOS = null;
        try {
            tableInfoVOS = xcfJdbcTemplate.<TableInfoVO>queryForEntities(
                    " SELECT " +
                            " bdp_meta_datasource.type datasource_type , " +
                            " bdp_meta_datasource.`options` datasource_options , " +
                            " bdp_meta_datatable.`name`  table_name " +
                            " FROM " +
                            " bdp_meta_datasource " +
                            " LEFT JOIN bdp_meta_datatable ON bdp_meta_datasource.id = bdp_meta_datatable.datasource_id  " +
                            " WHERE " +
                            " bdp_meta_datasource.id =? AND bdp_meta_datatable.id = ?",
                    new Object[]{datasourceId, datatableId},
                    TableInfoVO.class
            );
            return tableInfoVOS;
        } catch (Exception e) {
            LOG.warn(e.toString());
        }
        return tableInfoVOS;
    }

    public Datasource getDatasourceById(String datasourceId) {
        Datasource datasource = null;
        try {
            datasource = xcfJdbcTemplate.<Datasource>queryForEntity(
                    " SELECT " +
                            " * " +
                            " FROM " +
                            TABLE_BDP_META_DATASOURCE +
                            " WHERE " + TABLE_BDP_META_DATASOURCE + ".id = ?",
                    new Object[]{datasourceId},
                    Datasource.class
            );
        } catch (Exception e) {
            LOG.warn(e.toString());
        }
        return datasource;
    }

    public MetaDatasetVO getDatasetByDatasetCode(String datasetCode) {
        MetaDatasetVO metaDatasetVO = null;
        try {
            metaDatasetVO = xcfJdbcTemplate.<MetaDatasetVO>queryForEntity(
                    " SELECT " +
                            " bdp_api_dataset.id, " +
                            " bdp_api_dataset.create_time, " +
                            " bdp_api_dataset.update_time, " +
                            " bdp_api_dataset.deleted, " +
                            " bdp_api_dataset.datasource_id, " +
                            " bdp_api_dataset.dataset_name, " +
                            " bdp_api_dataset.dataset_code, " +
                            " bdp_api_dataset.dataset_sql, " +
                            " bdp_api_dataset.dataset_published, " +
                            " bdp_api_dataset.dataset_parameter, " +
                            " bdp_api_dataset.dataset_mode, " +
                            " bdp_api_dataset.service_id, " +
                            " bdp_meta_datasource.`name` datasource_name, " +
                            " bdp_meta_datasource.type datasource_type " +
                            " FROM " +
                            TABLE_BDP_META_DATASET +
                            " LEFT JOIN " + TABLE_BDP_META_DATASOURCE + " ON " + TABLE_BDP_META_DATASET + ".datasource_id = " + TABLE_BDP_META_DATASOURCE + ".id " +
                            " WHERE " + TABLE_BDP_META_DATASET + ".dataset_code = ?",
                    new Object[]{datasetCode},
                    MetaDatasetVO.class
            );
        } catch (Exception e) {
            LOG.warn(e.toString());
        }
        return metaDatasetVO;
    }
}
