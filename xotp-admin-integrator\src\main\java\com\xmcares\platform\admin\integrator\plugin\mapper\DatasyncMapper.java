package com.xmcares.platform.admin.integrator.plugin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xmcares.platform.admin.integrator.plugin.model.Datasync;
import com.xmcares.platform.admin.integrator.plugin.model.PluginResourceQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DatasyncMapper extends BaseMapper<Datasync> {

    List<PluginResourceQueryDTO> selectUsageByPluginIds(@Param("pluginIds") List<String> pluginIds);

}
