package com.xmcares.platform.admin.integrator.common.util;

/**
 * <AUTHOR> chenYG
 * @date : 2022/4/1 16:24
 */
public enum MyGlueTypeEnum {

    BEAN("BEAN"),
    GLUE_GROOVY("GLUE(Java)"),
    GLUE_SHELL("GLUE(Shell)"),
    GLUE_PYTHON("GLUE(Python)"),
    GLUE_PHP("GLUE(PHP)"),
    GLUE_NODEJS("GLUE(Nodejs)"),
    GLUE_POWERSHELL("GLUE(PowerShell)");

    private String desc;

    private MyGlueTypeEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return this.desc;
    }

}
