package com.xmcares.platform.admin.quality.service;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.platform.admin.quality.model.vo.QualityRuleMetricsListQueryVO;
import com.xmcares.platform.admin.quality.repository.QualityRuleMetricsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/25 09:10
 */
@Service
public class QualityRuleMetricsService {

    @Autowired
    QualityRuleMetricsRepository qualityRuleMetricsRepository;

    public Page<QualityRuleMetricsListQueryVO> listQuery(String datawareId, String datatableName, String ruleName, String[] ruleSchedulerTaskIds, Integer pageNo, Integer pageSize) {
        return qualityRuleMetricsRepository.listQuery(datawareId, datatableName, ruleName, ruleSchedulerTaskIds, pageNo, pageSize);
    }

}
