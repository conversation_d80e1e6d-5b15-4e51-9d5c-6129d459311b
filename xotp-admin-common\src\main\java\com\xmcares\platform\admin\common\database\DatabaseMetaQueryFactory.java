package com.xmcares.platform.admin.common.database;


import com.xmcares.platform.admin.common.database.metaquery.*;
import com.xmcares.platform.admin.common.datasource.DataSourceOptions;
import com.xmcares.platform.admin.common.datasource.jdbc.JdbcDataSource;
import com.xmcares.platform.admin.common.datasource.jdbc.JdbcDataSourceManager;
import com.xmcares.platform.admin.common.datasource.jdbc.JdbcType;
import com.xmcares.platform.admin.common.errors.SystemException;

/**
 * 工具类，获取单例实体
 *  改进工具 使用统一的数据源管理器{@link JdbcDataSourceManager}获取数据库元数据
 * <AUTHOR>
 * <AUTHOR>
 * @since 1.0
 * @since 2019/7/18 9:36
 */
public class DatabaseMetaQueryFactory {

    private final JdbcDataSourceManager jdbcDataSourceManager;

    public DatabaseMetaQueryFactory(JdbcDataSourceManager jdbcDataSourceManager) {
        if (jdbcDataSourceManager == null) {
            throw new IllegalArgumentException("参数DataSourceManager不可为空");
        }
        this.jdbcDataSourceManager = jdbcDataSourceManager;
    }

    public DatabaseMetaQuery getDatabaseMetaQuery(DataSourceOptions options) {
        JdbcDataSource dataSource = this.jdbcDataSourceManager.getOrCreateDataSource(options);
        JdbcType type = JdbcType.fromTypeName(options.getTypeName());
        if (type == null) {
            type = JdbcType.fromJdbcUrl(options.getUrl());
        }
        if (null == type) {
            throw new SystemException(String.format("不支持的数据库类型[%s > %s]", options.getTypeName(), options.getUrl()));
        }
        //  DM
        //  DORIS
        //  MYSQL
        //  ORACLE
        //  SQL_SERVER
        //HDFS
        //HBASE_1_1
        //HIVE_1_1
        //  HIVE
        switch(type) {
            case MYSQL:
                return new MySqlMetaQuery(dataSource);
            case DM:
                return new Dm8MetaQuery(dataSource);
            case ORACLE:
                return new OracleMetaQuery(dataSource);
            case SQL_SERVER:
                return new SqlServerMetaQuery(dataSource);
            case DORIS:
                return new DorisMetaQuery(dataSource);
            /*case HIVE:
                return new Hive2MetaQuery(dataSource);*/
            case OPEN_GAUSS:
                return new OpenGaussMetaQuery(dataSource);
            /*case PHOENIX:
                return new PhoenixMetaQuery(dataSource);*/
        }

        throw new SystemException(String.format("平台不支持[%s]类型数据库的元数据查询", options.getTypeName()));
    }

    public JdbcDataSourceManager getDataSourceManager() {
        return jdbcDataSourceManager;
    }

    public JdbcDataSource getDataSource(DataSourceOptions options) {
        return this.jdbcDataSourceManager.getOrCreateDataSource(options);
    }
}
