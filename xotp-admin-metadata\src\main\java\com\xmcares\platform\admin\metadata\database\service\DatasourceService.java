package com.xmcares.platform.admin.metadata.database.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.framework.fsclient.FSTemplate;
import com.xmcares.platform.admin.common.datasource.DataSource;
import com.xmcares.platform.admin.common.datasource.DataSourceGroup;
import com.xmcares.platform.admin.common.datasource.DataSourceType;
import com.xmcares.platform.admin.common.datasource.mq.MqType;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.errors.MetadataException;
import com.xmcares.platform.admin.common.errors.RdbmsException;
import com.xmcares.platform.admin.common.errors.SystemException;
import com.xmcares.platform.admin.common.datasource.DataSourceOptions;
import com.xmcares.platform.admin.common.database.DatabaseMetaQuery;
import com.xmcares.platform.admin.common.database.DatabaseMetaQueryFactory;
import com.xmcares.platform.admin.common.util.HashUtils;
import com.xmcares.platform.admin.metadata.common.resource.DefResourceHolder;
import com.xmcares.platform.admin.metadata.common.resource.IResourceManager;
import com.xmcares.platform.admin.metadata.common.resource.RMEnum;
import com.xmcares.platform.admin.metadata.common.resource.ResourceFactory;
import com.xmcares.platform.admin.metadata.database.model.Datasource;
import com.xmcares.platform.admin.metadata.database.model.DatasourceModel;
import com.xmcares.platform.admin.metadata.database.model.DatasourceResourceSyncTask;
import com.xmcares.platform.admin.metadata.database.repository.DatasourceModelRepository;
import com.xmcares.platform.admin.metadata.database.repository.DatasourceRepository;
import com.xmcares.platform.admin.metadata.database.repository.DatasourceResourceSyncTaskRepository;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 提供数据源的各种连接测试
 *
 * <AUTHOR>
 * @Description
 * @Date 2022/2/11 14:07
 */
@Component
public class DatasourceService {

    private static final Logger LOG = LoggerFactory.getLogger(DatasourceService.class);

    @Autowired
    private DatasourceRepository dsRepository;

    @Autowired
    private DatasourceModelRepository dsModelRepository;

    @Autowired
    private DatabaseMetaQueryFactory metaQueryFactory;

    @Autowired
    private FSTemplate fsTemplate;

    @Autowired
    private DatasourceResourceSyncTaskRepository datasourceResourceSyncTaskRepository;

    //展示所有数据源
    public List<Datasource> listDatasource(Datasource datasource) {
        List<Datasource> result = dsRepository.queryList(datasource);
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        result.forEach(ds -> {
            ds.setOptions(null);
            ds.setCreateTime(null);
            ds.setUpdateTime(null);
            ds.setCreateUser(null);
            ds.setRemark(null);
        });
        return result;
    }

    //根據ID列表獲取數據源信息
    public List<Datasource> listDatasource(List<String> ids) {
        List<Datasource> result = dsRepository.queryList(ids);
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        return result;
    }

    //分页查询
    public Page<Datasource> pageListDatasource(Datasource datasource, Pagination pagination) {
        List<Datasource> datasources = dsRepository.queryPage(datasource, new Page<>(pagination.getPageNo() - 1, pagination.getPageSize()));
        int total = dsRepository.count(datasource);
        Page<Datasource> page = new Page<>();
        page.setData(datasources);
        page.setTotal(total);
        page.setPageNo(pagination.getPageNo());
        page.setPageSize(pagination.getPageSize());
        return page;
    }

    // 测试数据源
    public Boolean testDatasource2(Datasource datasource) {
        DataSourceOptions sourceOptions = JSON.parseObject(datasource.getOptions(), DataSourceOptions.class);
        DataSourceGroup sourceGroup = DataSourceGroup.groupOf(datasource.getCategory());
        if (sourceGroup == null) {
            throw new BusinessException(String.format("不支持的数据源分组[%s]", datasource.getCategory()));
        }
        DataSourceType<?> sourceType = sourceGroup.itsTypeOf(datasource.getType());
        if (sourceType == null) {
            throw new BusinessException(String.format("不支持的[%s]数据源类型[%s]", datasource.getType(), datasource.getType()));
        }
        DataSource.AvailableStatus status;
        try (DataSource dataSource = sourceType.createDataSource(sourceOptions)) {
            status = dataSource.testAvailable();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        if (status != null && !status.isAvailable()) {
            throw new BusinessException(status.getMessage());
        }
        return true;
    }

    //测试数据源(废弃)
    public String testDatasource(String id) {
        Datasource datasource = checkExist(id);
        DataSourceOptions sourceOptions = datasource.toDataSourceOptions();
        if (MqType.XIMC.equalsTypeName(sourceOptions.getTypeName())) {
            // 从 additionalParameters 中获取 imccUrl（消息中心地址）
            // TODO 消息中心数据源测试，暂时放在这里，待重构 eliming
            String imccUrl = (String) sourceOptions.get("imccUrl");
            if (imccUrl == null || imccUrl.trim().isEmpty()) {
                throw RdbmsException.asConnException(
                        MqType.XIMC.getTypeName(),
                        new IOException("消息中心地址未配置，请检查 imccUrl 参数"),
                        sourceOptions.getUsername(),
                        sourceOptions.getName()
                );
            }

            // todo 暂时让测试通过
            // 如果地址中不包含 "://" 则默认加上 "ws://" 前缀
            if (!imccUrl.contains("://")) {
                imccUrl = "ws://" + imccUrl;
            }

            try {
                // 使用默认加前缀后的地址解析 URL，获取主机和端口信息
                final URI imccUri = new URI(imccUrl);
                final String host = imccUri.getHost();
                int port = imccUri.getPort();
                // 如果端口未指定，根据协议使用默认端口
                if (port == -1) {
                    port = "ws".equalsIgnoreCase(imccUri.getScheme()) ? 5222 : 443;
                }

                // 尝试连接消息中心服务器，超时为 5000 毫秒
                try (Socket socket = new Socket()) {
                    socket.connect(new InetSocketAddress(host, port), 5000);
                } catch (Exception e) {
                    throw new IOException("无法连接到消息中心服务器: " + host + ":" + port, e);
                }
            } catch (URISyntaxException uriEx) {
                throw RdbmsException.asConnException(
                        MqType.XIMC.getTypeName(),
                        new IOException("消息中心地址格式不正确: " + uriEx.getMessage(), uriEx),
                        sourceOptions.getUsername(),
                        sourceOptions.getName()
                );
            } catch (IOException ioEx) {
                throw RdbmsException.asConnException(
                        MqType.XIMC.getTypeName(),
                        ioEx,
                        sourceOptions.getUsername(),
                        sourceOptions.getName()
                );
            }

        } else {
            DatabaseMetaQuery metaQuery = metaQueryFactory.getDatabaseMetaQuery(datasource.toDataSourceOptions());
            metaQuery.check();
        }
        return "true";
    }

    public Datasource getDatasource(String id) {
        return dsRepository.getDatasource(id);
    }


    //==================================================================


    public Datasource get(String id) {
        return dsRepository.get(id);
    }

    @Transactional(rollbackFor = SystemException.class)
    public boolean add(Datasource datasource) {
        this.checkRepeat(datasource);
        datasource.setId(SnowflakeGenerator.getNextId() + "");
        datasource.setCreateUser(UserContextHolder.getUserContext().getUsername());
        datasource.setCreateTime(new Date());
        datasource.setUpdateTime(new Date());
        doAddResource(datasource);
        try {
            dsRepository.add(datasource);
            return true;
        } catch (Exception e) {
            LOG.error("添加数据源信息异常", e);
            throw new SystemException("添加数据源信息异常");
        }
    }


    @Transactional(rollbackFor = SystemException.class)
    public boolean update(Datasource datasource) {
        datasource.setUpdateTime(new Date());
        Datasource older = this.checkExist(datasource.getId());
        doChangerResource(datasource, older);
        if (older.getName().equals(datasource.getName())) {
            //直接更新
            dsRepository.update(datasource);
        } else {
            //先判重
            this.checkRepeat(datasource);
            dsRepository.update(datasource);
        }
        return true;
    }

    public void delete(String id) {
        Datasource datasource = this.checkExist(id);
        doRemoveResource(datasource);
        dsRepository.delete(id);
    }

    public void deleteBatch(List<String> ids) {
        if (ids != null && !ids.isEmpty()) {
            for (String id : ids) {
                this.delete(id);
            }
        }
    }

    public String getTemplate(String id) {
        Datasource datasource = this.checkExist(id);
        DatasourceModel datasourceModel = dsModelRepository.getByType(datasource.getType());
        if (datasourceModel == null) {
            throw new MetadataException("数据源模型不存在");
        }
        return datasourceModel.getTemplate();
    }

    private void checkRepeat(Datasource dataSource) {
        Datasource repeat = dsRepository.getByName(dataSource.getName());
        if (repeat != null) {
            throw new BusinessException("数据源重复");
        }
    }

    private Datasource checkExist(String id) {
        Datasource older = dsRepository.get(id);
        if (older == null) {
            throw new MetadataException("数据源不存在");
        }
        return older;
    }

    /**
     * 如果在数据同步中使用了，就不能删除
     */
    private void checkUsed(String id) {
        // 原始调用feign修改成调用服务
        // datasyncClient.isUsed(id)
        if (dsRepository.isUsed(id)) {
            throw new MetadataException("数据源已使用，不能删除");
        }
    }

    public DatasourceModel getModelById(String datasourceId) {
        Datasource datasource = dsRepository.get(datasourceId);
        DatasourceModel datasourceModel = dsModelRepository.getByType(datasource.getType());
        return datasourceModel;
    }

    private void doAddResource(Datasource datasource) throws SystemException, BusinessException {
        DataSourceOptions sourceOptions = datasource.toDataSourceOptions();
        IResourceManager<DataSourceOptions> resourceManager = ResourceFactory.create(RMEnum.DB, new DefResourceHolder((path, e) -> {
            throw new BusinessException(path, e);
        }, fsTemplate), sourceOptions);
        List<String> resourceTypes = resourceManager.types();
        if (CollectionUtils.isNotEmpty(resourceTypes)) {
            JSONObject options = JSON.parseObject(datasource.getOptions());
            for (String type : resourceTypes) {
                String path = moveResource(resourceManager, type);
                if (path != null) {
                    JSONObject resourceObj = options.getJSONObject(type);
                    resourceObj.put("path", path);
                    options.put(type, resourceObj);
                    int id = HashUtils.apHash(path);
                    datasourceResourceSyncTaskRepository.add(DatasourceResourceSyncTask.add((id > 0 ? "1" + id : "0" + -id), path));
                }
            }
            datasource.setOptions(options.toJSONString());
        }
    }

    private void doRemoveResource(Datasource datasource) {
        DataSourceOptions sourceOptions = datasource.toDataSourceOptions();
        IResourceManager<DataSourceOptions> resourceManager = ResourceFactory.create(RMEnum.DB, new DefResourceHolder((path, e) -> {
            throw new BusinessException(path, e);
        }, fsTemplate), sourceOptions);
        List<String> resourceTypes = resourceManager.types();
        if (CollectionUtils.isNotEmpty(resourceTypes)) {
            for (String type : resourceTypes) {
                try {
                    resourceManager.removeResource(type);
                } catch (Exception e) {
                    LOG.warn("删除资源出现错误！请手动删除", e);
                }
            }
        }
    }

    private void doChangerResource(Datasource datasource, Datasource old) {
        DataSourceOptions sourceOptions = datasource.toDataSourceOptions();
        DataSourceOptions oldOptions = old.toDataSourceOptions();

        IResourceManager<DataSourceOptions> resourceManager = ResourceFactory.create(RMEnum.DB, new DefResourceHolder((path, e) -> {
            throw new BusinessException(path, e);
        }, fsTemplate), sourceOptions);
        IResourceManager<DataSourceOptions> oldResourceManager = ResourceFactory.create(RMEnum.DB, new DefResourceHolder((path, e) -> {
            throw new BusinessException(path, e);
        }, fsTemplate), oldOptions);

        JSONObject options = JSON.parseObject(datasource.getOptions());

        resourceManager.doChanger(oldOptions, new IResourceManager.IDoChange() {
            @Override
            public void doRemove(String type) {
                try {
                    oldResourceManager.removeResource(type);
                    String path = oldResourceManager.findPathByType(type);
                    if (path != null) {
                        int id = HashUtils.apHash(path);
                        datasourceResourceSyncTaskRepository.toRemove((id > 0 ? "1" + id : "0" + -id));
                    }
                } catch (Exception e) {
                    LOG.warn("删除资源出现错误！请手动删除", e);
                }
            }

            @Override
            public void doAdd(String type) {
                String path = moveResource(resourceManager, type);
                if (path != null) {
                    int id = HashUtils.apHash(path);
                    datasourceResourceSyncTaskRepository.add(DatasourceResourceSyncTask.add((id > 0 ? "1" + id : "0" + -id), path));
                    JSONObject resourceObj = options.getJSONObject(type);
                    resourceObj.put("path", path);
                    options.put(type, resourceObj);
                }
            }

            @Override
            public void doChange(String type) {
                String path = resourceManager.findPathByType(type);
                if (path != null) {
                    JSONObject resourceObj = options.getJSONObject(type);
                    resourceObj.put("path", path);
                    options.put(type, resourceObj);
                }
            }
        });

        datasource.setOptions(options.toJSONString());

    }

    private static String moveResource(IResourceManager<DataSourceOptions> resourceManager, String type) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            resourceManager.downloadResource(type, baos);
            try (ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
                 BufferedInputStream bis = new BufferedInputStream(bais)) {
                return resourceManager.addResource(type, bis);
            }
        } catch (Exception e) {
            LOG.error("迁移文件-{}-内容时发生异常!", e.getMessage());
            LOG.error("错误原因", e);
            throw new BusinessException("迁移文件异常");
        }
    }

}
