package com.xmcares.platform.admin.asset.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/27 15:42
 */
@ApiModel(value = MetaDatatable.TABLE, description = "表")
public class MetaDatatable implements Serializable {
    public static final String TABLE = "bdp_meta_datatable";

    @ApiModelProperty(value = "记录ID")
    private String id;

    @ApiModelProperty(value = "表名")
    private String name;

    @ApiModelProperty(value = "别名（中文）")
    private String alias;

    @ApiModelProperty(value = "资产编码")
    private String code;

    @ApiModelProperty(value = "数据源ID")
    private String datasource_id;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "目录ID")
    private String catalog_id;

    @ApiModelProperty(value = "仓库ID")
    private String dataware_id;

    @ApiModelProperty(value = "create_user")
    private String create_user;

    @ApiModelProperty(value = "create_time")
    private Date create_time;

    @ApiModelProperty(value = "update_user")
    private String update_user;

    @ApiModelProperty(value = "update_time")
    private Date update_time;

    public static String getTABLE() {
        return TABLE;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDatasource_id() {
        return datasource_id;
    }

    public void setDatasource_id(String datasource_id) {
        this.datasource_id = datasource_id;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCatalog_id() {
        return catalog_id;
    }

    public void setCatalog_id(String catalog_id) {
        this.catalog_id = catalog_id;
    }

    public String getDataware_id() {
        return dataware_id;
    }

    public void setDataware_id(String dataware_id) {
        this.dataware_id = dataware_id;
    }

    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_user() {
        return update_user;
    }

    public void setUpdate_user(String update_user) {
        this.update_user = update_user;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }
}
