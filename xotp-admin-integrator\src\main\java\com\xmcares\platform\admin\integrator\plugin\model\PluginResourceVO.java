package com.xmcares.platform.admin.integrator.plugin.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * PluginResourceVO
 *
 * <AUTHOR>
 * @Descriptions PluginResourceVO
 * @Date 2025/8/7 14:40
 */
@ApiModel(description="插件资源视图对象")
public class PluginResourceVO extends PluginResource {

    @ApiModelProperty(value="被同步任务引用的次数")
    private Integer usageCount = 0;

    @ApiModelProperty(value="引用该插件的同步任务名称列表")
    private List<String> usedByTasks;

    public Integer getUsageCount() {
        return usageCount;
    }

    public void setUsageCount(Integer usageCount) {
        this.usageCount = usageCount;
    }

    public List<String> getUsedByTasks() {
        return usedByTasks;
    }

    public void setUsedByTasks(List<String> usedByTasks) {
        this.usedByTasks = usedByTasks;
    }
}
