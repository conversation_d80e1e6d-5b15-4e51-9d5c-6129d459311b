package com.xmcares.platform.admin.lifecycle.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(value = AsstArchiveDatatable.TABLE, description = "数据资产归档数据表")
public class AsstArchiveDatatable {
    public static final String TABLE = "bdp_asst_archive_datatable";

    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "表ID")
    private String datatable_id;
    @ApiModelProperty(value = "库ID")
    private String dataware_id;
    @ApiModelProperty(value = "表名（冗余）")
    private String datatable_name;
    @ApiModelProperty(value = "归档调度ID")
    private String archive_scheduler_id;
    @ApiModelProperty(value = "归档条件表达式")
    private String archive_range_expr;
    @ApiModelProperty(value = "删除原表数据")
    private String datatable_reduced;
    @ApiModelProperty(value = "启用近线归档（0：未启用，1：启用）")
    private String nearlined;
    @ApiModelProperty(value = "近线数据源ID")
    private String nearline_datasource_id;
    @ApiModelProperty(value = "近线数据源名（冗余）")
    private String nearline_datasource_name;
    @ApiModelProperty(value = "近线数据表名")
    private String nearline_datatable_name;
    @ApiModelProperty(value = "启用离线归档（0：未启用，1：启用）")
    private String offlined;
    @ApiModelProperty(value = "离线文件名")
    private String offline_file_name;
    @ApiModelProperty(value = "离线留存天数")
    private Integer offline_file_lifedays;
    @ApiModelProperty(value = "create_user")
    private String create_user;
    @ApiModelProperty(value = "create_time")
    private Date create_time;
    @ApiModelProperty(value = "update_user")
    private String update_user;
    @ApiModelProperty(value = "update_time")
    private Date update_time;

    @ApiModelProperty(value = "归档类型")
    private String archive_type;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDatatable_id() {
        return datatable_id;
    }

    public void setDatatable_id(String datatable_id) {
        this.datatable_id = datatable_id;
    }

    public String getDataware_id() {
        return dataware_id;
    }

    public void setDataware_id(String dataware_id) {
        this.dataware_id = dataware_id;
    }

    public String getDatatable_name() {
        return datatable_name;
    }

    public void setDatatable_name(String datatable_name) {
        this.datatable_name = datatable_name;
    }

    public String getArchive_scheduler_id() {
        return archive_scheduler_id;
    }

    public void setArchive_scheduler_id(String archive_scheduler_id) {
        this.archive_scheduler_id = archive_scheduler_id;
    }

    public String getArchive_range_expr() {
        return archive_range_expr;
    }

    public void setArchive_range_expr(String archive_range_expr) {
        this.archive_range_expr = archive_range_expr;
    }

    public String getDatatable_reduced() {
        return datatable_reduced;
    }

    public void setDatatable_reduced(String datatable_reduced) {
        this.datatable_reduced = datatable_reduced;
    }

    public String getNearlined() {
        return nearlined;
    }

    public void setNearlined(String nearlined) {
        this.nearlined = nearlined;
    }

    public String getNearline_datasource_id() {
        return nearline_datasource_id;
    }

    public void setNearline_datasource_id(String nearline_datasource_id) {
        this.nearline_datasource_id = nearline_datasource_id;
    }

    public String getNearline_datasource_name() {
        return nearline_datasource_name;
    }

    public void setNearline_datasource_name(String nearline_datasource_name) {
        this.nearline_datasource_name = nearline_datasource_name;
    }

    public String getNearline_datatable_name() {
        return nearline_datatable_name;
    }

    public void setNearline_datatable_name(String nearline_datatable_name) {
        this.nearline_datatable_name = nearline_datatable_name;
    }

    public String getOfflined() {
        return offlined;
    }

    public void setOfflined(String offlined) {
        this.offlined = offlined;
    }

    public String getOffline_file_name() {
        return offline_file_name;
    }

    public void setOffline_file_name(String offline_file_name) {
        this.offline_file_name = offline_file_name;
    }

    public Integer getOffline_file_lifedays() {
        return offline_file_lifedays;
    }

    public void setOffline_file_lifedays(Integer offline_file_lifedays) {
        this.offline_file_lifedays = offline_file_lifedays;
    }

    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public String getUpdate_user() {
        return update_user;
    }

    public void setUpdate_user(String update_user) {
        this.update_user = update_user;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }

    public String getArchive_type() {
        return archive_type;
    }

    public void setArchive_type(String archive_type) {
        this.archive_type = archive_type;
    }
}
