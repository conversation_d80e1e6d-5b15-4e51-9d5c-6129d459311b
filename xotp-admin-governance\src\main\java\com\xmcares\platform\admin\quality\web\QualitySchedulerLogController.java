package com.xmcares.platform.admin.quality.web;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.platform.admin.quality.model.vo.QualitySchedulerLogListQueryVO;
import com.xmcares.platform.admin.quality.service.QualitySchedulerLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/25 09:10
 */
@Api(value = "质量规则调度记录服务")
@Validated
@RestController
@RequestMapping("/quality/scheduler-log")
public class QualitySchedulerLogController {

    @Autowired
    QualitySchedulerLogService qualitySchedulerLogService;

    @ApiOperation("规则执行查询")
    @PostMapping("/list-query")
    @ResponseBody
    public Page<QualitySchedulerLogListQueryVO> listQuery(
             String datawareId,
             String datatableId,
            @RequestParam(name = "beginTime") String beginTime,
            @RequestParam(name = "endTime") String endTime,
            @RequestParam(name = "pageNo") Integer pageNo,
            @RequestParam(name = "pageSize") Integer pageSize
    ) {
        return qualitySchedulerLogService.listQuery(datawareId, datatableId, beginTime, endTime, pageNo, pageSize);
    }
    @ApiOperation("从数据库中查询详细的规则调度记录")
    @GetMapping("/get")
    @ResponseBody
    public String get(
            @RequestParam(name = "scheduleId") String scheduleId,
            String metricId
    ) {
        return qualitySchedulerLogService.get(scheduleId, metricId);
    }
}
