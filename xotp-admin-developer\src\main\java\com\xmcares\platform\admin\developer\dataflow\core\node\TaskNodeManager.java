package com.xmcares.platform.admin.developer.dataflow.core.node;

import com.xmcares.framework.commons.util.CollectionUtils;
import com.xmcares.platform.admin.common.errors.SystemException;
import com.xmcares.platform.admin.developer.dataflow.core.DigraphAcyclicGraph;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/29 16:28
 **/
public class TaskNodeManager<R extends IGroupDAGNode> implements ITaskNodeManager<R>{

    private List<ITaskNode<R>> nodeAgents;

    public void init(List<ITaskNode<R>> nodeAgents) {
        this.nodeAgents = nodeAgents;
    }

    @Override
    public void check() throws SystemException {
        if (CollectionUtils.isEmpty(nodeAgents)) {
            throw new SystemException("需要管理的节点未初始化");
        }
        Map<String, List<IGroupDAGNode>> groupNodes = nodeInfos().stream().collect(Collectors.groupingBy(IGroupDAGNode::groupId));
        groupNodes.forEach((k,v) -> {
            if (v.size() == 1) { return; }
            DigraphAcyclicGraph<IGroupDAGNode> dag = DigraphAcyclicGraph.create(v);
            if (!dag.isDAG()) {
                throw new SystemException(String.format("任务【%s】所在的图非法：不允许任务递归", v.get(0).name()));
            }
        });
        for (ITaskNode<R> nodeAgent : nodeAgents) {
            nodeAgent.validate();
        }
    }

    @Override
    public List<R> nodeInfos() {
        return nodeAgents.stream().map(ITaskNode::nodeInfo).collect(Collectors.toList());
    }

}
