/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2025
 * Author： lius
 * Date：2025/5/27
 */
package com.xmcares.platform.admin.dataservice.message.repository;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.framework.sharing.reference.resource.AppResource;
import com.xmcares.platform.admin.dataservice.message.vo.MessageTopicAuthAppVO;
import com.xmcares.platform.admin.dataservice.message.vo.MessageTopicVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 消息主题数据访问层
 * <AUTHOR>
 * @Date 2025/5/27
 */
@Repository
public class MessageTopicRepository {

    private static final Logger LOG = LoggerFactory.getLogger(MessageTopicRepository.class);

    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    public static final String TABLE_MESSAGE_TOPIC = "bdp_api_message_topic";

    public static final String TABLE_TOPIC_APP = "bdp_api_message_topic_app";


    /**
     * 创建或更新消息主题
     *
     * @param messageTopic 消息主题对象
     * @return MessageTopicVO
     */
    public MessageTopicVO createOrUpdateMessageTopic(MessageTopicVO messageTopic) {
        int result;
        if (messageTopic.getId() == null) {
            // 插入新主题
            String id = SnowflakeGenerator.getNextId().toString();
            messageTopic.setId(id);
            result = xcfJdbcTemplate.update(
                    "INSERT INTO " + TABLE_MESSAGE_TOPIC + "(id, topic_name, datasource_id, remark, create_time, update_time, create_user, update_user) VALUES (?,?,?,?,?,?,?,?)",
                    messageTopic.getId(), messageTopic.getTopicName(), messageTopic.getDatasourceId(),
                    messageTopic.getRemark(), messageTopic.getCreateTime(), messageTopic.getUpdateTime(),
                    messageTopic.getCreateUser(), messageTopic.getUpdateUser());
        } else {
            // 更新已存在的主题
            result = xcfJdbcTemplate.update(
                    "UPDATE " + TABLE_MESSAGE_TOPIC + " SET topic_name=?, datasource_id=?, remark=?, update_time=?, update_user=? WHERE id=?",
                    messageTopic.getTopicName(), messageTopic.getDatasourceId(),
                    messageTopic.getRemark(), messageTopic.getUpdateTime(), messageTopic.getUpdateUser(),
                    messageTopic.getId());
        }
        return result > 0 ? messageTopic : null;
    }


    /**
     * 分页查询消息主题
     *
     * @param messageTopicVO 参数
     * @param page           分页信息
     * @return
     */
    public List<MessageTopicVO> messageTopicPageQuery(MessageTopicVO messageTopicVO, Page<MessageTopicVO> page) {
        Map<String, Object> conditions = buildCondition(messageTopicVO);
        Map<String, Object> map = DBUtils.queryList(MessageTopicVO.TABLE, conditions);
        String sql = DBUtils.getSql(map) + " ORDER BY update_time Desc ";
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntities(sql, args, page, MessageTopicVO.class);
    }


    /**
     * 删除消息主题
     *
     * @param topicId 主题ID列表
     * @return List<String>
     */
    public int deleteMessageTopics(String topicId) {
        return xcfJdbcTemplate.update("DELETE FROM " + TABLE_MESSAGE_TOPIC + " WHERE id = ?", topicId);
    }

    /**
     * 根据ID获取消息主题
     *
     * @param topicId 主题ID
     * @return MessageTopicVO
     */
    public MessageTopicVO getMessageTopicById(String topicId) {
        String query = "SELECT * FROM " + TABLE_MESSAGE_TOPIC + " WHERE id = ?";
        return xcfJdbcTemplate.queryForEntity(query, new Object[]{topicId}, MessageTopicVO.class);
    }

    /**
     * 检查主题名称唯一性
     *
     * @param topicName 主题名称
     * @return boolean
     */
    public boolean nameUniqueCheck(String topicName) {
        Integer count = xcfJdbcTemplate.queryForObject("SELECT COUNT(*) FROM " + TABLE_MESSAGE_TOPIC + " WHERE topic_name = ?", Integer.class, topicName);
        return count != null && count == 0;
    }

    private Map<String, Object> buildCondition(MessageTopicVO messageTopicVO) {
        Map<String, Object> conditions = new HashMap<>();
        if (messageTopicVO != null) {
            if (messageTopicVO.getTopicName() != null && !messageTopicVO.getTopicName().equals("")) {
                conditions.put("topicName", "%" + messageTopicVO.getTopicName() + "%");
            }
            if (messageTopicVO.getRemark() != null && !messageTopicVO.getRemark().equals("")) {
                conditions.put("remark", "%" + messageTopicVO.getRemark() + "%");
            }
        }
        return conditions;
    }

    /**
     * 统计数据流
     *
     * @param messageTopicVO 查询参数
     * @return
     */
    public int countDevDataflow(MessageTopicVO messageTopicVO) {
        Map<String, Object> conditions = buildCondition(messageTopicVO);
        Map<String, Object> map = DBUtils.queryCount(MessageTopicVO.TABLE, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForObject(sql, Integer.class, args);
    }


    // 清空指定 topicId 的所有 appId
    public boolean deleteAppAuthByTopicId(String topicId) {
        String sql = "DELETE FROM " + TABLE_TOPIC_APP + " WHERE topic_id = ?";
        int rowsAffected = xcfJdbcTemplate.update(sql, topicId);
        return rowsAffected > 0;
    }


    public boolean insertAppAuth(String appId, String topicId) {
        String id = SnowflakeGenerator.getNextId().toString();
        String sql = "INSERT INTO " + TABLE_TOPIC_APP + " (id,app_id, topic_id, oper_type) " +
                "VALUES (?, ?, ?, ?)";

        int rowsAffected = xcfJdbcTemplate.update(sql, id, appId, topicId,"SUB");
        return rowsAffected > 0;
    }

    // 清空指定 appId 的所有 TopicId
    public boolean deleteTopicAuthByAppId(String appId) {
        String sql = "DELETE FROM " + TABLE_TOPIC_APP + " WHERE app_id = ?";
        int rowsAffected = xcfJdbcTemplate.update(sql, appId);
        return rowsAffected > 0;
    }



    // 根据 topicId 获取已授权的应用列表
    public List<MessageTopicAuthAppVO> getAppResourceByTopicId(String topicId) {
        // Step 1: 定义 SQL 查询语句
        String sql = "SELECT app_id, create_user, oper_type, remark " +
                "FROM " + TABLE_TOPIC_APP + " " + // 添加空格
                "WHERE topic_id = ? ";

        // Step 2: 执行查询并处理结果
        return xcfJdbcTemplate.query(sql, new Object[]{topicId}, new RowMapper<MessageTopicAuthAppVO>() {
            @Override
            public MessageTopicAuthAppVO mapRow(ResultSet rs, int rowNum) throws SQLException {
                MessageTopicAuthAppVO messageTopicAuthAppVO = new MessageTopicAuthAppVO();
                messageTopicAuthAppVO.setAppId(rs.getString("app_id"));
                return messageTopicAuthAppVO;
            }
        });
    }

    // 根据 appId 获取已授权的主题列表
    public List<MessageTopicAuthAppVO> getTopicResourceByAppId(String appId) {
        // Step 1: 定义 SQL 查询语句
        String sql = "SELECT topic_id, create_user, oper_type, remark " +
                "FROM " + TABLE_TOPIC_APP + " " + // 添加空格
                "WHERE app_id = ? ";

        // Step 2: 执行查询并处理结果
        return xcfJdbcTemplate.query(sql, new Object[]{appId}, new RowMapper<MessageTopicAuthAppVO>() {
            @Override
            public MessageTopicAuthAppVO mapRow(ResultSet rs, int rowNum) throws SQLException {
                MessageTopicAuthAppVO messageTopicAuthAppVO = new MessageTopicAuthAppVO();
                messageTopicAuthAppVO.setTopicId(rs.getString("topic_id"));
                return messageTopicAuthAppVO;
            }
        });
    }
}
