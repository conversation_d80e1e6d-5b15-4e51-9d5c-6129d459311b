/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2023/5/26
 */
package com.xmcares.platform.admin.common.datasource.mq.rocketmq;


import com.xmcares.framework.commons.thread.NamedThreadFactory;
import com.xmcares.platform.admin.common.datasource.mq.MessageHeaders;
import com.xmcares.platform.admin.common.datasource.mq.MqDataSource;
import com.xmcares.platform.admin.common.datasource.mq.rocketmq.core.CustomMQConsumer;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.rocketmq.client.consumer.PullResult;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageQueue;
import org.apache.rocketmq.common.protocol.heartbeat.SubscriptionData;
import org.apache.rocketmq.remoting.exception.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * RocketMQ消息客户端代理，支持的版本4.5.x
 *
 * <AUTHOR>
 * @since 1.4.1
 */
public class RocketMqDataSource implements MqDataSource {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final String name;
    private final RocketMqProperties properties;
    private DefaultMQProducer producer;
    private Map<String/*group*/, CustomMQConsumer> consumers = new ConcurrentHashMap<>();
    //private CustomMQConsumer consumer;

    private final Map<String/*topic*/, Lock> topicPullLocks = new ConcurrentHashMap<>();

    //private long lastPullTimestamp;
    //private long lastSendTimestamp;

    /**
     * 主题队列拉取线程池
     */
    private final ExecutorService pullExecutor;

    public RocketMqDataSource(String name, RocketMqProperties properties) {
        if (Objects.isNull(name)) {
            throw new IllegalArgumentException("参数name不可为空");
        }
        this.name = name;
        this.properties = properties;
        pullExecutor = Executors.newCachedThreadPool(new NamedThreadFactory("MessageQueue-Pull", true));
    }

    public String getName() {
        return name;
    }

    protected String getConsumerGroup(String groupName) {
        return this.name + "_group_" + groupName;
    }

    @Override
    public void addMessageListener(String topic, String group, MqMessageListener listener) {
        //TODO
    }

    @Override
    public void removeMessageListener(String topic, String group, MqMessageListener listener) {
        //TODO
    }

    @Override
    public void sendMessage(String topic, MessageHeaders headers, byte[] body) {
        if (body == null || body.length == 0) {
            return;
        }
        Message message = new Message(topic, body);
        String orderKey = null;
        if (headers != null && !headers.isEmpty()) {
            orderKey = headers.getOrderKey();
        }
        try {
            SendResult result;
            if (StringUtils.isNotEmpty(orderKey)) {
                message.setKeys(orderKey);
                result = this.getProducer().send(message, (list, msg, o) -> {
                    String orderKey1 = o.toString();
                    int index = (orderKey1.hashCode() & Integer.MAX_VALUE) % list.size();
                    return list.get(index);
                }, orderKey);
            } else {
                result = this.getProducer().send(message);
            }
            switch (result.getSendStatus()) {
                case SEND_OK:
                    break;
                case FLUSH_DISK_TIMEOUT:
                case FLUSH_SLAVE_TIMEOUT:
                case SLAVE_NOT_AVAILABLE:
                    logger.warn("向RocketMQ数据源[{}]发送消息成功，但服务器可能存在异常，异常状态码：{}", this.getName(), result.getSendStatus().name());
                    break;
                default:
                    throw new RuntimeException("向RocketMQ数据源["+ getName()+"]发送消息返回未知状态: "+result.getSendStatus());
            }

        } catch (MQClientException e) {
            /*
             * 生产者服务未运行 || 主题为空 || 主题只允许a-z|A-Z|0-9|_|- || 长度超过102 || 等于TBW102
             * 消息为空 || 消息体为空 || 消息体长度<=0 || 消息体长度 > 允许的长度（可配置）
             * 未能找到路由信息 || 获取队列异常或者获取到的队列为空 || 从远处服务获取到的Border不存在。
             */
            throw new RuntimeException("向RocketMQ数据源["+this.getName()+"]发送消息错误", e);
        } catch (RemotingException e) {
            if (e instanceof RemotingCommandException) {
                // 解析远程命令时出现异常，出现该异常的可能客户端与服务端版本差距较大
                throw new RuntimeException("向RocketMQ数据源["+this.getName()+"]发送消息内容存在问题", e);
            }
            if (e instanceof RemotingConnectException || e instanceof RemotingSendRequestException) {
                // RemotingSendRequestException：远程请求信息异常
                // RemotingConnectException：连接通道断开异常
                throw new RuntimeException("与RocketMQ数据源["+this.getName()+"]远程服务连接异常(等待进行重连)", e);
            }
            if (e instanceof RemotingTooMuchRequestException || e instanceof RemotingTimeoutException) {
                // RemotingTooMuchRequestException： 当获取队列信息超时时 | 处理用户自定义的Hook过久时
                // RemotingTimeoutException：发送远程消息超时
                throw new RuntimeException("给RocketMQ数据源["+this.getName()+"]发送消息处理超时", e);
            }
            //TODO ? throws
        } catch (MQBrokerException | InterruptedException e) {
            // InterruptedException： 同步等待的线程异常
            // MQBrokerException：说明已经从远程服务端获取到信息，但远程给了异常的信息
            throw new RuntimeException("RocketMQ数据源["+this.getName()+"]远程服务端处理异常", e);
        }
    }

    /**
     * 拉取消息
     *
     * @param topic
     * @return
     */
    @Override
    public void pullMessage(String topic, String group, MessageHeaders headers, Callback callback) {
        //null默认true
        callback = callback == null ? (messages -> true) : callback;
        try {
            CustomMQConsumer consumer = this.getConsumer(group);
            //注册后才能更新到主题的路由信息
            consumer.registerTopicIfAbsent(topic);
            Lock lock = getTopicLock(topic);
            try {
                // 尝试获取锁，超时时间则不等待，避免客户端等待超时
                boolean acquired = lock.tryLock(this.properties.getConsumerPollingInMills(), TimeUnit.MILLISECONDS);
                if (acquired) {
                    try {// 用于释放lock
                        //记录下一个偏移（本次拉取后新的拉取位置）
                        Map<MessageQueue, Long> nextOffsets = new ConcurrentHashMap<>();

                        List<byte[]> messages = tryPullMessage(topic, group, headers, nextOffsets);
                        boolean success;
                        try {
                            success = callback.invoke(messages);
                        } catch (Exception e) {
                            logger.error("响应RocketMQ数据源[{}]主题[ {} ]消息失败:", this.getName(), topic, e);
                            success = false;
                        }
                        if (success) {
                            for (Map.Entry<MessageQueue, Long> mqOffset : nextOffsets.entrySet()) {
                                consumer.updateQueueOffset(mqOffset.getKey(), mqOffset.getValue(), false);
                            }
                        }
                    } finally {
                        lock.unlock();
                    }
                }
            } catch (InterruptedException e) {
                // 捕获 tryLock,其他异常交给proxy模块外的调用者处理
                logger.error("RocketMQ数据源[{}]拉取主题[{}]消息时被Interrupted：", this.getName(), topic, e);
                Thread.currentThread().interrupt();
                callback.invoke(Collections.emptyList());
            }
        } catch (MQClientException e) {
            //交由proxy模块包的 外层调用者处理
            throw new RuntimeException(String.format("RocketMQ数据源[%s]拉取主题[%s]消息错误:%s", getName(), topic, e.getErrorMessage()), ExceptionUtils.getRootCause(e));
        }
    }


    @Override
    public AvailableStatus testAvailable() {
        //TODO
        return null;
    }

    @Override
    public void close() throws Exception {
        this.shutdown();
    }

    public void shutdown() {
        this.shutdownProducer();
        this.shutdownConsumer();
    }

    public void shutdownProducer() {
        if (this.producer != null) {
            this.producer.shutdown();
        }
    }

    public void shutdownConsumer() {
        if (!this.consumers.isEmpty()) {
            for (CustomMQConsumer consumer : consumers.values()) {
                consumer.shutdown();
            }
        }
        this.topicPullLocks.clear();
    }

    /**
     * 尝试拉取消息
     * @param topic 主题名
     * @param headers 消息头
     * @param nextOffsets 下一个偏移
     * @return 消息内容
     * @throws MQClientException 获取主题队列异常
     */
    private List<byte[]> tryPullMessage(String topic, String group, MessageHeaders headers, Map<MessageQueue, Long> nextOffsets) throws MQClientException {
        //获取分配到的主题队列
        CustomMQConsumer consumer = this.getConsumer(group);
        Set<MessageQueue> dividedQueues = consumer.getDividedQueuesOf(topic);
        if (dividedQueues.isEmpty()) {
            //注意：第一次注册主题拉取消息时，因负载均衡异步延迟，可能未拿到分配的主题队列。
            try {
                TimeUnit.SECONDS.sleep(5);
            } catch (InterruptedException e) {
                //
            }
            dividedQueues = consumer.getDividedQueuesOf(topic);
        }
        if (dividedQueues.isEmpty()) {
            return Collections.emptyList();
        }

        //请求开始时间（该时间是服务端时间）
        long reqTimestamp = headers.getTimestamp();
        //剩余超时毫秒数 = 轮询可用最长毫秒数 - 已消耗的毫秒数
        long timeout = this.properties.getConsumerPollingInMills() - (System.currentTimeMillis() - reqTimestamp);

        List<byte[]> result = new ArrayList<>();

        //主题订阅/拉取信息
        SubscriptionData subscription = consumer.getSubscriptionData(topic);
        //留10s给向rocket server端拉取通信使用
        while (timeout > 10 * 1000) {
            result.addAll(pullSubsMessage(subscription, group, dividedQueues, nextOffsets, timeout));
            if (!result.isEmpty()) {
                break;
            } else {
                try {
                    //5s后再重试，避免空拉 给rocket造成压力
                    TimeUnit.SECONDS.sleep(3);
                } catch (InterruptedException e) {
                    //
                }
            }
            timeout = this.properties.getConsumerPollingInMills() - (System.currentTimeMillis() - reqTimestamp);
        }
        return result;
    }

    /**
     * 拉取主题信息
     *
     * @param subscription  订阅信息
     * @param group
     * @param dividedQueues 主题队列
     * @param nextOffsets   偏移量存储
     * @param timeout       超时毫秒数
     * @return 消息内容
     * @throws MQClientException 获取主题队列异常
     */
    private List<byte[]> pullSubsMessage(SubscriptionData subscription, String group, Set<MessageQueue> dividedQueues,
                                         Map<MessageQueue, Long> nextOffsets, long timeout) throws MQClientException {
        int queueCount = dividedQueues.size();
        List<MQFuture> futures = new ArrayList<>(queueCount);
        int batchSize = Math.max(this.properties.getConsumerPollingMaxRecords() / queueCount, 1);//避免batchSize=0；

        CustomMQConsumer mqConsumer = this.getConsumer(group);
        for (MessageQueue queue : dividedQueues) {
            futures.add(new MQFuture(queue, this.pullExecutor
                    .submit(() -> {
                        Long nextOffset = nextOffsets.get(queue);
                        if (nextOffset == null) {
                            nextOffset = mqConsumer.getQueueOffset(queue);
                            nextOffsets.put(queue, nextOffset);
                        }
                        return mqConsumer.pullQueueMessage(subscription, queue, nextOffset, batchSize, true, timeout);
                    }))
            );
        }
        List<byte[]> messages = new ArrayList<>();
        for (MQFuture future : futures) {
            try {
                PullResult pullResult = future.get(this.properties.getConsumerPollingInMills(), TimeUnit.MILLISECONDS);
                Long nextOffset = nextOffsets.get(future.getMq());
                switch (pullResult.getPullStatus()) {
                    case FOUND:
                        if (!CollectionUtils.isEmpty(pullResult.getMsgFoundList())) {
                            nextOffsets.put(future.getMq(), pullResult.getNextBeginOffset());
                            messages.addAll(pullResult.getMsgFoundList()
                                    .stream().map(Message::getBody).collect(Collectors.toList()));
                        }
                        break;
                    case OFFSET_ILLEGAL:
                        if (logger.isWarnEnabled()) {
                            logger.warn("RocketMQ数据源[{}]从队列请求的offset[ {} ] illegal： {}", this.getName(), nextOffset, pullResult);
                        }
                        //修复偏移量
                        nextOffset = pullResult.getNextBeginOffset();
                        nextOffsets.put(future.getMq(), nextOffset);
                        mqConsumer.updateQueueOffset(future.getMq(), nextOffset, false);
                        break;
                    default:
                        if (logger.isWarnEnabled()) {
                            logger.warn("RocketMQ数据源[{}]从队列请求的offset[ {} ] 未知状态： {}", this.getName(), nextOffset, pullResult);
                        }
                        break;
                }

            } catch (Exception e) {
                logger.warn("RocketMQ数据源[{}]从队列[{}]Future收取失败:{}", this.getName(), future.getMq(), e.getMessage());
            }
        }
        return messages;
    }

    /**
     * 获取主题拉取锁，主题消息必能并行拉取，否则会出现偏移增长错乱。
     *
     * @param topic 主题名
     * @return Lock
     */
    private Lock getTopicLock(String topic) {
        return topicPullLocks.computeIfAbsent(topic, key -> new ReentrantLock());
    }

    private synchronized DefaultMQProducer getProducer() {
        if (this.producer == null) {
            long a = System.currentTimeMillis();
            logger.info("延迟启动RocketMQ数据源[ {} ]生成者...", this.getName());
            this.producer = createProducer();
            logger.info("延迟启动RocketMQ数据源[ {} ]生成者完成[COST: {}ms]", this.getName(), (System.currentTimeMillis()-a));
        }
        return this.producer;
    }

    private DefaultMQProducer createProducer() {
        //uniqueName 作为组ID，当使用同一个任务启动多个消费对象实例时，保证其是组消费模式，使消息不会重复消费。
        DefaultMQProducer producer = new DefaultMQProducer(this.getName());
        producer.resetClientConfig(this.properties);
        //todo ROCKETMQ 4.5 版本不支持 账号 密码 认证，需要使用账号密码认证
        try {
            producer.start();
        } catch (MQClientException e) {
            throw new RuntimeException("启动RocketMQ数据源["+ getName()+"]生产者失败", e);
        }
        return producer;
    }

    private synchronized CustomMQConsumer getConsumer(String groupName) {
        return this.consumers.computeIfAbsent(groupName, key -> {
            long a = System.currentTimeMillis();
            logger.info("延迟启动RocketMQ数据源[ {} ]消费者组[{}]...", this.getName(), key);
            CustomMQConsumer consumer = createConsumer(getConsumerGroup(key));
            logger.info("延迟启动RocketMQ数据源[ {} ]消费者组[{}]完成[COST: {}ms]", this.getName(), key, (System.currentTimeMillis()-a));
            return consumer;
        });
    }

    private CustomMQConsumer createConsumer(String groupName) {
        CustomMQConsumer consumer = new CustomMQConsumer(groupName);
        consumer.resetClientConfig(this.properties);
        consumer.setStartOffsetBeforeHours(properties.getConsumerStartOffsetBeforeHours());
        try {
            consumer.start();
        } catch (MQClientException e) {
            throw new RuntimeException("启动RocketMQ数据源["+ getName()+"]消费者失败", e);
        }
        return consumer;
    }


    /**
     * 设计每个MessageQueue对应的Future类，并传入messageQueue信息
     */
    static class MQFuture implements Future<PullResult> {

        private final MessageQueue mq;
        private final Future<PullResult> future;

        public MQFuture(MessageQueue queue, Future<PullResult> future) {
            this.mq = queue;
            this.future = future;
        }

        public MessageQueue getMq() {
            return mq;
        }

        @Override
        public boolean cancel(boolean mayInterruptIfRunning) {
            return this.future.cancel(mayInterruptIfRunning);
        }

        @Override
        public boolean isCancelled() {
            return this.future.isCancelled();
        }

        @Override
        public boolean isDone() {
            return this.future.isDone();
        }

        @Override
        public PullResult get() throws InterruptedException, ExecutionException {
            return this.future.get();
        }

        @Override
        public PullResult get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
            return this.future.get(timeout, unit);
        }
    }

}
