/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2023/6/18
 */
package com.xmcares.platform.admin.common.datasource.mq;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @since 1.4.1
 */
public class MessageHeaders implements Map<String, Object>, Serializable {
    public static final String KEY_CLIENT_ID = "ClientId";
    public static final String KEY_REQUEST_ID = "RequestId";
    public static final String KEY_TIMESTAMP = "Timestamp";
    public static final String KEY_ORDER_KEY = "OrderKey";

    private final Map<String, Object> headers;

    public MessageHeaders() {
        this(new HashMap<String, Object>(), null);
    }
    /**
     * 使用给定的请求头构造MessageHeaders 。
     * 还将添加 ID 和 TIMESTAMP 标头，覆盖任何现有值。
     * 参数：
     * @param headers –带有要添加的头
     */
    public MessageHeaders(Map<String, Object> headers) {
        this(headers, null);
    }
    /**
     * 提供对请求ID 和请求时间戳的控制的构造函数
     * @param headers
     * @param requestId
     */
    public MessageHeaders(Map<String, Object> headers, String requestId) {
        this.headers = (headers != null ? new HashMap<>(headers) : new HashMap<>());
        if (StringUtils.isNotEmpty(requestId)) {
            this.headers.put(KEY_REQUEST_ID, requestId);
        }
        this.headers.put(KEY_TIMESTAMP, System.currentTimeMillis());
    }

    public String getClientId() {
        return this.get(KEY_CLIENT_ID, String.class);
    }

    public String getOrderKey() {
        return this.get(KEY_ORDER_KEY, String.class);
    }

    public void setOrderKey(String orderKey) {
        this.put(KEY_ORDER_KEY, orderKey);
    }


    public String getRequestId() {
        return this.get(KEY_REQUEST_ID, String.class);
    }

    public void setRequestId(String requestId) {
        this.put(KEY_REQUEST_ID, requestId);
    }
    public Long getTimestamp() {
        return this.get(KEY_TIMESTAMP, Long.class);
    }

    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> type) {
        Object value = this.headers.get(key);
        if (value == null) {
            return null;
        }
        if (!type.isAssignableFrom(value.getClass())) {
            throw new IllegalArgumentException("Incorrect type specified for header '" +
                    key + "'. Expected [" + type + "] but actual type is [" + value.getClass() + "]");
        }
        return (T) value;
    }


    // Delegating Map implementation

    @Override
    public boolean containsKey(Object key) {
        return this.headers.containsKey(key);
    }

    @Override
    public boolean containsValue(Object value) {
        return this.headers.containsValue(value);
    }

    @Override
    public Set<Entry<String, Object>> entrySet() {
        return Collections.unmodifiableMap(this.headers).entrySet();
    }

    @Override
    public Object get(Object key) {
        return this.headers.get(key);
    }

    @Override
    public boolean isEmpty() {
        return this.headers.isEmpty();
    }

    @Override
    public Set<String> keySet() {
        return Collections.unmodifiableSet(this.headers.keySet());
    }

    @Override
    public int size() {
        return this.headers.size();
    }

    @Override
    public Collection<Object> values() {
        return Collections.unmodifiableCollection(this.headers.values());
    }


    @Override
    public Object put(String key, Object value) {
        return this.headers.put(key, value);
    }

    @Override
    public void putAll(Map<? extends String, ? extends Object> map) {
        this.headers.putAll(map);
    }

    @Override
    public Object remove(Object key) {
        return this.headers.remove(key);
    }

    @Override
    public void clear() {
        this.headers.clear();
    }


    // equals, hashCode, toString
    @Override
    public boolean equals(Object other) {
        return (this == other ||
                (other instanceof MessageHeaders
                        && this.headers.equals(((MessageHeaders) other).headers)));
    }

    @Override
    public int hashCode() {
        return this.headers.hashCode();
    }

    @Override
    public String toString() {
        return this.headers.toString();
    }
}
