{
  "plugin_name": "Pulsar",
  "plugin_output": "${source.plugin_output!'default_source_output'}",
  "client.service-url": "${orginDatasource.url}",
  "admin.service-url": "${orginDatasource.adminServiceUrl}",
  "topic": "${orgin.topic}",
  "format": "${orgin.format!'json'}",
  "subscription.name": "sub-${.now?string('yyyyMMddHHmmss')}",
  "schema": {
    "fields": {
    <#-- schema字段从orginColumns动态生成 -->
    <#list orginColumns as column>
      "${column.title}": "${column.type}"<#if column_has_next>,</#if>
    </#list>
    }
  }
}
