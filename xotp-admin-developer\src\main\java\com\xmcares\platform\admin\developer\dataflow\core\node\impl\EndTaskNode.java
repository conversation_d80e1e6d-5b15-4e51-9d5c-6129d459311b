package com.xmcares.platform.admin.developer.dataflow.core.node.impl;

import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.developer.dataflow.core.node.BaseTaskNode;
import org.apache.commons.collections.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2022/6/30 11:08
 **/
public class EndTaskNode extends BaseTaskNode {


    @Override
    public void validate() throws BusinessException {
        // 1. 开始节点不允许有后置节点
        if (CollectionUtils.isNotEmpty(nodeInfo().getPostIds())) {
            throw new BusinessException("节点【" + nodeInfo().getLabel() + "】不允许存在后置节点！");
        }
    }

}
