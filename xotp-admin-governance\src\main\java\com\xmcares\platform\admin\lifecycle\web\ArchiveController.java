package com.xmcares.platform.admin.lifecycle.web;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.platform.admin.lifecycle.model.vo.*;
import com.xmcares.platform.admin.lifecycle.service.ArchiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Api(value = "表质量规则配置服务")
@Validated
@RestController
@RequestMapping("/lifecycle/archive")
public class ArchiveController {

    @Autowired
    ArchiveService archiveService;

    @ApiOperation("根据查询条件获取归档配置列表")
    @GetMapping("/query")
    @ResponseBody
    public Page<LifecycleArchiveQueryVO> listQuery(
            String datawareId,
            String datatableId,
            String datatableReduced,
            @RequestParam(name = "pageNo") Integer pageNo,
            @RequestParam(name = "pageSize") Integer pageSize
    ) {
        return archiveService.listQuery(datawareId, datatableId, datatableReduced, pageNo, pageSize);
    }


    @ApiOperation("根据归档信息ID获取归档明细")
    @GetMapping("/get")
    @ResponseBody
    public LifecycleArchiveGetVO get(
            @RequestParam(name = "id") String id
    ) {
        return archiveService.get(id);
    }


    @ApiOperation("新增归档调度信息")
    @PostMapping("/scheduler/add")
    @ResponseBody
    public String schedulerAdd(
            @RequestBody LifecycleArchiveSchedulerAddInVO schedulerAddInVO
    ) {
        return archiveService.schedulerAdd(schedulerAddInVO);
    }


    @ApiOperation("更新归档调度信息")
    @PostMapping("/scheduler/update")
    @ResponseBody
    public String schedulerUpdate(
            @RequestBody LifecycleArchiveSchedulerUpdateInVO schedulerUpdateInVO
    ) {
        return archiveService.schedulerUpdate(schedulerUpdateInVO);
    }


    @ApiOperation("删除归档调度信息")
    @GetMapping("/scheduler/delete")
    @ResponseBody
    public String schedulerDelete(
            @RequestParam(name = "id") String id
    ) {
        return archiveService.schedulerDelete(id);
    }


    @ApiOperation("关闭归档调度")
    @GetMapping("/scheduler/close")
    @ResponseBody
    public String schedulerClose(
            @RequestParam(name = "id") String id
    ) {
        return archiveService.schedulerClose(id);
    }


    @ApiOperation("打开归档调度")
    @GetMapping("/scheduler/open")
    @ResponseBody
    public String schedulerOpen(
            @RequestParam(name = "id") String id
    ) {
        return archiveService.schedulerOpen(id);
    }


    @ApiOperation("添加归档数据")
    @PostMapping("/add")
    @ResponseBody
    public String add(
            @RequestBody LifecycleArchiveAddInVO lifecycleArchiveAddInVO
    ) {
        return archiveService.add(lifecycleArchiveAddInVO);
    }


    @ApiOperation("更新归档数据")
    @PostMapping("/update")
    @ResponseBody
    public String update(
            @RequestBody LifecycleArchiveUpdateInVO lifecycleArchiveAddInVO
    ) {
        return archiveService.update(lifecycleArchiveAddInVO);
    }


    @ApiOperation("删除归档信息")
    @GetMapping("/delete")
    @ResponseBody
    public String delete(
            @RequestParam(name = "id") String id
    ) {
        return archiveService.delete(id);
    }


    @ApiOperation("启用归档")
    @GetMapping("/start")
    @ResponseBody
    public String start(
            @RequestParam(name = "id") String id,
            @RequestParam(name = "archiveType") String archiveType
    ) {
        return archiveService.start(id, archiveType);
    }

    @ApiOperation("禁用归档")
    @GetMapping("/disable")
    @ResponseBody
    public String disable(
            @RequestParam(name = "id") String id,
            @RequestParam(name = "archiveType") String archiveType
    ) {
        return archiveService.disable(id, archiveType);
    }


    @ApiOperation("根据查询条件获取归档记录列表")
    @GetMapping("/record/query")
    @ResponseBody
    public Page<LifecycleArchiveRecordQueryVO> recordQuery(
            String datawareId,
            String datatableId,
            String archiveId,
            @RequestParam(name = "beginTimer") String beginTimer,
            @RequestParam(name = "endTimer") String endTimer,
            @RequestParam(name = "pageNo") Integer pageNo,
            @RequestParam(name = "pageSize") Integer pageSize
    ) {
        return archiveService.recordQuery(datawareId, datatableId, archiveId, beginTimer, endTimer, pageNo, pageSize);
    }


    @ApiOperation("删除归档记录信息")
    @GetMapping("/record/delete")
    @ResponseBody
    public String recordDelete(
            @RequestParam(name = "id") String id
    ) {
        return archiveService.recordDelete(id);
    }


}
