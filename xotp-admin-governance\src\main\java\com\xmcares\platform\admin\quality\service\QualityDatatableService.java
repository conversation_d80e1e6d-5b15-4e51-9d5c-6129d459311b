package com.xmcares.platform.admin.quality.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.platform.admin.asset.model.MetaDatasource;
import com.xmcares.platform.admin.asset.model.MetaDatatable;
import com.xmcares.platform.admin.quality.model.QltyDim;
import com.xmcares.platform.admin.quality.model.QltyRule;
import com.xmcares.platform.admin.quality.model.QltyRuleScheduler;
import com.xmcares.platform.admin.quality.model.vo.QualityDatatableListQueryVO;
import com.xmcares.platform.admin.quality.repository.QltyMetricsController;
import com.xmcares.platform.admin.quality.repository.QualityDatatableRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/10 10:07
 */
@Service
public class QualityDatatableService {
    private static final Logger LOG = LoggerFactory.getLogger(QualityDatatableService.class);
    private static final Base64.Encoder base64Encoder = Base64.getEncoder();
    @Autowired
    QualityDatatableRepository qualityDatatableRepository;
    @Resource
    QltyMetricsController qltyMetricsController;

    public Page<QualityDatatableListQueryVO> listQuery(String datawareId, String datatableId, String datatableAlias, Integer pageNo, Integer pageSize) {
        return qualityDatatableRepository.listQuery(datawareId, datatableId, datatableAlias, pageNo, pageSize);
    }

    public Page<QltyRule> ruleListQuery(String datatableId, String ruleName, String ruleLevel, String dimCode, Integer pageNo, Integer pageSize) {
        return qualityDatatableRepository.ruleListQuery(datatableId, ruleName, ruleLevel, dimCode, pageNo, pageSize);
    }

    public String ruleAdd(QltyRule qltyRule) {
        return qualityDatatableRepository.ruleAdd(qltyRule);
    }

    public String schedulerSave(QltyRuleScheduler qltyRuleScheduler) {
        return qualityDatatableRepository.schedulerSave(qltyRuleScheduler);
    }

    public String ruleUpdate(QltyRule qltyRule) {
        return qualityDatatableRepository.ruleUpdate(qltyRule);
    }

    public String ruleDelete(String ruleId) {
        return qualityDatatableRepository.ruleDelete(ruleId);
    }

    public String ruleControl(String ruleId, String disabled) {
        return qualityDatatableRepository.ruleControl(ruleId, disabled);
    }

    public String schedulerRun(String datawareId, String datatableId, String started) {
        return qualityDatatableRepository.schedulerRun(datawareId, datatableId, started);
    }

    public String ruleTestRun(String[] ruleIds) {
        /*
        {
          "platform" : {
            "datasource" : {
              "driverClassName" : "com.mysql.cj.jdbc.Driver",
              "password" : "root",
              "url" : "************************************************************************************************************************************************************************",
              "username" : "root"
            }
          },
          "business" : {
            "datatableId" : "ct01",
            "jobTriggerTime" : "",
            "datawareId" : "1",
            "jobId" : "scheduler1",
            "jobName" : "TEST"
          }
        }
         */
        if (ruleIds.length > 0){
            MetaDatasource datasource = qualityDatatableRepository.getDatasourceByRuleId(ruleIds[0]);
            MetaDatatable datatable = qualityDatatableRepository.getDatatableByRuleId(ruleIds[0]);

            JSONObject jo = JSON.parseObject(datasource.getOptions());

            JSONObject businessJsonNode = JSON.parseObject("{}");
            businessJsonNode.put("datatableId", datatable.getId());
            businessJsonNode.put("datawareId", datatable.getDataware_id());
            businessJsonNode.put("jobId", "TEST_SCHEDULER_".concat(UUID.randomUUID().toString()));
            businessJsonNode.put("jobName", SnowflakeGenerator.getNextId());
            businessJsonNode.put("testing", true);

            jo.put("business", businessJsonNode);

            try {
                String[] args = {
                        "--jsonParams",
                        base64Encoder.encodeToString(jo.toJSONString().getBytes("utf-8"))
                };
                qltyMetricsController.metricsFlinkLocalRun(args);
                return "true";
            } catch (Exception e) {
                LOG.warn("ruleTestRun", e);
            }
        }
        return "false";
    }

    public List<QltyRuleScheduler> schedulerAllQuery(String datawareId, String datatableId) {
        return qualityDatatableRepository.schedulerAllQuery(datawareId, datatableId);
    }

    public List<QltyRule> ruleAllQuery() {
        return qualityDatatableRepository.ruleAllQuery();
    }

    public List<QltyDim> dimAllQuery() {
        return qualityDatatableRepository.dimAllQuery();
    }
}
