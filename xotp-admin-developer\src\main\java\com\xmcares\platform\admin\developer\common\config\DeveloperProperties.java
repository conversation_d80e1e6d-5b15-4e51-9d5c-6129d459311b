package com.xmcares.platform.admin.developer.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

@ConfigurationProperties(prefix = "xbdp.developer")
@Configuration
public class DeveloperProperties {

    /** 文件服务器的根目录 */
    private String fileServerDir = "/xbdp/developer";
    /** 本地临时文件的根目录，默认为${user.dir}/tmp/developer */
    private String localTmpDir = System.getProperty("user.dir")+ "/tmp/developer";
    /** 调度器配置项 */
    @NestedConfigurationProperty
    private SchedulerProperties scheduler = new SchedulerProperties();

    public String getLocalTmpDir() {
        return localTmpDir;
    }

    public void setLocalTmpDir(String localTmpDir) {
        this.localTmpDir = localTmpDir;
    }

    public String getFileServerDir() {
        return fileServerDir;
    }

    public void setFileServerDir(String fileServerDir) {
        this.fileServerDir = fileServerDir;
    }

    public SchedulerProperties getScheduler() {
        return scheduler;
    }

    public void setScheduler(SchedulerProperties scheduler) {
        this.scheduler = scheduler;
    }

    /**
     * <AUTHOR> chenYG
     * @date : 2022/3/29 11:11
     */
    public static class SchedulerProperties {

        /** 调度任务执行时的告警邮箱 */
        private String alertEmail;
        /** 勾盒服务应用的名称 */
        private String group;

        public String getAlertEmail() {
            return alertEmail;
        }

        public void setAlertEmail(String alertEmail) {
            this.alertEmail = alertEmail;
        }

        public String getGroup() {
            return group;
        }

        public void setGroup(String group) {
            this.group = group;
        }
    }
}
