package com.xmcares.platform.admin.integrator.plugin.service;

import com.xmcares.framework.fsclient.FSTemplate;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.framework.fsclient.FileDescResult;
import com.xmcares.framework.fsclient.ftp.FtpFileDesc;
import com.xmcares.platform.admin.integrator.common.error.IntegratorException;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;

/**
 * FileClientService
 *
 * <AUTHOR>
 * @Descriptions FileClientService
 * @Date 2025/8/7 09:51
 */
@Service
public class FileClientService {

    private static final Logger logger = LoggerFactory.getLogger(FileClientService.class);
    private static final String JAR_SUFFIX = ".jar";

    @Resource
    private FSTemplate fsTemplate;

    /**
     * 上传文件到FTP服务器
     *
     * @param file 上传的文件
     * @param fileName 文件名
     * @param uploadFilePath 上传路径（必须以/开头和结尾）
     * @throws IOException IO异常
     */
    public String uploadFile(MultipartFile file, String fileName, String uploadFilePath) throws IOException {
        String finalFileName = fileName.endsWith(JAR_SUFFIX) ? fileName : fileName + JAR_SUFFIX;

        try {
            // 1. 规范化路径格式
            String normalizedPath = normalizeFtpPath(uploadFilePath);
            String fullStorePath = normalizedPath + finalFileName;

            // 2. 验证文件名和路径
            validateFileName(finalFileName);
            validatePath(normalizedPath);

            // 3. 构建FTP文件描述 - 关键修复点
            FileDesc fileDesc = FtpFileDesc.builder()
                    .fileName(fullStorePath)
                    .storeName(new String(fullStorePath.getBytes(StandardCharsets.UTF_8)))
                    .build();
            logger.info("准备上传文件: fileName={}, storeName={}", fullStorePath, finalFileName);
            fsTemplate.saveFile(fileDesc, file.getInputStream());
            return fullStorePath;
        } catch (Exception e) {
            logger.error("FTP文件上传异常", e);
            throw new IntegratorException("FTP文件上传异常" + e.getMessage());
        }
    }

    /**
     * 从FTP服务器下载文件
     */
    public void downloadFile(String remotePath, OutputStream outputStream) {

        try {
            // 1. 规范化路径格式
            String normalizedPath = normalizeFtpPath(remotePath);

            FileDesc fileDesc = FtpFileDesc.builder()
                    .storeName(new String(normalizedPath.getBytes(StandardCharsets.UTF_8)))
                    .build();

            FileDescResult result = fsTemplate.loadFile(fileDesc, outputStream);

            if (result != null && result.isSuccess()) {
                logger.info("FTP文件下载成功: {}", normalizedPath);
            }


        } catch (Exception e) {
            logger.error("FTP文件下载异常: {}", remotePath, e);
            throw new IntegratorException("FTP文件下载异常:" + e.getMessage());
        }
    }

    /**
     * 删除FTP服务器上的文件
     */
    public boolean deleteFile(String remotePath) {
        try {
            // 1. 规范化路径格式
            String normalizedPath = normalizeFtpPath(remotePath);

            FileDesc fileDesc = FtpFileDesc.builder()
                    .storeName(new String(normalizedPath.getBytes(StandardCharsets.UTF_8)))
                    .build();

            fsTemplate.deleteFile(fileDesc);
            logger.info("FTP文件删除成功: {}", normalizedPath);
            return true;
        } catch (Exception e) {
            logger.error("FTP文件删除失败: {}", remotePath, e);
            return false;
        }
    }

    /**
     * 规范化FTP路径格式
     *
     * @param path 原始路径
     * @return 规范化后的路径
     */
    private String normalizeFtpPath(String path) {
        if (path == null || path.trim().isEmpty()) {
            return "/";
        }

        // 移除Windows路径分隔符，统一使用Unix格式
        String normalized = path.replace("\\", "/");

        // 确保以/开头
        if (!normalized.startsWith("/")) {
            normalized = "/" + normalized;
        }

        // 如果是目录路径，确保以/结尾
        if (!normalized.endsWith("/") && !normalized.contains(".")) {
            normalized = normalized + "/";
        }

        // 移除重复的/
        normalized = normalized.replaceAll("/+", "/");

        return normalized;
    }

    /**
     * 验证文件名是否合法
     *
     * @param fileName 文件名
     */
    private void validateFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        // 检查文件名中的非法字符
        String[] illegalChars = {"\\", "/", ":", "*", "?", "\"", "<", ">", "|"};
        for (String illegalChar : illegalChars) {
            if (fileName.contains(illegalChar)) {
                throw new IllegalArgumentException("文件名包含非法字符: " + illegalChar);
            }
        }

        // 检查文件名长度
        if (fileName.length() > 255) {
            throw new IllegalArgumentException("文件名过长，最大支持255个字符");
        }
    }

    /**
     * 验证路径是否合法
     *
     * @param path 路径
     */
    private void validatePath(String path) {
        if (path == null) {
            throw new IllegalArgumentException("路径不能为空");
        }

        // 检查路径遍历攻击
        if (path.contains("../") || path.contains("..\\")) {
            throw new IllegalArgumentException("路径包含非法的相对路径引用");
        }

        // 检查路径长度
        if (path.length() > 1000) {
            throw new IllegalArgumentException("路径过长");
        }
    }
}
