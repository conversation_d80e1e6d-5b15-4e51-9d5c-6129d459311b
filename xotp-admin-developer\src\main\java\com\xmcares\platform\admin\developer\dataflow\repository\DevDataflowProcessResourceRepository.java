package com.xmcares.platform.admin.developer.dataflow.repository;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.platform.admin.developer.dataflow.model.DevDataflowProcessResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class DevDataflowProcessResourceRepository {

    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    public boolean batchSave(List<DevDataflowProcessResource> resources) {
        List<Object[]> params = new ArrayList<>();
        String sql = null;
        for (DevDataflowProcessResource datasourceColumn : resources) {
            Map<String, Object> map = DBUtils.insertSqlAndObjects(datasourceColumn, DevDataflowProcessResource.class, DevDataflowProcessResource.TABLE);
            params.add(DBUtils.getObjects(map));
            if (sql == null) {
                sql = DBUtils.getSql(map);
            }
        }
        assert sql != null;
        return xcfJdbcTemplate.batchUpdate(sql, params)[0] > 0;
    }

    /**
     * 根据流程实例编号删除
     * @param processId 流程实例编号
     * @return
     */
    public boolean deleteByProcessId(String  processId) {
        String sql = "DELETE from " + DevDataflowProcessResource.TABLE + " where process_id= ? ";
        return  xcfJdbcTemplate.update(sql, processId)>0;
    }

    public List<DevDataflowProcessResource> queryDevDataflowProcessResourcePage(DevDataflowProcessResource devDataflowResource, Page<Serializable> page) {
        Map<String, Object> conditions = buildCondition(devDataflowResource);
        Map<String, Object> map = DBUtils.queryList(DevDataflowProcessResource.TABLE, conditions);
        String sql = DBUtils.getSql(map) + " ORDER BY upload_time Desc ";
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForEntities(sql, args, page, DevDataflowProcessResource.class);
    }

    private Map<String, Object> buildCondition(DevDataflowProcessResource DevDataflowProcessResource) {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("process_id", DevDataflowProcessResource.getProcessId());
        return conditions;
    }

    public int countDevDataflowProcessResource(DevDataflowProcessResource devDataflowResource) {
        Map<String, Object> conditions = buildCondition(devDataflowResource);
        Map<String, Object> map = DBUtils.queryCount(DevDataflowProcessResource.TABLE, conditions);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.queryForObject(sql, Integer.class, args);
    }
}
