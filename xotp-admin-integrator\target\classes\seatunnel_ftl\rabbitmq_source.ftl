{
"plugin_name": "Rabbitmq",
"plugin_output": "${source.plugin_output!'default_source_output'}",
<#-- --- 在此处直接进行内联解析 --- -->
<#-- 1. 优先从 serverHost 和 serverport 取值 -->
<#if orginDatasource.serverHost?has_content>
  <#assign host = orginDatasource.serverHost>
<#else>
<#-- 检查URL是否存在，并移除协议前缀 -->
  <#if orginDatasource.url?has_content>
    <#if orginDatasource.url?contains("://")>
      <#assign clean_url = orginDatasource.url?substring(orginDatasource.url?index_of("://") + 3)>
    <#else>
      <#assign clean_url = orginDatasource.url>
    </#if>

  <#-- 分离 host -->
    <#assign colon_index = clean_url?last_index_of(":")>
    <#if colon_index != -1>
      <#assign host = clean_url?substring(0, colon_index)>
    <#else>
      <#assign host = clean_url>
    </#if>
  <#else>
  <#-- 处理url为空的边界情况 -->
    <#assign host = "">
  </#if>
</#if>
<#-- 2. 优先从 serverport 取值 -->
<#if orginDatasource.serverport?has_content>
  <#assign port = orginDatasource.serverport?string>
<#else>
<#-- 从URL中解析端口 -->
  <#if orginDatasource.url?has_content>
    <#if orginDatasource.url?contains("://")>
      <#assign clean_url = orginDatasource.url?substring(orginDatasource.url?index_of("://") + 3)>
    <#else>
      <#assign clean_url = orginDatasource.url>
    </#if>

    <#assign colon_index = clean_url?last_index_of(":")>
    <#if colon_index != -1>
      <#assign port = clean_url?substring(colon_index + 1)>
    <#else>
      <#assign port = "5672"> <#-- 默认端口 -->
    </#if>
  <#else>
    <#assign port = "5672"> <#-- 默认端口 -->
  </#if>
</#if>
"host": "${host}",
"port": "${port}",
<#-- --- 解析结束，下面是其他参数 --- -->
  "virtual_host": "${orginDatasource.virtual_host!'/'}",
  "username": "${orginDatasource.username}",
  "password": "${orginDatasource.password}",
  "queue_name": "${orgin.topic}",
  "format": "${orgin.format!'json'}",
  "durable": ${(dest.durable)!true?string('true', 'false')},
  "exclusive": ${(dest.exclusive)!false?string('true', 'false')},
  "auto_delete": ${(dest.auto_delete)!false?string('true', 'false')},
  "schema": {
    "fields": {
    <#-- schema字段从orginColumns动态生成 -->
    <#list orginColumns as column>
        "${column.title}": "${column.type}"<#if column_has_next>,</#if>
    </#list>
    }
  }
}
