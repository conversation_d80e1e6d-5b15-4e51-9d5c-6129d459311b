/*
 * Copyright (c) 2025 XMCares Co., LTD.
 * All rights reserved.
 *
 * Created on 2025/5/22
 */
package com.xmcares.platform.admin.common.datasource;

/**
 * 数据源模型分组
 * <AUTHOR>
 * @since 1.0.0
 */
public interface DataSourceType<T extends DataSource> {

    String getTypeName();

    DataSourceGroup getGroup();

    default boolean equalsTypeName(String typeName) {
        return this.getTypeName() != null && this.getTypeName().equalsIgnoreCase(typeName);
    }

     T createDataSource(DataSourceOptions options);


}
