package com.xmcares.platform.admin.quality.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/12 16:48
 */
@ApiModel(value = QltyRuleTmpl.TABLE, description = "数据质量规则模板")
public class QltyRuleTmpl implements Serializable {
    public static final String TABLE = "bdp_qlty_rule_tmpl";

    @ApiModelProperty(value = "模板ID")
    private String id;

    @ApiModelProperty(value = "模板类型（SYS：系统，USR：用户自定义）")
    private String type;

    @ApiModelProperty(value = "规则名称")
    private String rule_name;

    @ApiModelProperty(value = "规则级别（TAB：表级，COL：字段）")
    private String rule_level;

    @ApiModelProperty(value = "规则配置项定义")
    private String rule_options_def;

    @ApiModelProperty(value = "函数调用表达式")
    private String rule_func_expr;

    @ApiModelProperty(value = "质量维度编码")
    private String dim_code;

    @ApiModelProperty(value = "检查(值比较)方式")
    private String check_type;

    @ApiModelProperty(value = "检查配置项定义")
    private String check_options_def;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "create_user")
    private String create_user;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "create_time")
    private Date create_time;

    @ApiModelProperty(value = "update_user")
    private String update_user;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "update_time")
    private Date update_time;

    public static String getTABLE() {
        return TABLE;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRule_name() {
        return rule_name;
    }

    public void setRule_name(String rule_name) {
        this.rule_name = rule_name;
    }

    public String getRule_level() {
        return rule_level;
    }

    public void setRule_level(String rule_level) {
        this.rule_level = rule_level;
    }

    public String getRule_options_def() {
        return rule_options_def;
    }

    public void setRule_options_def(String rule_options_def) {
        this.rule_options_def = rule_options_def;
    }

    public String getRule_func_expr() {
        return rule_func_expr;
    }

    public void setRule_func_expr(String rule_func_expr) {
        this.rule_func_expr = rule_func_expr;
    }

    public String getDim_code() {
        return dim_code;
    }

    public void setDim_code(String dim_code) {
        this.dim_code = dim_code;
    }

    public String getCheck_type() {
        return check_type;
    }

    public void setCheck_type(String check_type) {
        this.check_type = check_type;
    }

    public String getCheck_options_def() {
        return check_options_def;
    }

    public void setCheck_options_def(String check_options_def) {
        this.check_options_def = check_options_def;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreate_user() {
        return create_user;
    }

    public void setCreate_user(String create_user) {
        this.create_user = create_user;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_user() {
        return update_user;
    }

    public void setUpdate_user(String update_user) {
        this.update_user = update_user;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }
}
