package com.xmcares.platform.admin.dataservice.dataset.repository;

import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.SnowflakeGenerator;

/**
 * 
 * <AUTHOR> <<EMAIL>>
 * @version 1.0
 * @since: 2025-05-22
 */
@Repository
public class ServiceRepository {
    public static final String TABLE_NAME = "sys_service";

    private static final Logger LOG = LoggerFactory.getLogger(ServiceModelRepository.class);

    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    @Autowired
    com.xmcares.framework.sharing.repository.ServiceRepository serviceRepository;

    /**
     * 创建服务
     * 
     * @param category
     * @param serviceName
     * @param serviceUri
     * @return
     */
    public String createService(String category, String serviceName, String serviceUri) {
        String id = SnowflakeGenerator.getNextId().toString();
        Date nowDate = new Date();
        String sql = "insert into " + TABLE_NAME
                + " (id, category, service_name, uri, service_status, create_time, update_time) values (?, ?, ?, ?, ?, ?, ?)";
        int update = xcfJdbcTemplate.update(sql, id, category, serviceName, serviceUri, 1, nowDate,
                nowDate);
        if (update > 0) {
            return id;
        } else {
            return null;
        }
    }

    /**
     * 检查服务名是否存在
     * 
     * @param serviceName
     * @return
     */
    public boolean checkServiceNameUnique(String serviceName) {
        String sql = "select count(1) from " + TABLE_NAME + " where service_name = ?";
        int count = xcfJdbcTemplate.queryForObject(sql, Integer.class, serviceName);
        return count > 0;
    }

    /**
     * 更新服务状态
     * 服务上下线 update status
     * 
     * @param serviceName
     * @param status      0 未生效 / 1 生效
     * @return
     */
    public boolean updateStatusByName(String serviceName, int status) {
        if (status != 1) {
            status = 0;
        }
        String sql = "update " + TABLE_NAME + " set service_status = ? where service_name = ?";
        int update = xcfJdbcTemplate.update(sql, status, serviceName);
        return update > 0;
    }

    /**
     * 删除服务
     * 
     * @param id
     * @return
     */
    public int deleteService(String id) {
        return serviceRepository.deleteService(id);
    }
}