package com.xmcares.platform.admin.developer.dataflow.repository.scheduler;

import com.xmcares.platform.admin.developer.dataflow.vo.AddProcessTaskVo;
import com.xmcares.platform.admin.common.vo.ReturnT;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "${xbdp.feign.scheduler-service.name:scheduler-service}",
        url = "${xbdp.feign.scheduler-service.url:}"
)
public interface XbdpProcessClient {

    @PostMapping("/xbdp/process/api/add")
    ReturnT<Boolean> add(@RequestBody List<AddProcessTaskVo> tasks);

}
