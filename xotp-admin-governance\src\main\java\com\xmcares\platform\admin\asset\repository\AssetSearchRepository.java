package com.xmcares.platform.admin.asset.repository;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.error.BusinessException;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.platform.admin.asset.model.MetaDatatable;
import com.xmcares.platform.admin.asset.model.MetaDatatableColumn;
import com.xmcares.platform.admin.asset.model.MetaDataware;
import com.xmcares.platform.admin.asset.model.vo.JobPageQueryVO;
import com.xmcares.platform.admin.asset.model.vo.ServicePageQueryVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/28 08:23
 */
@Repository
public class AssetSearchRepository {
    private static final Logger LOG = LoggerFactory.getLogger(AssetSearchRepository.class);

    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    public static final String TABLE_BDP_META_DATAWARE = "bdp_meta_dataware";
    public static final String TABLE_BDP_META_DATATABLE = "bdp_meta_datatable";
    public static final String TABLE_BDP_META_DATATABLE_COLUMN = "bdp_meta_datatable_column";
    public static final String TABLE_BDP_ASST_STATS_METRICS = "bdp_asst_stats_metrics";
    public static final String TABLE_BDP_ASST_STATS_SCHEDULER = "bdp_asst_stats_scheduler";
    public static final String SYS_SERVICE = "sys_service";

    public List<MetaDataware> datawareListQuery() {
        try {
            List<MetaDataware> metaDatawares = xcfJdbcTemplate.<MetaDataware>queryForEntities(
                    " select * " +
                            " from " + TABLE_BDP_META_DATAWARE,
                    new Object[]{},
                    MetaDataware.class
            );

            return metaDatawares;
        } catch (Exception e) {
            LOG.warn("datawareListQuery", e);
            throw new BusinessException("{'error':'"+ e +"'}");
        }
    }


    public List<MetaDatatable> datatableListQuery(String datawareId) {
        try {
            List<MetaDatatable> metaDatatables = xcfJdbcTemplate.<MetaDatatable>queryForEntities(
                    " select * " +
                            " from " + TABLE_BDP_META_DATATABLE +
                            " where dataware_id='" + datawareId + "'",
                    new Object[]{},
                    MetaDatatable.class
            );

            return metaDatatables;
        } catch (Exception e) {
            LOG.warn("datatableListQuery", e);
            throw new BusinessException("{'error':'"+ e +"'}");
        }
    }

    public Page<MetaDatatable> datatablePageQuery(String name, String datawareId, Integer pageNo, Integer pageSize) {
        try {
            if (null == datawareId) {
                datawareId = "";
            }
            if (null == name) {
                name = "_";
            }
            Page<MetaDatatable> bdpMetaDatatablePage = new Page<MetaDatatable>();
            List<MetaDatatable> metaDatatables = xcfJdbcTemplate.<MetaDatatable>queryForEntities(
                    " select * " +
                            " from " + TABLE_BDP_META_DATATABLE +
                            " where " +
                            " dataware_id='" + datawareId + "' or" +
                            " name like '%" + name + "%' " +
                            " limit  " + (pageNo * pageSize) + "," + pageSize,
                    new Object[]{},
                    MetaDatatable.class
            );
            Integer total = xcfJdbcTemplate.<Integer>queryForObject(
                    "SELECT " +
                            " IFNULL(( " +
                            " select " +
                            " COUNT(*) all_count" +
                            " FROM " + TABLE_BDP_META_DATATABLE +
                            " where " +
                            " dataware_id='" + datawareId + "' or" +
                            " name like '%" + name + "%' " +
                            " ),0 ) all_count "
                    ,
                    Integer.class,
                    new Object[]{}
            );
            bdpMetaDatatablePage.setData(metaDatatables);
            bdpMetaDatatablePage.setPageNo(pageNo);
            bdpMetaDatatablePage.setPageSize(pageSize);
            bdpMetaDatatablePage.setTotal(total);
            return bdpMetaDatatablePage;
        } catch (Exception e) {
            LOG.warn("datatablePageQuery", e);
            throw new BusinessException("{'error':'"+ e +"'}");
        }
    }

    public Page<MetaDatatableColumn> datatableColumnPageQuery(String name, String datawareId, String datatableId, Integer pageNo, Integer pageSize) {
        try {
            if (null == datawareId) {
                datawareId = "";
            }
            if (null == name) {
                name = "";
            }
            Page<MetaDatatableColumn> bdpMetaDatatableColumnPage = new Page<MetaDatatableColumn>();
            List<MetaDatatableColumn> metaDatatableColumns = xcfJdbcTemplate.<MetaDatatableColumn>queryForEntities(
                    " select " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".id id, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".code code, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".name name, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".alisa alisa, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".datasource_id datasource_id, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".datatable_id datatable_id, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".type type, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".length length, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".accuracy accuracy, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".allow_empty allow_empty, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".column_key column_key, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".has_system has_system, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".remark remark, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".create_user create_user, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".create_time create_time, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".update_user update_user, " +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".update_time update_time, " +
                            " " + TABLE_BDP_META_DATATABLE + ".name datatable_name " +
                            " from " + TABLE_BDP_META_DATATABLE_COLUMN +
                            " join " + TABLE_BDP_META_DATATABLE + " on " + TABLE_BDP_META_DATATABLE + ".id=" + TABLE_BDP_META_DATATABLE_COLUMN + ".datatable_id and " +
                            " " + TABLE_BDP_META_DATATABLE + ".dataware_id='" + datawareId + "' and " + TABLE_BDP_META_DATATABLE + ".id='" + datatableId + "'" +
                            " where " +
                            " dataware_id='" + datawareId + "' or" +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".name like '%" + name + "%' " +
                            " limit  " + (pageNo * pageSize) + "," + pageSize,
                    new Object[]{},
                    MetaDatatableColumn.class
            );
            Integer total = xcfJdbcTemplate.<Integer>queryForObject(
                    " select " +
                            " IFNULL(( " +
                            " select " +
                            " COUNT(*) all_count " +
                            " from " + TABLE_BDP_META_DATATABLE_COLUMN +
                            " join " + TABLE_BDP_META_DATATABLE + " on " + TABLE_BDP_META_DATATABLE + ".id=" + TABLE_BDP_META_DATATABLE_COLUMN + ".datatable_id and " +
                            " " + TABLE_BDP_META_DATATABLE + ".dataware_id='" + datawareId + "' and " + TABLE_BDP_META_DATATABLE + ".id='" + datatableId + "'" +
                            " where " +
                            " dataware_id='" + datawareId + "' or" +
                            " " + TABLE_BDP_META_DATATABLE_COLUMN + ".name like '%" + name + "%' " +
                            " ),0 ) all_count "
                    ,
                    Integer.class,
                    new Object[]{}
            );
            bdpMetaDatatableColumnPage.setData(metaDatatableColumns);
            bdpMetaDatatableColumnPage.setPageNo(pageNo);
            bdpMetaDatatableColumnPage.setPageSize(pageSize);
            bdpMetaDatatableColumnPage.setTotal(total);
            return bdpMetaDatatableColumnPage;
        } catch (Exception e) {
            LOG.warn("datatableColumnPageQuery", e);
            throw new BusinessException("{'error':'"+ e +"'}");
        }
    }

    public Page<ServicePageQueryVO> servicePageQuery(String name, String datawareId, Integer pageNo, Integer pageSize) {
        try {
            if (null == datawareId) {
                datawareId = "";
            }
            if (null == name) {
                name = "";
            }
            Page<ServicePageQueryVO> servicePageQueryVOPage = new Page<ServicePageQueryVO>();
            List<ServicePageQueryVO> servicePageQueryVOS = xcfJdbcTemplate.<ServicePageQueryVO>queryForEntities(
                    " select  " +
                            " " + SYS_SERVICE + ".id id, " +
                            " " + SYS_SERVICE + ".service_name name, " +
                            " " + SYS_SERVICE + ".service_status isPublic, " +
                            " " + SYS_SERVICE + ".uri serviceUrl, " +
                            " IFNULL(" + TABLE_BDP_ASST_STATS_METRICS + ".metric_value, 0) hot, " +
                            " " + SYS_SERVICE + ".category remark, " +
                            " " + SYS_SERVICE + ".create_time createTime, " +
                            " " + SYS_SERVICE + ".update_time updateTime, " +
                            " " + SYS_SERVICE + ".category createUser " +
                            " from " + SYS_SERVICE +
                            " left join " + TABLE_BDP_ASST_STATS_METRICS + " on  " + TABLE_BDP_ASST_STATS_METRICS + ".stats_targets = " + SYS_SERVICE + ".service_name and " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".metric_name='dataapi_requests_total' " +
                            " where " +
                            " json_unquote(JSON_EXTRACT(" + TABLE_BDP_ASST_STATS_METRICS + ".metric_labels, '$.dataware_id'))='" + datawareId + "' or" +
                            " " + SYS_SERVICE + ".service_name like '%" + name + "%' " +
                            " limit  " + (pageNo * pageSize) + "," + pageSize,
                    new Object[]{},
                    ServicePageQueryVO.class
            );


            Integer total = xcfJdbcTemplate.<Integer>queryForObject(
                    " select  " +
                            " IFNULL(( " +
                            " select " +
                            " count(*) total " +
                            " from " + SYS_SERVICE +
                            " left join " + TABLE_BDP_ASST_STATS_METRICS + " on  " + TABLE_BDP_ASST_STATS_METRICS + ".stats_targets = " + SYS_SERVICE + ".service_name and " +
                            " " + TABLE_BDP_ASST_STATS_METRICS + ".metric_name='dataapi_requests_total' " +
                            " where " +
                            " json_unquote(JSON_EXTRACT(" + TABLE_BDP_ASST_STATS_METRICS + ".metric_labels, '$.dataware_id'))='" + datawareId + "' or" +
                            " " + SYS_SERVICE + ".service_name like '%" + name + "%' " +
                            " ),0 ) total "

                    ,
                    Integer.class,
                    new Object[]{}
            );
            servicePageQueryVOPage.setData(servicePageQueryVOS);
            servicePageQueryVOPage.setPageNo(pageNo);
            servicePageQueryVOPage.setPageSize(pageSize);
            servicePageQueryVOPage.setTotal(total);
            return servicePageQueryVOPage;
        } catch (Exception e) {
            LOG.warn("servicePageQuery", e);
            throw new BusinessException("{'error':'"+ e +"'}");
        }
    }
    public Page<JobPageQueryVO> jobPageQuery(String name, String status, Integer pageNo, Integer pageSize) {
        try {
            if (null == name) {
                name = "";
            }
            if (null == status) {
                status = "";
            }
            Page<JobPageQueryVO> jobPageQueryVOPage = new Page<JobPageQueryVO>();
            List<JobPageQueryVO> jobPageQueryVOS = xcfJdbcTemplate.<JobPageQueryVO>queryForEntities(
                    " select  " +
                            " " + TABLE_BDP_ASST_STATS_SCHEDULER + ".id id, " +
                            " " + TABLE_BDP_ASST_STATS_SCHEDULER + ".started status, " +
                            " " + TABLE_BDP_ASST_STATS_SCHEDULER + ".name remark, " +
                            " " + TABLE_BDP_ASST_STATS_SCHEDULER + ".create_time createTime, " +
                            " " + TABLE_BDP_ASST_STATS_SCHEDULER + ".update_time updateTime, " +
                            " " + TABLE_BDP_ASST_STATS_SCHEDULER + ".create_user createUser " +
                            " from " + TABLE_BDP_ASST_STATS_SCHEDULER +
                            " where " +
                            " " + TABLE_BDP_ASST_STATS_SCHEDULER + ".name like '%" + name + "%' and " +
                            " " + TABLE_BDP_ASST_STATS_SCHEDULER + ".started like '%" + status + "%' " +
                            " limit  " + (pageNo * pageSize) + "," + pageSize,
                    new Object[]{},
                    JobPageQueryVO.class
            );


            Integer total = xcfJdbcTemplate.<Integer>queryForObject(
                    " select  " +
                            " IFNULL(( " +
                            " select " +
                            " count(*) total " +
                            " from " + TABLE_BDP_ASST_STATS_SCHEDULER +
                            " where " +
                            " " + TABLE_BDP_ASST_STATS_SCHEDULER + ".name like '%" + name + "%'  and  " +
                            " " + TABLE_BDP_ASST_STATS_SCHEDULER + ".started like '%" + status + "%'  " +
                            " ),0 ) total "
                    ,
                    Integer.class,
                    new Object[]{}
            );
            jobPageQueryVOPage.setData(jobPageQueryVOS);
            jobPageQueryVOPage.setPageNo(pageNo);
            jobPageQueryVOPage.setPageSize(pageSize);
            jobPageQueryVOPage.setTotal(total);
            return jobPageQueryVOPage;
        } catch (Exception e) {
            LOG.warn("servicePageQuery", e);
            throw new BusinessException("{'error':'"+ e +"'}");
        }
    }

}
