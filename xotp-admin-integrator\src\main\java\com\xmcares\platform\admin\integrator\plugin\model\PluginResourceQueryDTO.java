package com.xmcares.platform.admin.integrator.plugin.model;

import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * PluginResourceQueryDTO
 *
 * <AUTHOR>
 * @Descriptions PluginResourceQueryDTO
 * @Date 2025/8/6 15:19
 */
public class PluginResourceQueryDTO {

    private int pageNum = 1;
    private int pageSize = 10;
    private String name;
    private String createUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private String pluginId;
    private String intgName;


    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getPluginId() {
        return pluginId;
    }

    public void setPluginId(String pluginId) {
        this.pluginId = pluginId;
    }

    public String getIntgName() {
        return intgName;
    }

    public void setIntgName(String intgName) {
        this.intgName = intgName;
    }
}
