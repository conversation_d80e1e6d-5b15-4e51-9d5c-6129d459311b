/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： zhanlh
 * Date：2022/7/20
 */
package com.xmcares.platform.admin.developer.dataflow.repository;

import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.framework.commons.util.DBUtils;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.developer.dataflow.model.DevDataflowResourceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 3.0.3
 */
@Repository
public class DevDataflowResourceMapperRepository {
    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    public List<DevDataflowResourceMapper> selectAllByUnSync() {
        String sql = "select * from " + DevDataflowResourceMapper.TABLE + " where synced = ? order by upload_time desc ";
        return xcfJdbcTemplate.queryForEntities(sql, new Object[]{YNEnum.NO.getIntCharCode()}, DevDataflowResourceMapper.class);
    }

    public List<DevDataflowResourceMapper> selectAll() {
        String sql = "select * from " + DevDataflowResourceMapper.TABLE + "  order by upload_time desc ";
        return xcfJdbcTemplate.queryForEntities(sql,new Object[]{}, DevDataflowResourceMapper.class);
    }

    public Boolean insertResourceMapper(DevDataflowResourceMapper devDataflowResourceMapper){
        Map map = DBUtils.insertSqlAndObjects(devDataflowResourceMapper, DevDataflowResourceMapper.class, DevDataflowResourceMapper.TABLE);
        String sql = DBUtils.getSql(map);
        Object[] args = DBUtils.getObjects(map);
        return xcfJdbcTemplate.update(sql, args) > 0;
    }

    public void delete(String id) {
        String sql = DBUtils.deleteSql(DevDataflowResourceMapper.TABLE, "id");
        xcfJdbcTemplate.update(sql, id);
    }

    public void updateToSyncByIds(List<String> ids) {
        String[] args = new String[ids.size() + 1];
        StringBuilder sqlBuilder = new StringBuilder(" UPDATE ").append(DevDataflowResourceMapper.TABLE).append(" SET synced=? ")
                .append(" WHERE id IN ");
        sqlBuilder.append("(").append("?");
        args[0] = YNEnum.YES.getIntCharCode();
        args[1] = ids.get(0);
        if (ids.size() > 1) {
            for (int i = 1; i < ids.size(); i++) {
                sqlBuilder.append(",").append("?");
                args[i + 1] = ids.get(i);
            }
        }
        sqlBuilder.append(")");
        xcfJdbcTemplate.update(sqlBuilder.toString(), args);
    }

    /**
     * 根据dataflowid更新bdp_dataflow_resource_mapper标志
     * @param dataflowid
     * @return
     */
    public Boolean deleteResourceMapper(String dataflowid){
        String sql = "update " + DevDataflowResourceMapper.TABLE + " set deleted=?, synced=? where id in(select id from bdp_dev_dataflow_resource where dataflow_id=?)";
        return xcfJdbcTemplate.update(sql, new Object[]{YNEnum.YES.getIntCharCode(),YNEnum.NO.getIntCode(), dataflowid}) > 0;
    }

    public Boolean updateToDeletedById(String id) {
        String sql = "update " + DevDataflowResourceMapper.TABLE + " set deleted=?, synced=? where id= ? ";
        return xcfJdbcTemplate.update(sql, new Object[]{YNEnum.YES.getIntCode(), YNEnum.NO.getIntCode(), id}) > 0;
    }
}
