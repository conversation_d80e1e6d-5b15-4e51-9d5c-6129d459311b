package com.xmcares.platform.admin.quality.repository;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.jdbc.XcfJdbcTemplate;
import com.xmcares.platform.admin.quality.model.QltyRuleTmpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/25 09:10
 */
@Repository
public class QualityRuleTmplRepository {
    private static final Logger LOG = LoggerFactory.getLogger(QualityRuleTmplRepository.class);
    public static final String TABLE_BDP_QLTY_RULE_TMPL = "bdp_qlty_rule_tmpl";
    @Autowired
    private XcfJdbcTemplate xcfJdbcTemplate;

    public Page<QltyRuleTmpl> pageQuery(String ruleName, String ruleLevel, String dimCode, Integer pageNo, Integer pageSize) {
        try {

            List<QltyRuleTmpl> qltyRuleTmpls = xcfJdbcTemplate.<QltyRuleTmpl>queryForEntities(
                    " select " +
                            " * " +
                            " from " + TABLE_BDP_QLTY_RULE_TMPL +
                            " where true and check_type != 'SCORE' " +
                            ((ruleName != null && !"".equals(ruleName)) ? " and rule_name like '%" + ruleName + "%' " : "  ") +
                            ((ruleLevel != null && !"".equals(ruleLevel)) ? (" and rule_level ='" + ruleLevel + "'") : "") +
                            ((dimCode != null && !"".equals(dimCode)) ? ("  and dim_code ='" + dimCode + "' ") : "") +
                            " limit  " + ((pageNo - 1) * pageSize) + "," + pageSize,
                    new Object[]{},
                    QltyRuleTmpl.class
            );
            Integer total = xcfJdbcTemplate.<Integer>queryForObject(
                    " SELECT " +
                            " IFNULL(( " +
                            " select " +
                            " COUNT(*) all_count" +
                            " from " + TABLE_BDP_QLTY_RULE_TMPL +
                            " where true and check_type != 'SCORE' " +
                            ((ruleName != null && !"".equals(ruleName)) ? " and rule_name like '%" + ruleName + "%' " : "  ") +
                            ((ruleLevel != null && !"".equals(ruleLevel)) ? (" and rule_level ='" + ruleLevel + "'") : "") +
                            ((dimCode != null && !"".equals(dimCode)) ? ("  and dim_code ='" + dimCode + "' ") : "") +
                            " ),0 ) all_count "
                    ,
                    Integer.class,
                    new Object[]{}
            );
            Page<QltyRuleTmpl> qltyRuleTmplPage = new Page<QltyRuleTmpl>();
            qltyRuleTmplPage.setData(qltyRuleTmpls);
            qltyRuleTmplPage.setPageNo(pageNo);
            qltyRuleTmplPage.setPageSize(pageSize);
            qltyRuleTmplPage.setTotal(total);
            return qltyRuleTmplPage;
        } catch (Exception e) {
            LOG.warn("pageQuery", e);
            return null;
        }
    }

    public List<QltyRuleTmpl> allQuery() {
        try {

            List<QltyRuleTmpl> qltyRuleTmpls = xcfJdbcTemplate.<QltyRuleTmpl>queryForEntities(
                    " select " +
                            " * " +
                            " from " + TABLE_BDP_QLTY_RULE_TMPL +
                            " where  check_type != 'SCORE' "
                    ,
                    new Object[]{},
                    QltyRuleTmpl.class
            );
            return qltyRuleTmpls;
        } catch (Exception e) {
            LOG.warn("pageQuery", e);
            return null;
        }
    }
}
