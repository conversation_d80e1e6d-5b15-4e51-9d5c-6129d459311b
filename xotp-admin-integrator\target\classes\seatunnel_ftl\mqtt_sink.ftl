<#-- mqtt_sink.ftl -->
{
  "plugin_name": "Mqtt",
  <#-- 如果有 transform, 则消费 transform 的输出, 否则消费 source 的输出 -->
  <#if fieldMappings?? && fieldMappings?has_content>
      "plugin_input": "${sink.plugin_input!'default_transform_output'}",
  <#else>
      "plugin_input": "${sink.plugin_input!'default_source_output'}",
  </#if>
  "broker.urls": [
  <#list destDatasource.url?split(',') as url>
      "${url?trim}"<#if url_has_next>,</#if>
  </#list>
  ],
  "topic": "${dest.topic}",
  "qos": ${(dest.qos)!'1'},
  "auto.reconect": ${(dest.retained)!true?string('true', 'false')},
  "max.inflight.messages": "${dest.maxInflightMessages}",
  "retained": ${(dest.retained)!false?string('true', 'false')},
  "message.format": "${dest.messageFormat!'json'}"
  <#-- ==================== 条件渲染区 开始 ==================== -->
  <#-- 只有当 username 存在且不为空时，才生成 "username" 字段 -->
  <#if destDatasource.username?has_content>
      ,"username": "${destDatasource.username}"
  </#if>

  <#-- 只有当 password 存在且不为空时，才生成 "password" 字段 -->
  <#if destDatasource.password?has_content>
      ,"password": "${destDatasource.password}"
  </#if>

  <#-- 只有当 client_id 存在且不为空时，才生成 "client.id" 字段 -->
  <#if dest.client_id?has_content>
      ,"client.id": "${dest.client_id}"
  </#if>
}
