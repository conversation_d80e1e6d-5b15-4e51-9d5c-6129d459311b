package com.xmcares.platform.admin.developer.dataflow.core.node;


import com.xmcares.platform.admin.common.errors.SystemException;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/30 10:26
 **/
public interface ITaskNodeManager<R extends IGroupDAGNode> {

    /**
     * 对所有节点进行校验
     * @throws SystemException
     */
    void check() throws SystemException;

    /**
     * 节点信息列表
     * @return
     */
    List<R> nodeInfos();

}
