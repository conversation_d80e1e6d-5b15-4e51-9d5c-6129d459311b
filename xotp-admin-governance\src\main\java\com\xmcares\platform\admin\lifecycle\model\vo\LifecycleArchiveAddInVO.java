package com.xmcares.platform.admin.lifecycle.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value = "LifecycleArchiveAddInVO", description = "添加归档数据")
public class LifecycleArchiveAddInVO implements Serializable {

    @ApiModelProperty(value = "需要归档的表的ID")
    private String datatableId;
    @ApiModelProperty(value = "仓库ID")
    private String datawareId;
    @ApiModelProperty(value = "归档调度ID")
    private String archiveSchedulerId;
    @ApiModelProperty(value = "表名称")
    private String datatableName;
    @ApiModelProperty(value = "归档条件表达式")
    private String archiveRangeExpr;
    @ApiModelProperty(value = "是否删除原有表数据")
    private String datatableReduced;
    @ApiModelProperty(value = "启用近线归档，0：否1：是")
    private String nearlined;
    @ApiModelProperty(value = "近线归档数据源ID")
    private String nearlineDatasourceId;
    @ApiModelProperty(value = "近线归档数据源名称")
    private String nearlineDatasourceName;
    @ApiModelProperty(value = "近线归档数据表名称")
    private String nearlineDatatableName;
    @ApiModelProperty(value = "启用离线归档，0：否1：是")
    private String offlined;
    @ApiModelProperty(value = "离线归档文件名")
    private String offlineFileName;
    @ApiModelProperty(value = "离线保留天数")
    private String offlineFileLifedays;

    @ApiModelProperty(value = "归档类型")
    private String archiveType;

    public String getDatatableId() {
        return datatableId;
    }

    public void setDatatableId(String datatableId) {
        this.datatableId = datatableId;
    }

    public String getDatawareId() {
        return datawareId;
    }

    public void setDatawareId(String datawareId) {
        this.datawareId = datawareId;
    }

    public String getArchiveSchedulerId() {
        return archiveSchedulerId;
    }

    public void setArchiveSchedulerId(String archiveSchedulerId) {
        this.archiveSchedulerId = archiveSchedulerId;
    }

    public String getDatatableName() {
        return datatableName;
    }

    public void setDatatableName(String datatableName) {
        this.datatableName = datatableName;
    }

    public String getArchiveRangeExpr() {
        return archiveRangeExpr;
    }

    public void setArchiveRangeExpr(String archiveRangeExpr) {
        this.archiveRangeExpr = archiveRangeExpr;
    }

    public String getDatatableReduced() {
        return datatableReduced;
    }

    public void setDatatableReduced(String datatableReduced) {
        this.datatableReduced = datatableReduced;
    }

    public String getNearlined() {
        return nearlined;
    }

    public void setNearlined(String nearlined) {
        this.nearlined = nearlined;
    }

    public String getNearlineDatasourceId() {
        return nearlineDatasourceId;
    }

    public void setNearlineDatasourceId(String nearlineDatasourceId) {
        this.nearlineDatasourceId = nearlineDatasourceId;
    }

    public String getNearlineDatasourceName() {
        return nearlineDatasourceName;
    }

    public void setNearlineDatasourceName(String nearlineDatasourceName) {
        this.nearlineDatasourceName = nearlineDatasourceName;
    }

    public String getNearlineDatatableName() {
        return nearlineDatatableName;
    }

    public void setNearlineDatatableName(String nearlineDatatableName) {
        this.nearlineDatatableName = nearlineDatatableName;
    }

    public String getOfflined() {
        return offlined;
    }

    public void setOfflined(String offlined) {
        this.offlined = offlined;
    }

    public String getOfflineFileName() {
        return offlineFileName;
    }

    public void setOfflineFileName(String offlineFileName) {
        this.offlineFileName = offlineFileName;
    }

    public String getOfflineFileLifedays() {
        return offlineFileLifedays;
    }

    public void setOfflineFileLifedays(String offlineFileLifedays) {
        this.offlineFileLifedays = offlineFileLifedays;
    }

    public String getArchiveType() {
        return archiveType;
    }

    public void setArchiveType(String archiveType) {
        this.archiveType = archiveType;
    }
}
