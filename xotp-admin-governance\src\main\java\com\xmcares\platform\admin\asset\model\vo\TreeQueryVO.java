package com.xmcares.platform.admin.asset.model.vo;

import com.xmcares.platform.admin.asset.model.MetaCatalog;
import com.xmcares.platform.admin.asset.model.MetaDatatable;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/9 13:35
 */
public class TreeQueryVO implements Serializable {

    private MetaCatalog metaCatalog;
    private List<MetaDatatable> datatables;
    private boolean isLeaf;

    private List<TreeQueryVO> childrenCatalogs;

    public MetaCatalog getMetaCatalog() {
        return metaCatalog;
    }

    public void setMetaCatalog(MetaCatalog metaCatalog) {
        this.metaCatalog = metaCatalog;
    }

    public List<MetaDatatable> getDatatables() {
        return datatables;
    }

    public void setDatatables(List<MetaDatatable> datatables) {
        this.datatables = datatables;
    }

    public boolean isLeaf() {
        return isLeaf;
    }

    public void setLeaf(boolean leaf) {
        isLeaf = leaf;
    }

    public List<TreeQueryVO> getChildrenCatalogs() {
        return childrenCatalogs;
    }

    public void setChildrenCatalogs(List<TreeQueryVO> childrenCatalogs) {
        this.childrenCatalogs = childrenCatalogs;
    }
}
