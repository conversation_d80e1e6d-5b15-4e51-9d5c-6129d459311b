/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2023/6/12
 */
package com.xmcares.platform.admin.common.datasource.mq.rocketmq;

import org.apache.rocketmq.client.ClientConfig;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 *
 * <AUTHOR>
 * @since 1.4.1
 */
public class RocketMqProperties extends ClientConfig{


    private String accessKey;

    private String secretKey;


    /**
     * 代理客户端拉取消息时，最大轮询秒数
     */
    @Min(10*1000) @Max(60*1000)
    private Long consumerPollingInMillis = 30*1000l;

    /**
     * 代理客户端拉取消息时，最大拉取消息数
     */
    @Min(8) @Max(1024)
    private Integer consumerPollingMaxRecords = 64;

    /**
     * 代理客户端启动时，从N小时前开始（用于长期未消费时，跳过历史数据）
     */
    @Max(72)
    private Integer consumerStartOffsetBeforeHours = 12;

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public Long getConsumerPollingInMills() {
        return consumerPollingInMillis;
    }

    public void setConsumerPollingInMillis(Long consumerPollingInMillis) {
        this.consumerPollingInMillis = consumerPollingInMillis;
    }

    public Integer getConsumerPollingMaxRecords() {
        return consumerPollingMaxRecords;
    }

    public void setConsumerPollingMaxRecords(Integer consumerPollingMaxRecords) {
        this.consumerPollingMaxRecords = consumerPollingMaxRecords;
    }

    public Integer getConsumerStartOffsetBeforeHours() {
        return consumerStartOffsetBeforeHours;
    }

    public void setConsumerStartOffsetBeforeHours(Integer consumerStartOffsetBeforeHours) {
        this.consumerStartOffsetBeforeHours = consumerStartOffsetBeforeHours;
    }

    @Override
    public String toString() {
        return "RocketMqProperties{" +
                "consumerPollingInMillis=" + consumerPollingInMillis +
                ", consumerPollingMaxRecords=" + consumerPollingMaxRecords +
                ", client={"+ super.toString() +"}" +
                '}';
    }
}




