package com.xmcares.platform.admin.common.database.metainfo;


/**
 * 原始jdbc字段对象
 *
 * <AUTHOR>
 * @ClassName DasColumn
 * @Version 1.0
 * @since 2019/7/17 16:29
 */
public class DasColumn {

    private String columnName;

    private String columnTypeName;

    private String columnClassName;

    private String columnComment;

    private int precision;

    private int scale;

    private int isNull;
    private boolean isprimaryKey;

    public DasColumn() {
    }

    public DasColumn(String columnName, String columnTypeName, String columnClassName, String columnComment, int precision, int scale, int isNull, boolean isprimaryKey) {
        this.columnName = columnName;
        this.columnTypeName = columnTypeName;
        this.columnClassName = columnClassName;
        this.columnComment = columnComment;
        this.precision = precision;
        this.scale = scale;
        this.isNull = isNull;
        this.isprimaryKey = isprimaryKey;
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public String getColumnTypeName() {
        return columnTypeName;
    }

    public void setColumnTypeName(String columnTypeName) {
        this.columnTypeName = columnTypeName;
    }

    public String getColumnClassName() {
        return columnClassName;
    }

    public void setColumnClassName(String columnClassName) {
        this.columnClassName = columnClassName;
    }

    public String getColumnComment() {
        return columnComment;
    }

    public void setColumnComment(String columnComment) {
        this.columnComment = columnComment;
    }

    public int getPrecision() {
        return precision;
    }

    public void setPrecision(int precision) {
        this.precision = precision;
    }

    public int getScale() {
        return scale;
    }

    public void setScale(int scale) {
        this.scale = scale;
    }

    public int getIsNull() {
        return isNull;
    }

    public void setIsNull(int isNull) {
        this.isNull = isNull;
    }

    public boolean isIsprimaryKey() {
        return isprimaryKey;
    }

    public void setIsprimaryKey(boolean isprimaryKey) {
        this.isprimaryKey = isprimaryKey;
    }
}
