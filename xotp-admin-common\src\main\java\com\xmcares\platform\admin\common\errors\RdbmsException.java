package com.xmcares.platform.admin.common.errors;

import com.xmcares.platform.admin.common.datasource.jdbc.JdbcType;
import com.xmcares.platform.admin.common.util.CommonConstants;

/**
 * RdbmsException
 *
 * <AUTHOR>
 * @ClassName RdbmsException
 * @Version 2.1.1
 * @since 2020/03/14 07:15
 */
public class RdbmsException extends DataXException{


    public RdbmsException(ErrorCode errorCode, String errorMessage) {
        super(errorCode, errorMessage);
    }

    public static DataXException asConnException(String dataBaseType, Exception e, String userName, String dbName){
        if (JdbcType.MYSQL.equalsTypeName(dataBaseType)){
            DatabaseErrorCode databaseErrorCode = mySqlConnectionErrorAna(e.getMessage());
            if (databaseErrorCode == DatabaseErrorCode.MYSQL_CONN_DB_ERROR && dbName !=null ){
                return DataXException.asDataXException(databaseErrorCode,"该数据库名称为："+dbName+" 具体错误信息为："+e);
            }
            if (databaseErrorCode == DatabaseErrorCode.MYSQL_CONN_USERPWD_ERROR ){
                return DataXException.asDataXException(databaseErrorCode,"该数据库用户名为："+userName+" 具体错误信息为："+e);
            }
            return DataXException.asDataXException(databaseErrorCode," 具体错误信息为："+e);
        }

        if (JdbcType.ORACLE.equalsTypeName(dataBaseType)){
            DatabaseErrorCode databaseErrorCode = oracleConnectionErrorAna(e.getMessage());
            if (databaseErrorCode == DatabaseErrorCode.ORACLE_CONN_DB_ERROR && dbName != null){
                return DataXException.asDataXException(databaseErrorCode,"该数据库名称为："+dbName+" 具体错误信息为："+e);
            }
            if (databaseErrorCode == DatabaseErrorCode.ORACLE_CONN_USERPWD_ERROR ){
                return DataXException.asDataXException(databaseErrorCode,"该数据库用户名为："+userName+" 具体错误信息为："+e);
            }
            return DataXException.asDataXException(databaseErrorCode," 具体错误信息为："+e);
        }
        return DataXException.asDataXException(DatabaseErrorCode.CONN_DB_ERROR," 具体错误信息为："+e);
    }

    public static DatabaseErrorCode mySqlConnectionErrorAna(String e){
        if (e.contains(CommonConstants.MYSQL_DATABASE)){
            return DatabaseErrorCode.MYSQL_CONN_DB_ERROR;
        }

        if (e.contains(CommonConstants.MYSQL_CONNEXP)){
            return DatabaseErrorCode.MYSQL_CONN_IPPORT_ERROR;
        }

        if (e.contains(CommonConstants.MYSQL_ACCDENIED)){
            return DatabaseErrorCode.MYSQL_CONN_USERPWD_ERROR;
        }

        return DatabaseErrorCode.CONN_DB_ERROR;
    }

    public static DatabaseErrorCode oracleConnectionErrorAna(String e){
        if (e.contains(CommonConstants.ORACLE_DATABASE)){
            return DatabaseErrorCode.ORACLE_CONN_DB_ERROR;
        }

        if (e.contains(CommonConstants.ORACLE_CONNEXP)){
            return DatabaseErrorCode.ORACLE_CONN_IPPORT_ERROR;
        }

        if (e.contains(CommonConstants.ORACLE_ACCDENIED)){
            return DatabaseErrorCode.ORACLE_CONN_USERPWD_ERROR;
        }

        return DatabaseErrorCode.CONN_DB_ERROR;
    }

    public static DataXException asQueryException(String dataBaseType, Exception e, String querySql, String table, String userName){
        if (JdbcType.MYSQL.equalsTypeName(dataBaseType)){
            DatabaseErrorCode databaseErrorCode = mySqlQueryErrorAna(e.getMessage());
            if (databaseErrorCode == DatabaseErrorCode.MYSQL_QUERY_TABLE_NAME_ERROR && table != null){
                return DataXException.asDataXException(databaseErrorCode,"表名为："+table+" 执行的SQL为:"+querySql+" 具体错误信息为："+e);
            }
            if (databaseErrorCode == DatabaseErrorCode.MYSQL_QUERY_SELECT_PRI_ERROR && userName != null){
                return DataXException.asDataXException(databaseErrorCode,"用户名为："+userName+" 具体错误信息为："+e);
            }

            return DataXException.asDataXException(databaseErrorCode,"执行的SQL为: "+querySql+" 具体错误信息为："+e);
        }

        if (JdbcType.ORACLE.equalsTypeName(dataBaseType)){
            DatabaseErrorCode databaseErrorCode = oracleQueryErrorAna(e.getMessage());
            if (databaseErrorCode == DatabaseErrorCode.ORACLE_QUERY_TABLE_NAME_ERROR && table != null){
                return DataXException.asDataXException(databaseErrorCode,"表名为："+table+" 执行的SQL为:"+querySql+" 具体错误信息为："+e);
            }
            if (databaseErrorCode == DatabaseErrorCode.ORACLE_QUERY_SELECT_PRI_ERROR){
                return DataXException.asDataXException(databaseErrorCode,"用户名为："+userName+" 具体错误信息为："+e);
            }

            return DataXException.asDataXException(databaseErrorCode,"执行的SQL为: "+querySql+" 具体错误信息为："+e);

        }

        return DataXException.asDataXException(DatabaseErrorCode.SQL_EXECUTE_FAIL, "执行的SQL为: "+querySql+" 具体错误信息为："+e);
    }

    public static DatabaseErrorCode mySqlQueryErrorAna(String e){
        if (e.contains(CommonConstants.MYSQL_TABLE_NAME_ERR1) && e.contains(CommonConstants.MYSQL_TABLE_NAME_ERR2)){
            return DatabaseErrorCode.MYSQL_QUERY_TABLE_NAME_ERROR;
        }else if (e.contains(CommonConstants.MYSQL_SELECT_PRI)){
            return DatabaseErrorCode.MYSQL_QUERY_SELECT_PRI_ERROR;
        }else if (e.contains(CommonConstants.MYSQL_COLUMN1) && e.contains(CommonConstants.MYSQL_COLUMN2)){
            return DatabaseErrorCode.MYSQL_QUERY_COLUMN_ERROR;
        }else if (e.contains(CommonConstants.MYSQL_WHERE)){
            return DatabaseErrorCode.MYSQL_QUERY_SQL_ERROR;
        }
        return DatabaseErrorCode.READ_RECORD_FAIL;
    }

    public static DatabaseErrorCode oracleQueryErrorAna(String e){
        if (e.contains(CommonConstants.ORACLE_TABLE_NAME)){
            return DatabaseErrorCode.ORACLE_QUERY_TABLE_NAME_ERROR;
        }else if (e.contains(CommonConstants.ORACLE_SQL)){
            return DatabaseErrorCode.ORACLE_QUERY_SQL_ERROR;
        }else if (e.contains(CommonConstants.ORACLE_SELECT_PRI)){
            return DatabaseErrorCode.ORACLE_QUERY_SELECT_PRI_ERROR;
        }
        return DatabaseErrorCode.READ_RECORD_FAIL;
    }

    public static DataXException asSqlParserException(String dataBaseType, Exception e, String querySql){
        if (JdbcType.MYSQL.equalsTypeName(dataBaseType)){
            throw DataXException.asDataXException(DatabaseErrorCode.MYSQL_QUERY_SQL_PARSER_ERROR, "执行的SQL为:"+querySql+" 具体错误信息为：" + e);
        }
        if (JdbcType.ORACLE.equalsTypeName(dataBaseType)){
            throw DataXException.asDataXException(DatabaseErrorCode.ORACLE_QUERY_SQL_PARSER_ERROR,"执行的SQL为:"+querySql+" 具体错误信息为：" +e);
        }
        throw DataXException.asDataXException(DatabaseErrorCode.READ_RECORD_FAIL,"执行的SQL为:"+querySql+" 具体错误信息为："+e);
    }

    public static DataXException asPreSQLParserException(String dataBaseType, Exception e, String querySql){
        if (JdbcType.MYSQL.equalsTypeName(dataBaseType)){
            throw DataXException.asDataXException(DatabaseErrorCode.MYSQL_PRE_SQL_ERROR, "执行的SQL为:"+querySql+" 具体错误信息为：" + e);
        }

        if (JdbcType.ORACLE.equalsTypeName(dataBaseType)){
            throw DataXException.asDataXException(DatabaseErrorCode.ORACLE_PRE_SQL_ERROR,"执行的SQL为:"+querySql+" 具体错误信息为：" +e);
        }
        throw DataXException.asDataXException(DatabaseErrorCode.READ_RECORD_FAIL,"执行的SQL为:"+querySql+" 具体错误信息为："+e);
    }

    public static DataXException asPostSQLParserException(String dataBaseType, Exception e, String querySql){
        if (JdbcType.MYSQL.equalsTypeName(dataBaseType)){
            throw DataXException.asDataXException(DatabaseErrorCode.MYSQL_POST_SQL_ERROR, "执行的SQL为:"+querySql+" 具体错误信息为：" + e);
        }

        if (JdbcType.ORACLE.equalsTypeName(dataBaseType)){
            throw DataXException.asDataXException(DatabaseErrorCode.ORACLE_POST_SQL_ERROR,"执行的SQL为:"+querySql+" 具体错误信息为：" +e);
        }
        throw DataXException.asDataXException(DatabaseErrorCode.READ_RECORD_FAIL,"执行的SQL为:"+querySql+" 具体错误信息为："+e);
    }

    public static DataXException asInsertPriException(String dataBaseType, String userName, String jdbcUrl){
        if (JdbcType.MYSQL.equalsTypeName(dataBaseType)){
            throw DataXException.asDataXException(DatabaseErrorCode.MYSQL_INSERT_ERROR, "用户名为:"+userName+" jdbcURL为："+jdbcUrl);
        }

        if (JdbcType.ORACLE.equalsTypeName(dataBaseType)){
            throw DataXException.asDataXException(DatabaseErrorCode.ORACLE_INSERT_ERROR,"用户名为:"+userName+" jdbcURL为："+jdbcUrl);
        }
        throw DataXException.asDataXException(DatabaseErrorCode.NO_INSERT_PRIVILEGE,"用户名为:"+userName+" jdbcURL为："+jdbcUrl);
    }

    public static DataXException asDeletePriException(String dataBaseType, String userName, String jdbcUrl){
        if (JdbcType.MYSQL.equalsTypeName(dataBaseType)){
            throw DataXException.asDataXException(DatabaseErrorCode.MYSQL_DELETE_ERROR, "用户名为:"+userName+" jdbcURL为："+jdbcUrl);
        }

        if (JdbcType.ORACLE.equalsTypeName(dataBaseType)){
            throw DataXException.asDataXException(DatabaseErrorCode.ORACLE_DELETE_ERROR,"用户名为:"+userName+" jdbcURL为："+jdbcUrl);
        }
        throw DataXException.asDataXException(DatabaseErrorCode.NO_DELETE_PRIVILEGE,"用户名为:"+userName+" jdbcURL为："+jdbcUrl);
    }

    public static DataXException asSplitPKException(String dataBaseType, Exception e, String splitSql, String splitPkID){
        if (JdbcType.MYSQL.equalsTypeName(dataBaseType)){

            return DataXException.asDataXException(DatabaseErrorCode.MYSQL_SPLIT_PK_ERROR,"配置的SplitPK为: "+splitPkID+", 执行的SQL为: "+splitSql+" 具体错误信息为："+e);
        }

        if (JdbcType.ORACLE.equalsTypeName(dataBaseType)){
            return DataXException.asDataXException(DatabaseErrorCode.ORACLE_SPLIT_PK_ERROR,"配置的SplitPK为: "+splitPkID+", 执行的SQL为: "+splitSql+" 具体错误信息为："+e);
        }

        return DataXException.asDataXException(DatabaseErrorCode.READ_RECORD_FAIL,splitSql+e);
    }
}
