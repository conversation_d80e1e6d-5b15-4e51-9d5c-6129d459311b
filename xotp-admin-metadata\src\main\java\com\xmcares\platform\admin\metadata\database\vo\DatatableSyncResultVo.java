package com.xmcares.platform.admin.metadata.database.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR> chenYG
 * @date : 2022/5/18 14:38
 */
@ApiModel(value = "DatatableSyncResultVo", description = "数据源表同步结果")
public class DatatableSyncResultVo {

    @ApiModelProperty(value = "表名称")
    private final String table;
    @ApiModelProperty(value = "操作类型", notes = "add: 新增 remove: 删除")
    private final String type;
    @ApiModelProperty(value = "操作结果", notes = "200：成功 其他：失败")
    private final String code;
    @ApiModelProperty(value = "提示信息")
    private final String message;

    private DatatableSyncResultVo(String table, String type, String code, String message) {
        this.table = table;
        this.type = type;
        this.code = code;
        this.message = message;
    }

    public String getTable() {
        return table;
    }

    public String getType() {
        return type;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }



    public static DatatableSyncResultVo addOk(String table) {
        return new DatatableSyncResultVo(table, "add", "200", "添加成功");
    }
    public static DatatableSyncResultVo removeOk(String table) {
        return new DatatableSyncResultVo(table, "remove", "200", "删除成功");
    }
    public static DatatableSyncResultVo addError(String table, String message) {
        return new DatatableSyncResultVo(table, "add", "500", message);
    }
    public static DatatableSyncResultVo removeError(String table, String message) {
        return new DatatableSyncResultVo(table, "remove", "500", message);
    }
}
