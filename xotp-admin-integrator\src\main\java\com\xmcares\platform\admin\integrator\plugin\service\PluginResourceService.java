package com.xmcares.platform.admin.integrator.plugin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xmcares.framework.commons.context.UserContextHolder;
import com.xmcares.framework.commons.util.SnowflakeGenerator;
import com.xmcares.framework.fsclient.FSClientProperties;
import com.xmcares.platform.admin.common.errors.BusinessException;
import com.xmcares.platform.admin.common.util.YNEnum;
import com.xmcares.platform.admin.integrator.common.error.IntegratorException;
import com.xmcares.platform.admin.integrator.plugin.mapper.PluginResourceMapper;
import com.xmcares.platform.admin.integrator.plugin.model.Datasync;
import com.xmcares.platform.admin.integrator.plugin.model.PluginResource;
import com.xmcares.platform.admin.integrator.plugin.model.PluginResourceQueryDTO;
import com.xmcares.platform.admin.integrator.plugin.model.PluginResourceVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.context.properties.bind.Bindable;


import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.*;
import java.util.jar.JarInputStream;
import java.util.jar.Manifest;
import java.util.stream.Collectors;

/**
 * PluginResourceService
 *
 * <AUTHOR>
 * @Descriptions PluginResourceService
 * @Date 2025/8/6 14:52
 */
@Service
@EnableConfigurationProperties(FSClientProperties.class)
public class PluginResourceService extends ServiceImpl<PluginResourceMapper, PluginResource> {
    private static final Logger logger = LoggerFactory.getLogger(PluginResourceService.class);

    private static final String FS_CLIENT_PREFIX = "xcnf.data.fsclient.";

    private final FSClientProperties baseProperties;
    private final Environment environment;

    @Value("${xbdp.integrator.file-server-root:/xotp/plugins}")
    private String fileServerRootPath;

    @Resource
    private FileClientService fileClientService;

    @Resource
    private PluginDatasyncService pluginDatasyncService;

    public PluginResourceService(FSClientProperties baseProperties, Environment environment) {
        this.baseProperties = baseProperties;
        this.environment = environment;
    }

    /**
     * 1. 分页查询插件资源
     *
     * @param queryDTO 查询参数 DTO
     * @return 分页后的插件资源列表
     */
    public Page<PluginResourceVO> pagePluginResources(PluginResourceQueryDTO queryDTO) {
        Page<PluginResource> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        QueryWrapper<PluginResource> queryWrapper = new QueryWrapper<>();

        queryWrapper.like(StringUtils.hasText(queryDTO.getName()), "name", queryDTO.getName());
        queryWrapper.eq(StringUtils.hasText(queryDTO.getCreateUser()), "create_user", queryDTO.getCreateUser());

        if (queryDTO.getStartTime() != null && queryDTO.getEndTime() != null) {
            queryWrapper.between("create_time", queryDTO.getStartTime(), queryDTO.getEndTime());
        }
        queryWrapper.orderByDesc("create_time");

        Page<PluginResource> pluginPage = this.page(page, queryWrapper);

        Page<PluginResourceVO> resultVOPage = new Page<>();
        BeanUtils.copyProperties(pluginPage, resultVOPage, "records");

        List<PluginResource> records = pluginPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return resultVOPage;
        }

        List<String> pluginIds = records.stream().map(PluginResource::getId).collect(Collectors.toList());

        List<PluginResourceQueryDTO> totalUsages = new ArrayList<>();

        // 数据 in 分批处理
        final int batchSize = 500;
        if (!pluginIds.isEmpty()) {
            for (int i = 0; i < pluginIds.size(); i += batchSize) {
                List<String> subList = pluginIds.subList(i, Math.min(i + batchSize, pluginIds.size()));

                if (!subList.isEmpty()) {
                    List<PluginResourceQueryDTO> batchUsages = pluginDatasyncService.getBaseMapper().selectUsageByPluginIds(subList);
                    totalUsages.addAll(batchUsages);
                }
            }
        }

        Map<String, List<String>> usageMap = totalUsages.stream()
                .collect(Collectors.groupingBy(
                        PluginResourceQueryDTO::getPluginId,
                        Collectors.mapping(PluginResourceQueryDTO::getIntgName, Collectors.toList())
                ));

        List<PluginResourceVO> voRecords = records.stream().map(plugin -> {
            PluginResourceVO vo = new PluginResourceVO();
            BeanUtils.copyProperties(plugin, vo);

            List<String> usedByTasks = usageMap.get(plugin.getId());
            if (!CollectionUtils.isEmpty(usedByTasks)) {
                List<String> distinctTasks = usedByTasks.stream().distinct().collect(Collectors.toList());
                vo.setUsedByTasks(distinctTasks);
                vo.setUsageCount(distinctTasks.size());
            } else {
                vo.setUsedByTasks(Collections.emptyList());
                vo.setUsageCount(0);
            }
            return vo;
        }).collect(Collectors.toList());

        resultVOPage.setRecords(voRecords);
        return resultVOPage;
    }


    /**
     * 2. 新增插件
     *
     * @param file     上传的 JAR 文件
     * @param fileName 插件名称
     * @param remark   插件备注
     * @return 保存后的 PluginResource 对象
     * @throws IOException 读取文件时可能抛出 IO 异常
     */
    public PluginResource addPlugin(MultipartFile file, String fileName, String remark) throws IOException {
        // 重复性校验
        QueryWrapper<PluginResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", fileName);

        // 2. 执行查询，获取匹配的记录数
        long count = this.count(queryWrapper);

        // 3. 如果记录数大于0，说明名称已存在，立即抛出异常并终止操作
        if (count > 0) {
            throw new IntegratorException("插件名称 '" + fileName + "' 已存在，请使用其他名称。");
        }

        // --- 文件存储 ---
        String filePath = fileServerRootPath;
        logger.info("文件存储路径: {}，文件存储名称：{}", filePath, fileName);
        String fullStorePath = fileClientService.uploadFile(file, fileName, filePath);

        // 从 JAR 文件中解析 MANIFEST.MF
        Map<String, String> manifestProperties = parseManifest(file.getInputStream());

        PluginResource pluginResource = new PluginResource();
        pluginResource.setId(SnowflakeGenerator.getNextId().toString());
        pluginResource.setName(fileName);
        pluginResource.setRemark(remark);
        pluginResource.setCreateUser(UserContextHolder.getUserContext().getUsername());
        pluginResource.setCreateTime(new Date());
        pluginResource.setUpdateUser(UserContextHolder.getUserContext().getUsername());
        pluginResource.setUpdateTime(new Date());
        pluginResource.setPath(fullStorePath);
        pluginResource.setBytes(Long.valueOf(file.getSize()).intValue());

        // 将 manifest 属性转换为 JSON 字符串并存入 properties 字段
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String propertiesJson = objectMapper.writeValueAsString(manifestProperties);
            pluginResource.setProperties(propertiesJson);
        } catch (Exception e) {
            logger.error("序列化 manifest 属性到 JSON 失败", e);
            throw new IntegratorException("处理插件属性失败，该插件不包含需要的META-INFO信息。");
        }
        // 必要性检查 插件合法性
        if (!StringUtils.hasText(pluginResource.getProperties())) {
            logger.error("无法获取 META-INFO 信息，该插件不符合 SDK 使用规范:{}", pluginResource.getProperties());
            throw new IntegratorException("无法获取 META-INFO 信息，该插件不符合 SDK 使用规范。");
        }
        this.save(pluginResource);
        return pluginResource;
    }


    /**
     * 3. 修改插件备注
     *
     * @param id     要更新的插件 ID
     * @param remark 新的备注信息
     * @return 如果成功返回 true，否则返回 false
     */
    public boolean updatePluginRemark(String id, String remark) {
        PluginResource pluginResource = new PluginResource();
        pluginResource.setId(id);
        pluginResource.setRemark(remark);
        pluginResource.setUpdateUser(UserContextHolder.getUserContext().getUsername());
        pluginResource.setUpdateTime(new Date());
        return this.updateById(pluginResource);
    }

    /**
     * 4. 删除插件 (以及关联的文件)
     *
     * @param id 要删除的插件 ID
     * @return 如果成功返回 true，否则返回 false
     */
    public boolean deletePlugin(String id) {
        PluginResource pluginResource = this.getPluginById(id);
        QueryWrapper<Datasync> usageQuery = new QueryWrapper<>();
        // 查找在源插件ID或目标插件ID中使用了该插件ID的记录
        usageQuery.and(qw -> qw.eq("orgin_plugin_id", id).or().eq("dest_plugin_id", id));
        usageQuery.eq("has_delete", YNEnum.NO.getIntCharCode());
        List<Datasync> usages = pluginDatasyncService.list(usageQuery);
        if (!usages.isEmpty()) {
            // 如果被引用，收集引用者信息并抛出业务异常
            String usingTasks = usages.stream()
                    .map(Datasync::getIntgName)
                    .distinct()
                    .collect(Collectors.joining(", "));

            throw new BusinessException("删除失败：插件正被同步任务 [" + usingTasks + "] 使用，无法删除。");
        }

        if (pluginResource != null) {
            logger.info("正在从路径删除文件: {}", pluginResource.getPath());
            boolean b = fileClientService.deleteFile(pluginResource.getPath());
            if (!b) {
                logger.error("删除文件失败：{}", pluginResource.getName());
                throw new IntegratorException("删除文件失败:" + pluginResource.getName());
            }
        }
        return this.removeById(id);
    }

    /**
     * 5. 根据 ID 获取插件详情
     *
     * @param id 插件 ID
     * @return PluginResource 对象，如果未找到则返回 null
     */
    public PluginResource getPluginById(String id) {
        return this.getById(id);
    }

    /**
     * 从 JAR 输入流中解析 MANIFEST.MF 文件的辅助方法
     */
    private Map<String, String> parseManifest(InputStream inputStream) throws IOException {
        Map<String, String> properties = new HashMap<>();
        try (JarInputStream jarStream = new JarInputStream(inputStream)) {
            Manifest manifest = jarStream.getManifest();
            if (manifest != null) {
                java.util.jar.Attributes mainAttributes = manifest.getMainAttributes();
                // 按要求提取特定属性
                String readers = mainAttributes.getValue("Implementation-Readers");
                String writers = mainAttributes.getValue("Implementation-Writers");

                if (readers != null) {
                    properties.put("readers", readers);
                }
                if (writers != null) {
                    properties.put("writers", writers);
                }
            } else {
                logger.warn("上传的 JAR 文件中未找到 MANIFEST.MF 文件。");
            }
        }
        return properties;
    }

    /**
     * 获取文件服务的相关属性
     *
     * @return 文件服务属性
     */
    public Map<String, Object> getActiveFilesSystemConfig() {
        String type = baseProperties.getType().name().toLowerCase();
        String prefix = FS_CLIENT_PREFIX + type;
        Binder binder = Binder.get(environment);
        Map<String, Object> activeProperties = binder.bind(prefix, Bindable.mapOf(String.class, Object.class))
                .orElse(Collections.emptyMap());
        // 构建所需要的属性
        Map<String, Object> result = new HashMap<>();
        result.put("fsclient_type", type.toUpperCase());
        result.put("fsclient_props", activeProperties);
        result.put("fsclient_filter", baseProperties.getFilter());
        result.put("fsclient_crypto", baseProperties.getCrypto());
        return result;
    }


    /**
     * 根据ID下载插件文件
     *
     * @param id       插件ID
     * @param response HttpServletResponse 用于将文件流写回客户端
     * @throws UnsupportedEncodingException IO异常
     */
    public void downloadPlugin(String id, HttpServletResponse response) throws UnsupportedEncodingException {
        PluginResource pluginResource = this.getPluginById(id);
        if (pluginResource == null) {
            logger.error("下载失败：未找到ID为 {} 的插件。", id);
            throw new IntegratorException("未找到指定的插件，无法下载。");
        }

        String fileName = pluginResource.getName();
        // 确保文件名以 .jar 结尾
        if (!fileName.toLowerCase().endsWith(".jar")) {
            fileName += ".jar";
        }

        // 2. 设置HTTP响应头
        // 设置内容类型为二进制流，适用于所有文件类型
        response.setContentType("application/octet-stream");
        // 对文件名进行URL编码，以防止中文乱码或特殊字符问题
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()).replace("+", "%20");
        // 设置Content-Disposition头，告诉浏览器这是一个需要下载的附件
        response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
        // 设置文件大小，浏览器可以显示下载进度
        if (pluginResource.getBytes() != null) {
            response.setContentLength(pluginResource.getBytes());
        }


        // 3. 获取响应的输出流，并调用文件服务进行下载
        try (OutputStream outputStream = response.getOutputStream()) {
            logger.info("准备从路径 {} 下载文件: {}", pluginResource.getPath(), fileName);
            fileClientService.downloadFile(pluginResource.getPath(), outputStream);
            outputStream.flush(); // 确保所有数据都被发送到客户端
        } catch (Exception e) {
            logger.error("下载插件 {} 失败", fileName, e);
            // 向上抛出异常，由Controller的全局异常处理器或Spring Boot默认机制处理
            throw new IntegratorException("文件下载失败: " + e.getMessage());
        }
    }

    public List<PluginResource> listPluginResources() {
        return this.list(new QueryWrapper<PluginResource>()
                .orderByDesc("create_time")
        );
    }
}
