package com.xmcares.platform.admin.dataservice.dataset.web;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.xmcares.framework.commons.domain.Page;
import com.xmcares.framework.commons.domain.Pagination;
import com.xmcares.framework.sharing.domain.model.Service;
import com.xmcares.framework.sharing.reference.query.ServiceComplexQuery;
import com.xmcares.framework.sharing.reference.resource.AppResource;
import com.xmcares.framework.sharing.repository.AppRepository;
import com.xmcares.framework.sharing.repository.ServiceRepository;
import com.xmcares.platform.admin.metadata.common.model.Result;
import com.xmcares.platform.admin.dataservice.dataset.service.DatasetPublishedService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 服务控制器
 * 
 * <AUTHOR> <<EMAIL>>
 * @version 1.0
 * @since 2025-05-20
 */
@Api(value = "服务控制器")
@Validated
@RestController
@RequestMapping(value = "${xbdp.api.dataservice:/dataservice}/dataset/service", produces = "application/json")
public class ServiceController {

    @Autowired
    ServiceRepository serviceRepository;

    @Autowired
    AppRepository appRepository;

    @Autowired
    DatasetPublishedService datasetPublishedService;

    /**
     * 获取服务列表
     */
    @ApiOperation("获取服务列表")
    @GetMapping("/list")
    @ResponseBody
    public Page<Service> getPage(
            @RequestParam(name = "page", defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Pagination pagination = new Pagination();
        pagination.setPageNo(page);
        pagination.setPageSize(pageSize);
        ServiceComplexQuery complexQuery = new ServiceComplexQuery();

        return serviceRepository.queryServiceResource(complexQuery, pagination);
    }

    @ApiOperation("服务已授权列表")
    @GetMapping("/apps")
    @ResponseBody
    public Result<List<AppResource>> getAppList(@RequestParam(name = "serviceId") String serviceId) {
        boolean success = false;
        String message = "";
        List<AppResource> appList = null;
        try {
            appList = appRepository.getAppResourceByRelService(serviceId);
            success = true;

        } catch (Exception e) {
            success = false;
            message = e.getMessage();
        }
        return new Result<>(success, message, appList);
    }

    @ApiOperation("服务授权")
    @GetMapping("/auth")
    @ResponseBody
    public Result<Boolean> auth(
            @RequestParam(name = "appIdList") List<String> appIdList,
            @RequestParam(name = "serviceIdList") List<String> serviceIdList) {

        boolean success = false;
        String message = "";
        try {
            if (appIdList != null && !appIdList.isEmpty()) {
                for (String appId : appIdList) {
                    Integer count = appRepository.resetAppService(appId, serviceIdList);
                    if (count > 0) {
                        success = true;
                    } else {
                        success = false;
                        message = "授权失败";
                        break;
                    }
                }
            }
        } catch (Exception e) {
            success = false;
            message = e.getMessage();
        }
        return new Result<>(success, message, null);
    }

    /**
     * 修改
     * body:
     * {"id":"45078026390200320","category":"mysql服务测试（设计器模式20250604）","name":"mysqlService202506057","uri":"/api/mysqlService20250605","status":"1"}
     */
    @ApiOperation("修改服务")
    @PostMapping("/update")
    @ResponseBody
    public Result<Boolean> updateService(@RequestBody Service service) {
        boolean success = false;
        String message = "";
        try {
            Service service1 = serviceRepository.getService(service.getId());
            if (service1 != null) {
                if (datasetPublishedService.checkServiceId(service.getId())) {
                    if (!service1.getName().equals(service.getName())) {
                        message = "数据集服务API不能变更";
                        return new Result<>(false, message, null);
                    }
                }
                service1.setName(service.getName());
                service1.setCategory(service.getCategory());
                service1.setUri(service.getUri());
                service1.setStatus(service.getStatus());
                serviceRepository.updateService(service1);
                success = true;
            } else {
                message = "服务不存在";
            }
        } catch (Exception e) {
            success = false;
            message = e.getMessage();
        }
        return new Result<>(success, message, null);
    }

    /**
     * 删除服务
     */
    @ApiOperation("删除服务")
    @GetMapping("/delete")
    @ResponseBody
    public Result<Boolean> deleteService(@RequestParam(name = "id") String id) {
        boolean success = false;
        String message = "";
        try {
            Service service = serviceRepository.getService(id);
            if (service != null) {
                if (datasetPublishedService.checkServiceId(service.getId())) {
                    message = "请从数据集服务化管理中删除";
                    return new Result<>(false, message, null);
                }
                serviceRepository.deleteService(id);
                success = true;
            } else {
                message = "服务不存在";
            }
        } catch (Exception e) {
            success = false;
            message = e.getMessage();
        }
        return new Result<>(success, message, null);
    }
}
